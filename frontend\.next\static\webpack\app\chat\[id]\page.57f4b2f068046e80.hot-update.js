"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/file-explorer.tsx":
/*!**************************************!*\
  !*** ./components/file-explorer.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileExplorer: function() { return /* binding */ FileExplorer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _custom_file_tree__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./custom-file-tree */ \"(app-pages-browser)/./components/custom-file-tree.tsx\");\n/* harmony import */ var _barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Download,FileText,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ FileExplorer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction FileExplorer(param) {\n    let { version } = param;\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (version) {\n            loadFiles();\n        } else {\n            setFiles([]);\n            setTreeData([]);\n            setSelectedFile(null);\n        }\n    }, [\n        version\n    ]);\n    const loadFiles = async ()=>{\n        if (!version) return;\n        setIsLoading(true);\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getVersionPreview(version.chat_id, version.id);\n            setFiles(data.files || []);\n            buildFileTree(data.files || []);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load files\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const buildFileTree = (files)=>{\n        const nodeMap = new Map();\n        const rootNodes = [];\n        // Sort files by path\n        const sortedFiles = files.sort((a, b)=>a.path.localeCompare(b.path));\n        for (const file of sortedFiles){\n            const pathParts = file.path.split(\"/\").filter(Boolean);\n            let currentPath = \"\";\n            for(let i = 0; i < pathParts.length; i++){\n                const part = pathParts[i];\n                const parentPath = currentPath;\n                currentPath = currentPath ? \"\".concat(currentPath, \"/\").concat(part) : part;\n                const isFile = i === pathParts.length - 1;\n                if (!nodeMap.has(currentPath)) {\n                    const newNode = {\n                        id: currentPath,\n                        name: part,\n                        type: isFile ? \"file\" : \"folder\",\n                        path: currentPath,\n                        children: isFile ? undefined : [],\n                        file: isFile ? file : undefined\n                    };\n                    if (parentPath === \"\") {\n                        // Root level\n                        rootNodes.push(newNode);\n                    } else {\n                        const parentNode = nodeMap.get(parentPath);\n                        if (parentNode && parentNode.children) {\n                            parentNode.children.push(newNode);\n                        }\n                    }\n                    nodeMap.set(currentPath, newNode);\n                }\n            }\n        }\n        setTreeData(rootNodes);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n                return \"javascript\";\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n                return \"javascript\";\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"java\":\n                return \"java\";\n            case \"cpp\":\n            case \"c\":\n                return \"cpp\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    if (!version) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center text-muted-foreground\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No version selected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-[#0d1117]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 border-b border-[#21262d]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-sm text-[#f0f6fc] flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Folder, {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"app\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc] rounded\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 16 16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-2\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-6 h-6 animate-spin text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, this) : !treeData || treeData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-[#7d8590]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"No files yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_custom_file_tree__WEBPACK_IMPORTED_MODULE_5__.CustomFileTree, {\n                        data: treeData,\n                        onFileSelect: handleFileSelect,\n                        selectedPath: selectedFile === null || selectedFile === void 0 ? void 0 : selectedFile.path\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col bg-[#1e1e1e]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-[#333] bg-[#2d2d30] flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 text-[#519aba]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm text-[#cccccc]\",\n                                        children: selectedFile ? selectedFile.path : \"Select a file\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-1 hover:bg-[#3c3c3c] rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-[#cccccc]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            height: \"100%\",\n                            language: getFileLanguage(selectedFile.path),\n                            value: selectedFile.content || \"// No content\",\n                            theme: \"vs-dark\",\n                            options: {\n                                readOnly: true,\n                                minimap: {\n                                    enabled: false\n                                },\n                                scrollBeyondLastLine: false,\n                                fontSize: 14,\n                                fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n                                lineNumbers: \"on\",\n                                glyphMargin: false,\n                                folding: true,\n                                lineDecorationsWidth: 0,\n                                lineNumbersMinChars: 3,\n                                renderLineHighlight: \"line\",\n                                selectOnLineNumbers: true,\n                                wordWrap: \"on\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center text-[#858585]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Download_FileText_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Select a file to view its content\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(FileExplorer, \"rWgTg2L5GgEIVJeU5qp2Tp8/9d0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = FileExplorer;\nvar _c;\n$RefreshReg$(_c, \"FileExplorer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/file-explorer.tsx\n"));

/***/ })

});