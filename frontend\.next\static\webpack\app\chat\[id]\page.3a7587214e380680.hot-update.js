"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(app-pages-browser)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(app-pages-browser)/./components/website-preview.tsx\");\n/* harmony import */ var react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-resizable-panels */ \"(app-pages-browser)/./node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage(param) {\n    let { params } = param;\n    _s();\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    if (!user) {\n        return null;\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (currentVersion === null || currentVersion === void 0 ? void 0 : currentVersion.id) || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id,\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelGroup, {\n                            direction: \"horizontal\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 35,\n                                    minSize: 25,\n                                    maxSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                            version: currentVersion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                            version: currentVersion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelResizeHandle, {\n                                    className: \"w-1 bg-[#21262d] hover:bg-[#0969da] transition-colors cursor-col-resize\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 65,\n                                    minSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: [\n                                            !showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                                version: currentVersion\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this),\n                                            showPreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                                version: currentVersion\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"/Fklln37zvFUqBZUDLDkK+6w5yI=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chat/[id]/page.tsx\n"));

/***/ })

});