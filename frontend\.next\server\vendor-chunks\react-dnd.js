"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dnd";
exports.ids = ["vendor-chunks/react-dnd"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/core/DndContext.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/core/DndContext.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DndContext: () => (/* binding */ DndContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Create the React Context\n */ var DndContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    dragDropManager: undefined\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2NvcmUvRG5kQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDdEM7O0NBRUMsR0FFTSxJQUFJQywyQkFBYUQsb0RBQWFBLENBQUM7SUFDcENFLGlCQUFpQkM7QUFDbkIsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQvZGlzdC9lc20vY29yZS9EbmRDb250ZXh0LmpzP2MyMmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0Jztcbi8qKlxuICogQ3JlYXRlIHRoZSBSZWFjdCBDb250ZXh0XG4gKi9cblxuZXhwb3J0IHZhciBEbmRDb250ZXh0ID0gY3JlYXRlQ29udGV4dCh7XG4gIGRyYWdEcm9wTWFuYWdlcjogdW5kZWZpbmVkXG59KTsiXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsIkRuZENvbnRleHQiLCJkcmFnRHJvcE1hbmFnZXIiLCJ1bmRlZmluZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/core/DndContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/core/DndProvider.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/core/DndProvider.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DndProvider: () => (/* binding */ DndProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dnd_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dnd-core */ \"(ssr)/./node_modules/dnd-core/dist/esm/createDragDropManager.js\");\n/* harmony import */ var _DndContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DndContext */ \"(ssr)/./node_modules/react-dnd/dist/esm/core/DndContext.js\");\nvar _excluded = [\n    \"children\"\n];\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n\n\n\n\nvar refCount = 0;\nvar INSTANCE_SYM = Symbol.for(\"__REACT_DND_CONTEXT_INSTANCE__\");\n/**\n * A React component that provides the React-DnD context\n */ var DndProvider = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function DndProvider(_ref) {\n    var children = _ref.children, props = _objectWithoutProperties(_ref, _excluded);\n    var _getDndContextValue = getDndContextValue(props), _getDndContextValue2 = _slicedToArray(_getDndContextValue, 2), manager = _getDndContextValue2[0], isGlobalInstance = _getDndContextValue2[1]; // memoized from props\n    /**\n   * If the global context was used to store the DND context\n   * then where theres no more references to it we should\n   * clean it up to avoid memory leaks\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (isGlobalInstance) {\n            var context = getGlobalContext();\n            ++refCount;\n            return function() {\n                if (--refCount === 0) {\n                    context[INSTANCE_SYM] = null;\n                }\n            };\n        }\n    }, []);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_DndContext__WEBPACK_IMPORTED_MODULE_2__.DndContext.Provider, Object.assign({\n        value: manager\n    }, {\n        children: children\n    }), void 0);\n});\nfunction getDndContextValue(props) {\n    if (\"manager\" in props) {\n        var _manager = {\n            dragDropManager: props.manager\n        };\n        return [\n            _manager,\n            false\n        ];\n    }\n    var manager = createSingletonDndContext(props.backend, props.context, props.options, props.debugMode);\n    var isGlobalInstance = !props.context;\n    return [\n        manager,\n        isGlobalInstance\n    ];\n}\nfunction createSingletonDndContext(backend) {\n    var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : getGlobalContext();\n    var options = arguments.length > 2 ? arguments[2] : undefined;\n    var debugMode = arguments.length > 3 ? arguments[3] : undefined;\n    var ctx = context;\n    if (!ctx[INSTANCE_SYM]) {\n        ctx[INSTANCE_SYM] = {\n            dragDropManager: (0,dnd_core__WEBPACK_IMPORTED_MODULE_3__.createDragDropManager)(backend, context, options, debugMode)\n        };\n    }\n    return ctx[INSTANCE_SYM];\n}\nfunction getGlobalContext() {\n    return typeof global !== \"undefined\" ? global : window;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/core/DndProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollectedProps.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useCollectedProps.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCollectedProps: () => (/* binding */ useCollectedProps)\n/* harmony export */ });\n/* harmony import */ var _useMonitorOutput__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useMonitorOutput */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useMonitorOutput.js\");\n\nfunction useCollectedProps(collector, monitor, connector) {\n    return (0,_useMonitorOutput__WEBPACK_IMPORTED_MODULE_0__.useMonitorOutput)(monitor, collector || function() {\n        return {};\n    }, function() {\n        return connector.reconnect();\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZUNvbGxlY3RlZFByb3BzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEO0FBQy9DLFNBQVNDLGtCQUFrQkMsU0FBUyxFQUFFQyxPQUFPLEVBQUVDLFNBQVM7SUFDN0QsT0FBT0osbUVBQWdCQSxDQUFDRyxTQUFTRCxhQUFhO1FBQzVDLE9BQU8sQ0FBQztJQUNWLEdBQUc7UUFDRCxPQUFPRSxVQUFVQyxTQUFTO0lBQzVCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZUNvbGxlY3RlZFByb3BzLmpzPzM1ZjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTW9uaXRvck91dHB1dCB9IGZyb20gJy4vdXNlTW9uaXRvck91dHB1dCc7XG5leHBvcnQgZnVuY3Rpb24gdXNlQ29sbGVjdGVkUHJvcHMoY29sbGVjdG9yLCBtb25pdG9yLCBjb25uZWN0b3IpIHtcbiAgcmV0dXJuIHVzZU1vbml0b3JPdXRwdXQobW9uaXRvciwgY29sbGVjdG9yIHx8IGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4ge307XG4gIH0sIGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gY29ubmVjdG9yLnJlY29ubmVjdCgpO1xuICB9KTtcbn0iXSwibmFtZXMiOlsidXNlTW9uaXRvck91dHB1dCIsInVzZUNvbGxlY3RlZFByb3BzIiwiY29sbGVjdG9yIiwibW9uaXRvciIsImNvbm5lY3RvciIsInJlY29ubmVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollectedProps.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollector.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useCollector.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCollector: () => (/* binding */ useCollector)\n/* harmony export */ });\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fast-deep-equal */ \"(ssr)/./node_modules/fast-deep-equal/index.js\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fast_deep_equal__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\");\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\n\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */ function useCollector(monitor, collect, onUpdate) {\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function() {\n        return collect(monitor);\n    }), _useState2 = _slicedToArray(_useState, 2), collected = _useState2[0], setCollected = _useState2[1];\n    var updateCollected = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        var nextValue = collect(monitor); // This needs to be a deep-equality check because some monitor-collected values\n        // include XYCoord objects that may be equivalent, but do not have instance equality.\n        if (!fast_deep_equal__WEBPACK_IMPORTED_MODULE_0___default()(collected, nextValue)) {\n            setCollected(nextValue);\n            if (onUpdate) {\n                onUpdate();\n            }\n        }\n    }, [\n        collected,\n        monitor,\n        onUpdate\n    ]); // update the collected properties after react renders.\n    // Note that the \"Dustbin Stress Test\" fails if this is not\n    // done when the component updates\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(updateCollected);\n    return [\n        collected,\n        updateCollected\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragDropManager: () => (/* binding */ useDragDropManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ \"(ssr)/./node_modules/react-dnd/dist/esm/core/DndContext.js\");\n\n\n\n/**\n * A hook to retrieve the DragDropManager from Context\n */ function useDragDropManager() {\n    var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_core__WEBPACK_IMPORTED_MODULE_2__.DndContext), dragDropManager = _useContext.dragDropManager;\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_1__.invariant)(dragDropManager != null, \"Expected drag drop context\");\n    return dragDropManager;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWdEcm9wTWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtQztBQUNjO0FBQ1o7QUFDckM7O0NBRUMsR0FFTSxTQUFTRztJQUNkLElBQUlDLGNBQWNKLGlEQUFVQSxDQUFDRSw2Q0FBVUEsR0FDbkNHLGtCQUFrQkQsWUFBWUMsZUFBZTtJQUVqREosK0RBQVNBLENBQUNJLG1CQUFtQixNQUFNO0lBQ25DLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWdEcm9wTWFuYWdlci5qcz9lY2ZiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICdAcmVhY3QtZG5kL2ludmFyaWFudCc7XG5pbXBvcnQgeyBEbmRDb250ZXh0IH0gZnJvbSAnLi4vY29yZSc7XG4vKipcbiAqIEEgaG9vayB0byByZXRyaWV2ZSB0aGUgRHJhZ0Ryb3BNYW5hZ2VyIGZyb20gQ29udGV4dFxuICovXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VEcmFnRHJvcE1hbmFnZXIoKSB7XG4gIHZhciBfdXNlQ29udGV4dCA9IHVzZUNvbnRleHQoRG5kQ29udGV4dCksXG4gICAgICBkcmFnRHJvcE1hbmFnZXIgPSBfdXNlQ29udGV4dC5kcmFnRHJvcE1hbmFnZXI7XG5cbiAgaW52YXJpYW50KGRyYWdEcm9wTWFuYWdlciAhPSBudWxsLCAnRXhwZWN0ZWQgZHJhZyBkcm9wIGNvbnRleHQnKTtcbiAgcmV0dXJuIGRyYWdEcm9wTWFuYWdlcjtcbn0iXSwibmFtZXMiOlsidXNlQ29udGV4dCIsImludmFyaWFudCIsIkRuZENvbnRleHQiLCJ1c2VEcmFnRHJvcE1hbmFnZXIiLCJfdXNlQ29udGV4dCIsImRyYWdEcm9wTWFuYWdlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragLayer.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDragLayer.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragLayer: () => (/* binding */ useDragLayer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n/* harmony import */ var _useCollector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useCollector */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollector.js\");\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\n\n/**\n * useDragLayer Hook\n * @param collector The property collector\n */ function useDragLayer(collect) {\n    var dragDropManager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    var monitor = dragDropManager.getMonitor();\n    var _useCollector = (0,_useCollector__WEBPACK_IMPORTED_MODULE_2__.useCollector)(monitor, collect), _useCollector2 = _slicedToArray(_useCollector, 2), collected = _useCollector2[0], updateCollected = _useCollector2[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        return monitor.subscribeToOffsetChange(updateCollected);\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        return monitor.subscribeToStateChange(updateCollected);\n    });\n    return collected;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragLayer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/DragSourceImpl.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/DragSourceImpl.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragSourceImpl: () => (/* binding */ DragSourceImpl)\n/* harmony export */ });\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n        _typeof = function _typeof(obj) {\n            return typeof obj;\n        };\n    } else {\n        _typeof = function _typeof(obj) {\n            return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n        };\n    }\n    return _typeof(obj);\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nvar DragSourceImpl = /*#__PURE__*/ function() {\n    function DragSourceImpl(spec, monitor, connector) {\n        _classCallCheck(this, DragSourceImpl);\n        _defineProperty(this, \"spec\", void 0);\n        _defineProperty(this, \"monitor\", void 0);\n        _defineProperty(this, \"connector\", void 0);\n        this.spec = spec;\n        this.monitor = monitor;\n        this.connector = connector;\n    }\n    _createClass(DragSourceImpl, [\n        {\n            key: \"beginDrag\",\n            value: function beginDrag() {\n                var _result;\n                var spec = this.spec;\n                var monitor = this.monitor;\n                var result = null;\n                if (_typeof(spec.item) === \"object\") {\n                    result = spec.item;\n                } else if (typeof spec.item === \"function\") {\n                    result = spec.item(monitor);\n                } else {\n                    result = {};\n                }\n                return (_result = result) !== null && _result !== void 0 ? _result : null;\n            }\n        },\n        {\n            key: \"canDrag\",\n            value: function canDrag() {\n                var spec = this.spec;\n                var monitor = this.monitor;\n                if (typeof spec.canDrag === \"boolean\") {\n                    return spec.canDrag;\n                } else if (typeof spec.canDrag === \"function\") {\n                    return spec.canDrag(monitor);\n                } else {\n                    return true;\n                }\n            }\n        },\n        {\n            key: \"isDragging\",\n            value: function isDragging(globalMonitor, target) {\n                var spec = this.spec;\n                var monitor = this.monitor;\n                var isDragging = spec.isDragging;\n                return isDragging ? isDragging(monitor) : target === globalMonitor.getSourceId();\n            }\n        },\n        {\n            key: \"endDrag\",\n            value: function endDrag() {\n                var spec = this.spec;\n                var monitor = this.monitor;\n                var connector = this.connector;\n                var end = spec.end;\n                if (end) {\n                    end(monitor.getItem(), monitor);\n                }\n                connector.reconnect();\n            }\n        }\n    ]);\n    return DragSourceImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/DragSourceImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/connectors.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/connectors.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnectDragPreview: () => (/* binding */ useConnectDragPreview),\n/* harmony export */   useConnectDragSource: () => (/* binding */ useConnectDragSource)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useConnectDragSource(connector) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return connector.hooks.dragSource();\n    }, [\n        connector\n    ]);\n}\nfunction useConnectDragPreview(connector) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return connector.hooks.dragPreview();\n    }, [\n        connector\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWcvY29ubmVjdG9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdDO0FBQ3pCLFNBQVNDLHFCQUFxQkMsU0FBUztJQUM1QyxPQUFPRiw4Q0FBT0EsQ0FBQztRQUNiLE9BQU9FLFVBQVVDLEtBQUssQ0FBQ0MsVUFBVTtJQUNuQyxHQUFHO1FBQUNGO0tBQVU7QUFDaEI7QUFDTyxTQUFTRyxzQkFBc0JILFNBQVM7SUFDN0MsT0FBT0YsOENBQU9BLENBQUM7UUFDYixPQUFPRSxVQUFVQyxLQUFLLENBQUNHLFdBQVc7SUFDcEMsR0FBRztRQUFDSjtLQUFVO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC9kaXN0L2VzbS9ob29rcy91c2VEcmFnL2Nvbm5lY3RvcnMuanM/MjVjYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZUNvbm5lY3REcmFnU291cmNlKGNvbm5lY3Rvcikge1xuICByZXR1cm4gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGNvbm5lY3Rvci5ob29rcy5kcmFnU291cmNlKCk7XG4gIH0sIFtjb25uZWN0b3JdKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiB1c2VDb25uZWN0RHJhZ1ByZXZpZXcoY29ubmVjdG9yKSB7XG4gIHJldHVybiB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gY29ubmVjdG9yLmhvb2tzLmRyYWdQcmV2aWV3KCk7XG4gIH0sIFtjb25uZWN0b3JdKTtcbn0iXSwibmFtZXMiOlsidXNlTWVtbyIsInVzZUNvbm5lY3REcmFnU291cmNlIiwiY29ubmVjdG9yIiwiaG9va3MiLCJkcmFnU291cmNlIiwidXNlQ29ubmVjdERyYWdQcmV2aWV3IiwiZHJhZ1ByZXZpZXciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/connectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDrag.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/useDrag.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDrag: () => (/* binding */ useDrag)\n/* harmony export */ });\n/* harmony import */ var _useRegisteredDragSource__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useRegisteredDragSource */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useRegisteredDragSource.js\");\n/* harmony import */ var _useOptionalFactory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useOptionalFactory */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useOptionalFactory.js\");\n/* harmony import */ var _useDragSourceMonitor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDragSourceMonitor */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceMonitor.js\");\n/* harmony import */ var _useDragSourceConnector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useDragSourceConnector */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceConnector.js\");\n/* harmony import */ var _useCollectedProps__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../useCollectedProps */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollectedProps.js\");\n/* harmony import */ var _connectors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./connectors */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/connectors.js\");\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n\n\n\n\n\n\n\n/**\n * useDragSource hook\n * @param sourceSpec The drag source specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */ function useDrag(specArg, deps) {\n    var spec = (0,_useOptionalFactory__WEBPACK_IMPORTED_MODULE_1__.useOptionalFactory)(specArg, deps);\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!spec.begin, \"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)\");\n    var monitor = (0,_useDragSourceMonitor__WEBPACK_IMPORTED_MODULE_2__.useDragSourceMonitor)();\n    var connector = (0,_useDragSourceConnector__WEBPACK_IMPORTED_MODULE_3__.useDragSourceConnector)(spec.options, spec.previewOptions);\n    (0,_useRegisteredDragSource__WEBPACK_IMPORTED_MODULE_4__.useRegisteredDragSource)(spec, monitor, connector);\n    return [\n        (0,_useCollectedProps__WEBPACK_IMPORTED_MODULE_5__.useCollectedProps)(spec.collect, monitor, connector),\n        (0,_connectors__WEBPACK_IMPORTED_MODULE_6__.useConnectDragSource)(connector),\n        (0,_connectors__WEBPACK_IMPORTED_MODULE_6__.useConnectDragPreview)(connector)\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSource.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSource.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragSource: () => (/* binding */ useDragSource)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _DragSourceImpl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DragSourceImpl */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/DragSourceImpl.js\");\n\n\nfunction useDragSource(spec, monitor, connector) {\n    var handler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return new _DragSourceImpl__WEBPACK_IMPORTED_MODULE_1__.DragSourceImpl(spec, monitor, connector);\n    }, [\n        monitor,\n        connector\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        handler.spec = spec;\n    }, [\n        spec\n    ]);\n    return handler;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWcvdXNlRHJhZ1NvdXJjZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTJDO0FBQ087QUFDM0MsU0FBU0csY0FBY0MsSUFBSSxFQUFFQyxPQUFPLEVBQUVDLFNBQVM7SUFDcEQsSUFBSUMsVUFBVU4sOENBQU9BLENBQUM7UUFDcEIsT0FBTyxJQUFJQywyREFBY0EsQ0FBQ0UsTUFBTUMsU0FBU0M7SUFDM0MsR0FBRztRQUFDRDtRQUFTQztLQUFVO0lBQ3ZCTixnREFBU0EsQ0FBQztRQUNSTyxRQUFRSCxJQUFJLEdBQUdBO0lBQ2pCLEdBQUc7UUFBQ0E7S0FBSztJQUNULE9BQU9HO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWcvdXNlRHJhZ1NvdXJjZS5qcz9kZmFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IERyYWdTb3VyY2VJbXBsIH0gZnJvbSAnLi9EcmFnU291cmNlSW1wbCc7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJhZ1NvdXJjZShzcGVjLCBtb25pdG9yLCBjb25uZWN0b3IpIHtcbiAgdmFyIGhhbmRsZXIgPSB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gbmV3IERyYWdTb3VyY2VJbXBsKHNwZWMsIG1vbml0b3IsIGNvbm5lY3Rvcik7XG4gIH0sIFttb25pdG9yLCBjb25uZWN0b3JdKTtcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBoYW5kbGVyLnNwZWMgPSBzcGVjO1xuICB9LCBbc3BlY10pO1xuICByZXR1cm4gaGFuZGxlcjtcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlTWVtbyIsIkRyYWdTb3VyY2VJbXBsIiwidXNlRHJhZ1NvdXJjZSIsInNwZWMiLCJtb25pdG9yIiwiY29ubmVjdG9yIiwiaGFuZGxlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceConnector.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceConnector.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragSourceConnector: () => (/* binding */ useDragSourceConnector)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _internals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/SourceConnector.js\");\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\");\n\n\n\n\nfunction useDragSourceConnector(dragSourceOptions, dragPreviewOptions) {\n    var manager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    var connector = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return new _internals__WEBPACK_IMPORTED_MODULE_2__.SourceConnector(manager.getBackend());\n    }, [\n        manager\n    ]);\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function() {\n        connector.dragSourceOptions = dragSourceOptions || null;\n        connector.reconnect();\n        return function() {\n            return connector.disconnectDragSource();\n        };\n    }, [\n        connector,\n        dragSourceOptions\n    ]);\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function() {\n        connector.dragPreviewOptions = dragPreviewOptions || null;\n        connector.reconnect();\n        return function() {\n            return connector.disconnectDragPreview();\n        };\n    }, [\n        connector,\n        dragPreviewOptions\n    ]);\n    return connector;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceMonitor.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceMonitor.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragSourceMonitor: () => (/* binding */ useDragSourceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _internals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/DragSourceMonitorImpl.js\");\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n\n\n\nfunction useDragSourceMonitor() {\n    var manager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return new _internals__WEBPACK_IMPORTED_MODULE_2__.DragSourceMonitorImpl(manager);\n    }, [\n        manager\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWcvdXNlRHJhZ1NvdXJjZU1vbml0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFDd0I7QUFDRztBQUNwRCxTQUFTRztJQUNkLElBQUlDLFVBQVVGLHVFQUFrQkE7SUFDaEMsT0FBT0YsOENBQU9BLENBQUM7UUFDYixPQUFPLElBQUlDLDZEQUFxQkEsQ0FBQ0c7SUFDbkMsR0FBRztRQUFDQTtLQUFRO0FBQ2QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWcvdXNlRHJhZ1NvdXJjZU1vbml0b3IuanM/Y2RmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRHJhZ1NvdXJjZU1vbml0b3JJbXBsIH0gZnJvbSAnLi4vLi4vaW50ZXJuYWxzJztcbmltcG9ydCB7IHVzZURyYWdEcm9wTWFuYWdlciB9IGZyb20gJy4uL3VzZURyYWdEcm9wTWFuYWdlcic7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJhZ1NvdXJjZU1vbml0b3IoKSB7XG4gIHZhciBtYW5hZ2VyID0gdXNlRHJhZ0Ryb3BNYW5hZ2VyKCk7XG4gIHJldHVybiB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gbmV3IERyYWdTb3VyY2VNb25pdG9ySW1wbChtYW5hZ2VyKTtcbiAgfSwgW21hbmFnZXJdKTtcbn0iXSwibmFtZXMiOlsidXNlTWVtbyIsIkRyYWdTb3VyY2VNb25pdG9ySW1wbCIsInVzZURyYWdEcm9wTWFuYWdlciIsInVzZURyYWdTb3VyY2VNb25pdG9yIiwibWFuYWdlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSourceMonitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragType.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragType.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragType: () => (/* binding */ useDragType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useDragType(spec) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        var result = spec.type;\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(result != null, \"spec.type must be defined\");\n        return result;\n    }, [\n        spec\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyYWcvdXNlRHJhZ1R5cGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDtBQUNqQjtBQUN6QixTQUFTRSxZQUFZQyxJQUFJO0lBQzlCLE9BQU9GLDhDQUFPQSxDQUFDO1FBQ2IsSUFBSUcsU0FBU0QsS0FBS0UsSUFBSTtRQUN0QkwsK0RBQVNBLENBQUNJLFVBQVUsTUFBTTtRQUMxQixPQUFPQTtJQUNULEdBQUc7UUFBQ0Q7S0FBSztBQUNYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC9kaXN0L2VzbS9ob29rcy91c2VEcmFnL3VzZURyYWdUeXBlLmpzP2Q5YjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnQHJlYWN0LWRuZC9pbnZhcmlhbnQnO1xuaW1wb3J0IHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBmdW5jdGlvbiB1c2VEcmFnVHlwZShzcGVjKSB7XG4gIHJldHVybiB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgcmVzdWx0ID0gc3BlYy50eXBlO1xuICAgIGludmFyaWFudChyZXN1bHQgIT0gbnVsbCwgJ3NwZWMudHlwZSBtdXN0IGJlIGRlZmluZWQnKTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9LCBbc3BlY10pO1xufSJdLCJuYW1lcyI6WyJpbnZhcmlhbnQiLCJ1c2VNZW1vIiwidXNlRHJhZ1R5cGUiLCJzcGVjIiwicmVzdWx0IiwidHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useRegisteredDragSource.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrag/useRegisteredDragSource.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRegisteredDragSource: () => (/* binding */ useRegisteredDragSource)\n/* harmony export */ });\n/* harmony import */ var _internals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internals */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/registration.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useDragSource__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDragSource */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragSource.js\");\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n/* harmony import */ var _useDragType__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDragType */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDragType.js\");\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\n\n\n\nfunction useRegisteredDragSource(spec, monitor, connector) {\n    var manager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_0__.useDragDropManager)();\n    var handler = (0,_useDragSource__WEBPACK_IMPORTED_MODULE_1__.useDragSource)(spec, monitor, connector);\n    var itemType = (0,_useDragType__WEBPACK_IMPORTED_MODULE_2__.useDragType)(spec);\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function registerDragSource() {\n        if (itemType != null) {\n            var _registerSource = (0,_internals__WEBPACK_IMPORTED_MODULE_4__.registerSource)(itemType, handler, manager), _registerSource2 = _slicedToArray(_registerSource, 2), handlerId = _registerSource2[0], unregister = _registerSource2[1];\n            monitor.receiveHandlerId(handlerId);\n            connector.receiveHandlerId(handlerId);\n            return unregister;\n        }\n    }, [\n        manager,\n        monitor,\n        connector,\n        handler,\n        itemType\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useRegisteredDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/DropTargetImpl.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/DropTargetImpl.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropTargetImpl: () => (/* binding */ DropTargetImpl)\n/* harmony export */ });\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nvar DropTargetImpl = /*#__PURE__*/ function() {\n    function DropTargetImpl(spec, monitor) {\n        _classCallCheck(this, DropTargetImpl);\n        _defineProperty(this, \"spec\", void 0);\n        _defineProperty(this, \"monitor\", void 0);\n        this.spec = spec;\n        this.monitor = monitor;\n    }\n    _createClass(DropTargetImpl, [\n        {\n            key: \"canDrop\",\n            value: function canDrop() {\n                var spec = this.spec;\n                var monitor = this.monitor;\n                return spec.canDrop ? spec.canDrop(monitor.getItem(), monitor) : true;\n            }\n        },\n        {\n            key: \"hover\",\n            value: function hover() {\n                var spec = this.spec;\n                var monitor = this.monitor;\n                if (spec.hover) {\n                    spec.hover(monitor.getItem(), monitor);\n                }\n            }\n        },\n        {\n            key: \"drop\",\n            value: function drop() {\n                var spec = this.spec;\n                var monitor = this.monitor;\n                if (spec.drop) {\n                    return spec.drop(monitor.getItem(), monitor);\n                }\n            }\n        }\n    ]);\n    return DropTargetImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/DropTargetImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/connectors.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/connectors.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnectDropTarget: () => (/* binding */ useConnectDropTarget)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useConnectDropTarget(connector) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return connector.hooks.dropTarget();\n    }, [\n        connector\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvY29ubmVjdG9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDekIsU0FBU0MscUJBQXFCQyxTQUFTO0lBQzVDLE9BQU9GLDhDQUFPQSxDQUFDO1FBQ2IsT0FBT0UsVUFBVUMsS0FBSyxDQUFDQyxVQUFVO0lBQ25DLEdBQUc7UUFBQ0Y7S0FBVTtBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQvZGlzdC9lc20vaG9va3MvdXNlRHJvcC9jb25uZWN0b3JzLmpzPzk5MmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmV4cG9ydCBmdW5jdGlvbiB1c2VDb25uZWN0RHJvcFRhcmdldChjb25uZWN0b3IpIHtcbiAgcmV0dXJuIHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBjb25uZWN0b3IuaG9va3MuZHJvcFRhcmdldCgpO1xuICB9LCBbY29ubmVjdG9yXSk7XG59Il0sIm5hbWVzIjpbInVzZU1lbW8iLCJ1c2VDb25uZWN0RHJvcFRhcmdldCIsImNvbm5lY3RvciIsImhvb2tzIiwiZHJvcFRhcmdldCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/connectors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useAccept.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/useAccept.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAccept: () => (/* binding */ useAccept)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * Internal utility hook to get an array-version of spec.accept.\n * The main utility here is that we aren't creating a new array on every render if a non-array spec.accept is passed in.\n * @param spec\n */ function useAccept(spec) {\n    var accept = spec.accept;\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(spec.accept != null, \"accept must be defined\");\n        return Array.isArray(accept) ? accept : [\n            accept\n        ];\n    }, [\n        accept\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlQWNjZXB0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUQ7QUFDakI7QUFDaEM7Ozs7Q0FJQyxHQUVNLFNBQVNFLFVBQVVDLElBQUk7SUFDNUIsSUFBSUMsU0FBU0QsS0FBS0MsTUFBTTtJQUN4QixPQUFPSCw4Q0FBT0EsQ0FBQztRQUNiRCwrREFBU0EsQ0FBQ0csS0FBS0MsTUFBTSxJQUFJLE1BQU07UUFDL0IsT0FBT0MsTUFBTUMsT0FBTyxDQUFDRixVQUFVQSxTQUFTO1lBQUNBO1NBQU87SUFDbEQsR0FBRztRQUFDQTtLQUFPO0FBQ2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlQWNjZXB0LmpzP2Q0NGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnQHJlYWN0LWRuZC9pbnZhcmlhbnQnO1xuaW1wb3J0IHsgdXNlTWVtbyB9IGZyb20gJ3JlYWN0Jztcbi8qKlxuICogSW50ZXJuYWwgdXRpbGl0eSBob29rIHRvIGdldCBhbiBhcnJheS12ZXJzaW9uIG9mIHNwZWMuYWNjZXB0LlxuICogVGhlIG1haW4gdXRpbGl0eSBoZXJlIGlzIHRoYXQgd2UgYXJlbid0IGNyZWF0aW5nIGEgbmV3IGFycmF5IG9uIGV2ZXJ5IHJlbmRlciBpZiBhIG5vbi1hcnJheSBzcGVjLmFjY2VwdCBpcyBwYXNzZWQgaW4uXG4gKiBAcGFyYW0gc3BlY1xuICovXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBY2NlcHQoc3BlYykge1xuICB2YXIgYWNjZXB0ID0gc3BlYy5hY2NlcHQ7XG4gIHJldHVybiB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICBpbnZhcmlhbnQoc3BlYy5hY2NlcHQgIT0gbnVsbCwgJ2FjY2VwdCBtdXN0IGJlIGRlZmluZWQnKTtcbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheShhY2NlcHQpID8gYWNjZXB0IDogW2FjY2VwdF07XG4gIH0sIFthY2NlcHRdKTtcbn0iXSwibmFtZXMiOlsiaW52YXJpYW50IiwidXNlTWVtbyIsInVzZUFjY2VwdCIsInNwZWMiLCJhY2NlcHQiLCJBcnJheSIsImlzQXJyYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useAccept.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDrop.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/useDrop.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDrop: () => (/* binding */ useDrop)\n/* harmony export */ });\n/* harmony import */ var _useRegisteredDropTarget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useRegisteredDropTarget */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useRegisteredDropTarget.js\");\n/* harmony import */ var _useOptionalFactory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useOptionalFactory */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useOptionalFactory.js\");\n/* harmony import */ var _useDropTargetMonitor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDropTargetMonitor */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetMonitor.js\");\n/* harmony import */ var _useDropTargetConnector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useDropTargetConnector */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetConnector.js\");\n/* harmony import */ var _useCollectedProps__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../useCollectedProps */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollectedProps.js\");\n/* harmony import */ var _connectors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/connectors.js\");\n\n\n\n\n\n\n/**\n * useDropTarget Hook\n * @param spec The drop target specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */ function useDrop(specArg, deps) {\n    var spec = (0,_useOptionalFactory__WEBPACK_IMPORTED_MODULE_0__.useOptionalFactory)(specArg, deps);\n    var monitor = (0,_useDropTargetMonitor__WEBPACK_IMPORTED_MODULE_1__.useDropTargetMonitor)();\n    var connector = (0,_useDropTargetConnector__WEBPACK_IMPORTED_MODULE_2__.useDropTargetConnector)(spec.options);\n    (0,_useRegisteredDropTarget__WEBPACK_IMPORTED_MODULE_3__.useRegisteredDropTarget)(spec, monitor, connector);\n    return [\n        (0,_useCollectedProps__WEBPACK_IMPORTED_MODULE_4__.useCollectedProps)(spec.collect, monitor, connector),\n        (0,_connectors__WEBPACK_IMPORTED_MODULE_5__.useConnectDropTarget)(connector)\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDrop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTarget.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTarget.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropTarget: () => (/* binding */ useDropTarget)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _DropTargetImpl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DropTargetImpl */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/DropTargetImpl.js\");\n\n\nfunction useDropTarget(spec, monitor) {\n    var dropTarget = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return new _DropTargetImpl__WEBPACK_IMPORTED_MODULE_1__.DropTargetImpl(spec, monitor);\n    }, [\n        monitor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        dropTarget.spec = spec;\n    }, [\n        spec\n    ]);\n    return dropTarget;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlRHJvcFRhcmdldC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTJDO0FBQ087QUFDM0MsU0FBU0csY0FBY0MsSUFBSSxFQUFFQyxPQUFPO0lBQ3pDLElBQUlDLGFBQWFMLDhDQUFPQSxDQUFDO1FBQ3ZCLE9BQU8sSUFBSUMsMkRBQWNBLENBQUNFLE1BQU1DO0lBQ2xDLEdBQUc7UUFBQ0E7S0FBUTtJQUNaTCxnREFBU0EsQ0FBQztRQUNSTSxXQUFXRixJQUFJLEdBQUdBO0lBQ3BCLEdBQUc7UUFBQ0E7S0FBSztJQUNULE9BQU9FO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlRHJvcFRhcmdldC5qcz9kMDc2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IERyb3BUYXJnZXRJbXBsIH0gZnJvbSAnLi9Ecm9wVGFyZ2V0SW1wbCc7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJvcFRhcmdldChzcGVjLCBtb25pdG9yKSB7XG4gIHZhciBkcm9wVGFyZ2V0ID0gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIG5ldyBEcm9wVGFyZ2V0SW1wbChzcGVjLCBtb25pdG9yKTtcbiAgfSwgW21vbml0b3JdKTtcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBkcm9wVGFyZ2V0LnNwZWMgPSBzcGVjO1xuICB9LCBbc3BlY10pO1xuICByZXR1cm4gZHJvcFRhcmdldDtcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlTWVtbyIsIkRyb3BUYXJnZXRJbXBsIiwidXNlRHJvcFRhcmdldCIsInNwZWMiLCJtb25pdG9yIiwiZHJvcFRhcmdldCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTarget.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetConnector.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetConnector.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropTargetConnector: () => (/* binding */ useDropTargetConnector)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _internals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/TargetConnector.js\");\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\");\n\n\n\n\nfunction useDropTargetConnector(options) {\n    var manager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    var connector = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return new _internals__WEBPACK_IMPORTED_MODULE_2__.TargetConnector(manager.getBackend());\n    }, [\n        manager\n    ]);\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function() {\n        connector.dropTargetOptions = options || null;\n        connector.reconnect();\n        return function() {\n            return connector.disconnectDropTarget();\n        };\n    }, [\n        options\n    ]);\n    return connector;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlRHJvcFRhcmdldENvbm5lY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBZ0M7QUFDa0I7QUFDUztBQUNjO0FBQ2xFLFNBQVNJLHVCQUF1QkMsT0FBTztJQUM1QyxJQUFJQyxVQUFVSix1RUFBa0JBO0lBQ2hDLElBQUlLLFlBQVlQLDhDQUFPQSxDQUFDO1FBQ3RCLE9BQU8sSUFBSUMsdURBQWVBLENBQUNLLFFBQVFFLFVBQVU7SUFDL0MsR0FBRztRQUFDRjtLQUFRO0lBQ1pILHFGQUF5QkEsQ0FBQztRQUN4QkksVUFBVUUsaUJBQWlCLEdBQUdKLFdBQVc7UUFDekNFLFVBQVVHLFNBQVM7UUFDbkIsT0FBTztZQUNMLE9BQU9ILFVBQVVJLG9CQUFvQjtRQUN2QztJQUNGLEdBQUc7UUFBQ047S0FBUTtJQUNaLE9BQU9FO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlRHJvcFRhcmdldENvbm5lY3Rvci5qcz84YmNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBUYXJnZXRDb25uZWN0b3IgfSBmcm9tICcuLi8uLi9pbnRlcm5hbHMnO1xuaW1wb3J0IHsgdXNlRHJhZ0Ryb3BNYW5hZ2VyIH0gZnJvbSAnLi4vdXNlRHJhZ0Ryb3BNYW5hZ2VyJztcbmltcG9ydCB7IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgfSBmcm9tICcuLi91c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0JztcbmV4cG9ydCBmdW5jdGlvbiB1c2VEcm9wVGFyZ2V0Q29ubmVjdG9yKG9wdGlvbnMpIHtcbiAgdmFyIG1hbmFnZXIgPSB1c2VEcmFnRHJvcE1hbmFnZXIoKTtcbiAgdmFyIGNvbm5lY3RvciA9IHVzZU1lbW8oZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiBuZXcgVGFyZ2V0Q29ubmVjdG9yKG1hbmFnZXIuZ2V0QmFja2VuZCgpKTtcbiAgfSwgW21hbmFnZXJdKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgY29ubmVjdG9yLmRyb3BUYXJnZXRPcHRpb25zID0gb3B0aW9ucyB8fCBudWxsO1xuICAgIGNvbm5lY3Rvci5yZWNvbm5lY3QoKTtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgcmV0dXJuIGNvbm5lY3Rvci5kaXNjb25uZWN0RHJvcFRhcmdldCgpO1xuICAgIH07XG4gIH0sIFtvcHRpb25zXSk7XG4gIHJldHVybiBjb25uZWN0b3I7XG59Il0sIm5hbWVzIjpbInVzZU1lbW8iLCJUYXJnZXRDb25uZWN0b3IiLCJ1c2VEcmFnRHJvcE1hbmFnZXIiLCJ1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IiwidXNlRHJvcFRhcmdldENvbm5lY3RvciIsIm9wdGlvbnMiLCJtYW5hZ2VyIiwiY29ubmVjdG9yIiwiZ2V0QmFja2VuZCIsImRyb3BUYXJnZXRPcHRpb25zIiwicmVjb25uZWN0IiwiZGlzY29ubmVjdERyb3BUYXJnZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetMonitor.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetMonitor.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropTargetMonitor: () => (/* binding */ useDropTargetMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _internals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internals */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/DropTargetMonitorImpl.js\");\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n\n\n\nfunction useDropTargetMonitor() {\n    var manager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_1__.useDragDropManager)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return new _internals__WEBPACK_IMPORTED_MODULE_2__.DropTargetMonitorImpl(manager);\n    }, [\n        manager\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlRHJvcFRhcmdldE1vbml0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFDd0I7QUFDRztBQUNwRCxTQUFTRztJQUNkLElBQUlDLFVBQVVGLHVFQUFrQkE7SUFDaEMsT0FBT0YsOENBQU9BLENBQUM7UUFDYixPQUFPLElBQUlDLDZEQUFxQkEsQ0FBQ0c7SUFDbkMsR0FBRztRQUFDQTtLQUFRO0FBQ2QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZURyb3AvdXNlRHJvcFRhcmdldE1vbml0b3IuanM/MGE0MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRHJvcFRhcmdldE1vbml0b3JJbXBsIH0gZnJvbSAnLi4vLi4vaW50ZXJuYWxzJztcbmltcG9ydCB7IHVzZURyYWdEcm9wTWFuYWdlciB9IGZyb20gJy4uL3VzZURyYWdEcm9wTWFuYWdlcic7XG5leHBvcnQgZnVuY3Rpb24gdXNlRHJvcFRhcmdldE1vbml0b3IoKSB7XG4gIHZhciBtYW5hZ2VyID0gdXNlRHJhZ0Ryb3BNYW5hZ2VyKCk7XG4gIHJldHVybiB1c2VNZW1vKGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gbmV3IERyb3BUYXJnZXRNb25pdG9ySW1wbChtYW5hZ2VyKTtcbiAgfSwgW21hbmFnZXJdKTtcbn0iXSwibmFtZXMiOlsidXNlTWVtbyIsIkRyb3BUYXJnZXRNb25pdG9ySW1wbCIsInVzZURyYWdEcm9wTWFuYWdlciIsInVzZURyb3BUYXJnZXRNb25pdG9yIiwibWFuYWdlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTargetMonitor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useRegisteredDropTarget.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useDrop/useRegisteredDropTarget.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useRegisteredDropTarget: () => (/* binding */ useRegisteredDropTarget)\n/* harmony export */ });\n/* harmony import */ var _internals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../internals */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/registration.js\");\n/* harmony import */ var _useDragDropManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../useDragDropManager */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js\");\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useAccept__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useAccept */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useAccept.js\");\n/* harmony import */ var _useDropTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDropTarget */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDropTarget.js\");\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\n\n\n\nfunction useRegisteredDropTarget(spec, monitor, connector) {\n    var manager = (0,_useDragDropManager__WEBPACK_IMPORTED_MODULE_0__.useDragDropManager)();\n    var dropTarget = (0,_useDropTarget__WEBPACK_IMPORTED_MODULE_1__.useDropTarget)(spec, monitor);\n    var accept = (0,_useAccept__WEBPACK_IMPORTED_MODULE_2__.useAccept)(spec);\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function registerDropTarget() {\n        var _registerTarget = (0,_internals__WEBPACK_IMPORTED_MODULE_4__.registerTarget)(accept, dropTarget, manager), _registerTarget2 = _slicedToArray(_registerTarget, 2), handlerId = _registerTarget2[0], unregister = _registerTarget2[1];\n        monitor.receiveHandlerId(handlerId);\n        connector.receiveHandlerId(handlerId);\n        return unregister;\n    }, [\n        manager,\n        monitor,\n        dropTarget,\n        connector,\n        accept.map(function(a) {\n            return a.toString();\n        }).join(\"|\")\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useRegisteredDropTarget.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n // suppress the useLayoutEffect warning on server side.\nvar useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2hvb2tzL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW1ELENBQUMsdURBQXVEO0FBRXBHLElBQUlFLDRCQUE0QixNQUFrQixHQUFjRixDQUFlQSxHQUFHQyw0Q0FBU0EsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQvZGlzdC9lc20vaG9va3MvdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdC5qcz83YzYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUxheW91dEVmZmVjdCwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnOyAvLyBzdXBwcmVzcyB0aGUgdXNlTGF5b3V0RWZmZWN0IHdhcm5pbmcgb24gc2VydmVyIHNpZGUuXG5cbmV4cG9ydCB2YXIgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gdXNlTGF5b3V0RWZmZWN0IDogdXNlRWZmZWN0OyJdLCJuYW1lcyI6WyJ1c2VMYXlvdXRFZmZlY3QiLCJ1c2VFZmZlY3QiLCJ1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useMonitorOutput.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useMonitorOutput.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMonitorOutput: () => (/* binding */ useMonitorOutput)\n/* harmony export */ });\n/* harmony import */ var _useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useIsomorphicLayoutEffect */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _useCollector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useCollector */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useCollector.js\");\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\nfunction useMonitorOutput(monitor, collect, onCollect) {\n    var _useCollector = (0,_useCollector__WEBPACK_IMPORTED_MODULE_0__.useCollector)(monitor, collect, onCollect), _useCollector2 = _slicedToArray(_useCollector, 2), collected = _useCollector2[0], updateCollected = _useCollector2[1];\n    (0,_useIsomorphicLayoutEffect__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(function subscribeToMonitorStateChange() {\n        var handlerId = monitor.getHandlerId();\n        if (handlerId == null) {\n            return;\n        }\n        return monitor.subscribeToStateChange(updateCollected, {\n            handlerIds: [\n                handlerId\n            ]\n        });\n    }, [\n        monitor,\n        updateCollected\n    ]);\n    return collected;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useMonitorOutput.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/hooks/useOptionalFactory.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/hooks/useOptionalFactory.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOptionalFactory: () => (/* binding */ useOptionalFactory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\n\nfunction useOptionalFactory(arg, deps) {\n    var memoDeps = _toConsumableArray(deps || []);\n    if (deps == null && typeof arg !== \"function\") {\n        memoDeps.push(arg);\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return typeof arg === \"function\" ? arg() : arg;\n    }, memoDeps);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/hooks/useOptionalFactory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/DragSourceMonitorImpl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/DragSourceMonitorImpl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragSourceMonitorImpl: () => (/* binding */ DragSourceMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\nvar isCallingCanDrag = false;\nvar isCallingIsDragging = false;\nvar DragSourceMonitorImpl = /*#__PURE__*/ function() {\n    function DragSourceMonitorImpl(manager) {\n        _classCallCheck(this, DragSourceMonitorImpl);\n        _defineProperty(this, \"internalMonitor\", void 0);\n        _defineProperty(this, \"sourceId\", null);\n        this.internalMonitor = manager.getMonitor();\n    }\n    _createClass(DragSourceMonitorImpl, [\n        {\n            key: \"receiveHandlerId\",\n            value: function receiveHandlerId(sourceId) {\n                this.sourceId = sourceId;\n            }\n        },\n        {\n            key: \"getHandlerId\",\n            value: function getHandlerId() {\n                return this.sourceId;\n            }\n        },\n        {\n            key: \"canDrag\",\n            value: function canDrag() {\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!isCallingCanDrag, \"You may not call monitor.canDrag() inside your canDrag() implementation. \" + \"Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor\");\n                try {\n                    isCallingCanDrag = true;\n                    return this.internalMonitor.canDragSource(this.sourceId);\n                } finally{\n                    isCallingCanDrag = false;\n                }\n            }\n        },\n        {\n            key: \"isDragging\",\n            value: function isDragging() {\n                if (!this.sourceId) {\n                    return false;\n                }\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!isCallingIsDragging, \"You may not call monitor.isDragging() inside your isDragging() implementation. \" + \"Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor\");\n                try {\n                    isCallingIsDragging = true;\n                    return this.internalMonitor.isDraggingSource(this.sourceId);\n                } finally{\n                    isCallingIsDragging = false;\n                }\n            }\n        },\n        {\n            key: \"subscribeToStateChange\",\n            value: function subscribeToStateChange(listener, options) {\n                return this.internalMonitor.subscribeToStateChange(listener, options);\n            }\n        },\n        {\n            key: \"isDraggingSource\",\n            value: function isDraggingSource(sourceId) {\n                return this.internalMonitor.isDraggingSource(sourceId);\n            }\n        },\n        {\n            key: \"isOverTarget\",\n            value: function isOverTarget(targetId, options) {\n                return this.internalMonitor.isOverTarget(targetId, options);\n            }\n        },\n        {\n            key: \"getTargetIds\",\n            value: function getTargetIds() {\n                return this.internalMonitor.getTargetIds();\n            }\n        },\n        {\n            key: \"isSourcePublic\",\n            value: function isSourcePublic() {\n                return this.internalMonitor.isSourcePublic();\n            }\n        },\n        {\n            key: \"getSourceId\",\n            value: function getSourceId() {\n                return this.internalMonitor.getSourceId();\n            }\n        },\n        {\n            key: \"subscribeToOffsetChange\",\n            value: function subscribeToOffsetChange(listener) {\n                return this.internalMonitor.subscribeToOffsetChange(listener);\n            }\n        },\n        {\n            key: \"canDragSource\",\n            value: function canDragSource(sourceId) {\n                return this.internalMonitor.canDragSource(sourceId);\n            }\n        },\n        {\n            key: \"canDropOnTarget\",\n            value: function canDropOnTarget(targetId) {\n                return this.internalMonitor.canDropOnTarget(targetId);\n            }\n        },\n        {\n            key: \"getItemType\",\n            value: function getItemType() {\n                return this.internalMonitor.getItemType();\n            }\n        },\n        {\n            key: \"getItem\",\n            value: function getItem() {\n                return this.internalMonitor.getItem();\n            }\n        },\n        {\n            key: \"getDropResult\",\n            value: function getDropResult() {\n                return this.internalMonitor.getDropResult();\n            }\n        },\n        {\n            key: \"didDrop\",\n            value: function didDrop() {\n                return this.internalMonitor.didDrop();\n            }\n        },\n        {\n            key: \"getInitialClientOffset\",\n            value: function getInitialClientOffset() {\n                return this.internalMonitor.getInitialClientOffset();\n            }\n        },\n        {\n            key: \"getInitialSourceClientOffset\",\n            value: function getInitialSourceClientOffset() {\n                return this.internalMonitor.getInitialSourceClientOffset();\n            }\n        },\n        {\n            key: \"getSourceClientOffset\",\n            value: function getSourceClientOffset() {\n                return this.internalMonitor.getSourceClientOffset();\n            }\n        },\n        {\n            key: \"getClientOffset\",\n            value: function getClientOffset() {\n                return this.internalMonitor.getClientOffset();\n            }\n        },\n        {\n            key: \"getDifferenceFromInitialOffset\",\n            value: function getDifferenceFromInitialOffset() {\n                return this.internalMonitor.getDifferenceFromInitialOffset();\n            }\n        }\n    ]);\n    return DragSourceMonitorImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/DragSourceMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/DropTargetMonitorImpl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/DropTargetMonitorImpl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropTargetMonitorImpl: () => (/* binding */ DropTargetMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\nvar isCallingCanDrop = false;\nvar DropTargetMonitorImpl = /*#__PURE__*/ function() {\n    function DropTargetMonitorImpl(manager) {\n        _classCallCheck(this, DropTargetMonitorImpl);\n        _defineProperty(this, \"internalMonitor\", void 0);\n        _defineProperty(this, \"targetId\", null);\n        this.internalMonitor = manager.getMonitor();\n    }\n    _createClass(DropTargetMonitorImpl, [\n        {\n            key: \"receiveHandlerId\",\n            value: function receiveHandlerId(targetId) {\n                this.targetId = targetId;\n            }\n        },\n        {\n            key: \"getHandlerId\",\n            value: function getHandlerId() {\n                return this.targetId;\n            }\n        },\n        {\n            key: \"subscribeToStateChange\",\n            value: function subscribeToStateChange(listener, options) {\n                return this.internalMonitor.subscribeToStateChange(listener, options);\n            }\n        },\n        {\n            key: \"canDrop\",\n            value: function canDrop() {\n                // Cut out early if the target id has not been set. This should prevent errors\n                // where the user has an older version of dnd-core like in\n                // https://github.com/react-dnd/react-dnd/issues/1310\n                if (!this.targetId) {\n                    return false;\n                }\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!isCallingCanDrop, \"You may not call monitor.canDrop() inside your canDrop() implementation. \" + \"Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor\");\n                try {\n                    isCallingCanDrop = true;\n                    return this.internalMonitor.canDropOnTarget(this.targetId);\n                } finally{\n                    isCallingCanDrop = false;\n                }\n            }\n        },\n        {\n            key: \"isOver\",\n            value: function isOver(options) {\n                if (!this.targetId) {\n                    return false;\n                }\n                return this.internalMonitor.isOverTarget(this.targetId, options);\n            }\n        },\n        {\n            key: \"getItemType\",\n            value: function getItemType() {\n                return this.internalMonitor.getItemType();\n            }\n        },\n        {\n            key: \"getItem\",\n            value: function getItem() {\n                return this.internalMonitor.getItem();\n            }\n        },\n        {\n            key: \"getDropResult\",\n            value: function getDropResult() {\n                return this.internalMonitor.getDropResult();\n            }\n        },\n        {\n            key: \"didDrop\",\n            value: function didDrop() {\n                return this.internalMonitor.didDrop();\n            }\n        },\n        {\n            key: \"getInitialClientOffset\",\n            value: function getInitialClientOffset() {\n                return this.internalMonitor.getInitialClientOffset();\n            }\n        },\n        {\n            key: \"getInitialSourceClientOffset\",\n            value: function getInitialSourceClientOffset() {\n                return this.internalMonitor.getInitialSourceClientOffset();\n            }\n        },\n        {\n            key: \"getSourceClientOffset\",\n            value: function getSourceClientOffset() {\n                return this.internalMonitor.getSourceClientOffset();\n            }\n        },\n        {\n            key: \"getClientOffset\",\n            value: function getClientOffset() {\n                return this.internalMonitor.getClientOffset();\n            }\n        },\n        {\n            key: \"getDifferenceFromInitialOffset\",\n            value: function getDifferenceFromInitialOffset() {\n                return this.internalMonitor.getDifferenceFromInitialOffset();\n            }\n        }\n    ]);\n    return DropTargetMonitorImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/DropTargetMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/SourceConnector.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/SourceConnector.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SourceConnector: () => (/* binding */ SourceConnector)\n/* harmony export */ });\n/* harmony import */ var _wrapConnectorHooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrapConnectorHooks */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/wrapConnectorHooks.js\");\n/* harmony import */ var _isRef__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRef */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/isRef.js\");\n/* harmony import */ var _react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/shallowequal */ \"(ssr)/./node_modules/@react-dnd/shallowequal/dist/shallowequal.esm.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\nvar SourceConnector = /*#__PURE__*/ function() {\n    // The drop target may either be attached via ref or connect function\n    // The drag preview may either be attached via ref or connect function\n    function SourceConnector(backend) {\n        var _this = this;\n        _classCallCheck(this, SourceConnector);\n        _defineProperty(this, \"hooks\", (0,_wrapConnectorHooks__WEBPACK_IMPORTED_MODULE_1__.wrapConnectorHooks)({\n            dragSource: function dragSource(node, options) {\n                _this.clearDragSource();\n                _this.dragSourceOptions = options || null;\n                if ((0,_isRef__WEBPACK_IMPORTED_MODULE_2__.isRef)(node)) {\n                    _this.dragSourceRef = node;\n                } else {\n                    _this.dragSourceNode = node;\n                }\n                _this.reconnectDragSource();\n            },\n            dragPreview: function dragPreview(node, options) {\n                _this.clearDragPreview();\n                _this.dragPreviewOptions = options || null;\n                if ((0,_isRef__WEBPACK_IMPORTED_MODULE_2__.isRef)(node)) {\n                    _this.dragPreviewRef = node;\n                } else {\n                    _this.dragPreviewNode = node;\n                }\n                _this.reconnectDragPreview();\n            }\n        }));\n        _defineProperty(this, \"handlerId\", null);\n        _defineProperty(this, \"dragSourceRef\", null);\n        _defineProperty(this, \"dragSourceNode\", void 0);\n        _defineProperty(this, \"dragSourceOptionsInternal\", null);\n        _defineProperty(this, \"dragSourceUnsubscribe\", void 0);\n        _defineProperty(this, \"dragPreviewRef\", null);\n        _defineProperty(this, \"dragPreviewNode\", void 0);\n        _defineProperty(this, \"dragPreviewOptionsInternal\", null);\n        _defineProperty(this, \"dragPreviewUnsubscribe\", void 0);\n        _defineProperty(this, \"lastConnectedHandlerId\", null);\n        _defineProperty(this, \"lastConnectedDragSource\", null);\n        _defineProperty(this, \"lastConnectedDragSourceOptions\", null);\n        _defineProperty(this, \"lastConnectedDragPreview\", null);\n        _defineProperty(this, \"lastConnectedDragPreviewOptions\", null);\n        _defineProperty(this, \"backend\", void 0);\n        this.backend = backend;\n    }\n    _createClass(SourceConnector, [\n        {\n            key: \"receiveHandlerId\",\n            value: function receiveHandlerId(newHandlerId) {\n                if (this.handlerId === newHandlerId) {\n                    return;\n                }\n                this.handlerId = newHandlerId;\n                this.reconnect();\n            }\n        },\n        {\n            key: \"connectTarget\",\n            get: function get() {\n                return this.dragSource;\n            }\n        },\n        {\n            key: \"dragSourceOptions\",\n            get: function get() {\n                return this.dragSourceOptionsInternal;\n            },\n            set: function set(options) {\n                this.dragSourceOptionsInternal = options;\n            }\n        },\n        {\n            key: \"dragPreviewOptions\",\n            get: function get() {\n                return this.dragPreviewOptionsInternal;\n            },\n            set: function set(options) {\n                this.dragPreviewOptionsInternal = options;\n            }\n        },\n        {\n            key: \"reconnect\",\n            value: function reconnect() {\n                this.reconnectDragSource();\n                this.reconnectDragPreview();\n            }\n        },\n        {\n            key: \"reconnectDragSource\",\n            value: function reconnectDragSource() {\n                var dragSource = this.dragSource; // if nothing has changed then don't resubscribe\n                var didChange = this.didHandlerIdChange() || this.didConnectedDragSourceChange() || this.didDragSourceOptionsChange();\n                if (didChange) {\n                    this.disconnectDragSource();\n                }\n                if (!this.handlerId) {\n                    return;\n                }\n                if (!dragSource) {\n                    this.lastConnectedDragSource = dragSource;\n                    return;\n                }\n                if (didChange) {\n                    this.lastConnectedHandlerId = this.handlerId;\n                    this.lastConnectedDragSource = dragSource;\n                    this.lastConnectedDragSourceOptions = this.dragSourceOptions;\n                    this.dragSourceUnsubscribe = this.backend.connectDragSource(this.handlerId, dragSource, this.dragSourceOptions);\n                }\n            }\n        },\n        {\n            key: \"reconnectDragPreview\",\n            value: function reconnectDragPreview() {\n                var dragPreview = this.dragPreview; // if nothing has changed then don't resubscribe\n                var didChange = this.didHandlerIdChange() || this.didConnectedDragPreviewChange() || this.didDragPreviewOptionsChange();\n                if (didChange) {\n                    this.disconnectDragPreview();\n                }\n                if (!this.handlerId) {\n                    return;\n                }\n                if (!dragPreview) {\n                    this.lastConnectedDragPreview = dragPreview;\n                    return;\n                }\n                if (didChange) {\n                    this.lastConnectedHandlerId = this.handlerId;\n                    this.lastConnectedDragPreview = dragPreview;\n                    this.lastConnectedDragPreviewOptions = this.dragPreviewOptions;\n                    this.dragPreviewUnsubscribe = this.backend.connectDragPreview(this.handlerId, dragPreview, this.dragPreviewOptions);\n                }\n            }\n        },\n        {\n            key: \"didHandlerIdChange\",\n            value: function didHandlerIdChange() {\n                return this.lastConnectedHandlerId !== this.handlerId;\n            }\n        },\n        {\n            key: \"didConnectedDragSourceChange\",\n            value: function didConnectedDragSourceChange() {\n                return this.lastConnectedDragSource !== this.dragSource;\n            }\n        },\n        {\n            key: \"didConnectedDragPreviewChange\",\n            value: function didConnectedDragPreviewChange() {\n                return this.lastConnectedDragPreview !== this.dragPreview;\n            }\n        },\n        {\n            key: \"didDragSourceOptionsChange\",\n            value: function didDragSourceOptionsChange() {\n                return !(0,_react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__.shallowEqual)(this.lastConnectedDragSourceOptions, this.dragSourceOptions);\n            }\n        },\n        {\n            key: \"didDragPreviewOptionsChange\",\n            value: function didDragPreviewOptionsChange() {\n                return !(0,_react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__.shallowEqual)(this.lastConnectedDragPreviewOptions, this.dragPreviewOptions);\n            }\n        },\n        {\n            key: \"disconnectDragSource\",\n            value: function disconnectDragSource() {\n                if (this.dragSourceUnsubscribe) {\n                    this.dragSourceUnsubscribe();\n                    this.dragSourceUnsubscribe = undefined;\n                }\n            }\n        },\n        {\n            key: \"disconnectDragPreview\",\n            value: function disconnectDragPreview() {\n                if (this.dragPreviewUnsubscribe) {\n                    this.dragPreviewUnsubscribe();\n                    this.dragPreviewUnsubscribe = undefined;\n                    this.dragPreviewNode = null;\n                    this.dragPreviewRef = null;\n                }\n            }\n        },\n        {\n            key: \"dragSource\",\n            get: function get() {\n                return this.dragSourceNode || this.dragSourceRef && this.dragSourceRef.current;\n            }\n        },\n        {\n            key: \"dragPreview\",\n            get: function get() {\n                return this.dragPreviewNode || this.dragPreviewRef && this.dragPreviewRef.current;\n            }\n        },\n        {\n            key: \"clearDragSource\",\n            value: function clearDragSource() {\n                this.dragSourceNode = null;\n                this.dragSourceRef = null;\n            }\n        },\n        {\n            key: \"clearDragPreview\",\n            value: function clearDragPreview() {\n                this.dragPreviewNode = null;\n                this.dragPreviewRef = null;\n            }\n        }\n    ]);\n    return SourceConnector;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/SourceConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/TargetConnector.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/TargetConnector.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TargetConnector: () => (/* binding */ TargetConnector)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/shallowequal */ \"(ssr)/./node_modules/@react-dnd/shallowequal/dist/shallowequal.esm.js\");\n/* harmony import */ var _wrapConnectorHooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wrapConnectorHooks */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/wrapConnectorHooks.js\");\n/* harmony import */ var _isRef__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRef */ \"(ssr)/./node_modules/react-dnd/dist/esm/internals/isRef.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\nvar TargetConnector = /*#__PURE__*/ function() {\n    // The drop target may either be attached via ref or connect function\n    function TargetConnector(backend) {\n        var _this = this;\n        _classCallCheck(this, TargetConnector);\n        _defineProperty(this, \"hooks\", (0,_wrapConnectorHooks__WEBPACK_IMPORTED_MODULE_1__.wrapConnectorHooks)({\n            dropTarget: function dropTarget(node, options) {\n                _this.clearDropTarget();\n                _this.dropTargetOptions = options;\n                if ((0,_isRef__WEBPACK_IMPORTED_MODULE_2__.isRef)(node)) {\n                    _this.dropTargetRef = node;\n                } else {\n                    _this.dropTargetNode = node;\n                }\n                _this.reconnect();\n            }\n        }));\n        _defineProperty(this, \"handlerId\", null);\n        _defineProperty(this, \"dropTargetRef\", null);\n        _defineProperty(this, \"dropTargetNode\", void 0);\n        _defineProperty(this, \"dropTargetOptionsInternal\", null);\n        _defineProperty(this, \"unsubscribeDropTarget\", void 0);\n        _defineProperty(this, \"lastConnectedHandlerId\", null);\n        _defineProperty(this, \"lastConnectedDropTarget\", null);\n        _defineProperty(this, \"lastConnectedDropTargetOptions\", null);\n        _defineProperty(this, \"backend\", void 0);\n        this.backend = backend;\n    }\n    _createClass(TargetConnector, [\n        {\n            key: \"connectTarget\",\n            get: function get() {\n                return this.dropTarget;\n            }\n        },\n        {\n            key: \"reconnect\",\n            value: function reconnect() {\n                // if nothing has changed then don't resubscribe\n                var didChange = this.didHandlerIdChange() || this.didDropTargetChange() || this.didOptionsChange();\n                if (didChange) {\n                    this.disconnectDropTarget();\n                }\n                var dropTarget = this.dropTarget;\n                if (!this.handlerId) {\n                    return;\n                }\n                if (!dropTarget) {\n                    this.lastConnectedDropTarget = dropTarget;\n                    return;\n                }\n                if (didChange) {\n                    this.lastConnectedHandlerId = this.handlerId;\n                    this.lastConnectedDropTarget = dropTarget;\n                    this.lastConnectedDropTargetOptions = this.dropTargetOptions;\n                    this.unsubscribeDropTarget = this.backend.connectDropTarget(this.handlerId, dropTarget, this.dropTargetOptions);\n                }\n            }\n        },\n        {\n            key: \"receiveHandlerId\",\n            value: function receiveHandlerId(newHandlerId) {\n                if (newHandlerId === this.handlerId) {\n                    return;\n                }\n                this.handlerId = newHandlerId;\n                this.reconnect();\n            }\n        },\n        {\n            key: \"dropTargetOptions\",\n            get: function get() {\n                return this.dropTargetOptionsInternal;\n            },\n            set: function set(options) {\n                this.dropTargetOptionsInternal = options;\n            }\n        },\n        {\n            key: \"didHandlerIdChange\",\n            value: function didHandlerIdChange() {\n                return this.lastConnectedHandlerId !== this.handlerId;\n            }\n        },\n        {\n            key: \"didDropTargetChange\",\n            value: function didDropTargetChange() {\n                return this.lastConnectedDropTarget !== this.dropTarget;\n            }\n        },\n        {\n            key: \"didOptionsChange\",\n            value: function didOptionsChange() {\n                return !(0,_react_dnd_shallowequal__WEBPACK_IMPORTED_MODULE_0__.shallowEqual)(this.lastConnectedDropTargetOptions, this.dropTargetOptions);\n            }\n        },\n        {\n            key: \"disconnectDropTarget\",\n            value: function disconnectDropTarget() {\n                if (this.unsubscribeDropTarget) {\n                    this.unsubscribeDropTarget();\n                    this.unsubscribeDropTarget = undefined;\n                }\n            }\n        },\n        {\n            key: \"dropTarget\",\n            get: function get() {\n                return this.dropTargetNode || this.dropTargetRef && this.dropTargetRef.current;\n            }\n        },\n        {\n            key: \"clearDropTarget\",\n            value: function clearDropTarget() {\n                this.dropTargetRef = null;\n                this.dropTargetNode = null;\n            }\n        }\n    ]);\n    return TargetConnector;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2ludGVybmFscy9UYXJnZXRDb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLFNBQVNBLGdCQUFnQkMsUUFBUSxFQUFFQyxXQUFXO0lBQUksSUFBSSxDQUFFRCxDQUFBQSxvQkFBb0JDLFdBQVUsR0FBSTtRQUFFLE1BQU0sSUFBSUMsVUFBVTtJQUFzQztBQUFFO0FBRXhKLFNBQVNDLGtCQUFrQkMsTUFBTSxFQUFFQyxLQUFLO0lBQUksSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlELE1BQU1FLE1BQU0sRUFBRUQsSUFBSztRQUFFLElBQUlFLGFBQWFILEtBQUssQ0FBQ0MsRUFBRTtRQUFFRSxXQUFXQyxVQUFVLEdBQUdELFdBQVdDLFVBQVUsSUFBSTtRQUFPRCxXQUFXRSxZQUFZLEdBQUc7UUFBTSxJQUFJLFdBQVdGLFlBQVlBLFdBQVdHLFFBQVEsR0FBRztRQUFNQyxPQUFPQyxjQUFjLENBQUNULFFBQVFJLFdBQVdNLEdBQUcsRUFBRU47SUFBYTtBQUFFO0FBRTVULFNBQVNPLGFBQWFkLFdBQVcsRUFBRWUsVUFBVSxFQUFFQyxXQUFXO0lBQUksSUFBSUQsWUFBWWIsa0JBQWtCRixZQUFZaUIsU0FBUyxFQUFFRjtJQUFhLElBQUlDLGFBQWFkLGtCQUFrQkYsYUFBYWdCO0lBQWMsT0FBT2hCO0FBQWE7QUFFdE4sU0FBU2tCLGdCQUFnQkMsR0FBRyxFQUFFTixHQUFHLEVBQUVPLEtBQUs7SUFBSSxJQUFJUCxPQUFPTSxLQUFLO1FBQUVSLE9BQU9DLGNBQWMsQ0FBQ08sS0FBS04sS0FBSztZQUFFTyxPQUFPQTtZQUFPWixZQUFZO1lBQU1DLGNBQWM7WUFBTUMsVUFBVTtRQUFLO0lBQUksT0FBTztRQUFFUyxHQUFHLENBQUNOLElBQUksR0FBR087SUFBTztJQUFFLE9BQU9EO0FBQUs7QUFFeko7QUFDRztBQUMxQjtBQUN6QixJQUFJSyxrQkFBa0IsV0FBVyxHQUFFO0lBQ3hDLHFFQUFxRTtJQUNyRSxTQUFTQSxnQkFBZ0JDLE9BQU87UUFDOUIsSUFBSUMsUUFBUSxJQUFJO1FBRWhCNUIsZ0JBQWdCLElBQUksRUFBRTBCO1FBRXRCTixnQkFBZ0IsSUFBSSxFQUFFLFNBQVNJLHVFQUFrQkEsQ0FBQztZQUNoREssWUFBWSxTQUFTQSxXQUFXQyxJQUFJLEVBQUVDLE9BQU87Z0JBQzNDSCxNQUFNSSxlQUFlO2dCQUVyQkosTUFBTUssaUJBQWlCLEdBQUdGO2dCQUUxQixJQUFJTiw2Q0FBS0EsQ0FBQ0ssT0FBTztvQkFDZkYsTUFBTU0sYUFBYSxHQUFHSjtnQkFDeEIsT0FBTztvQkFDTEYsTUFBTU8sY0FBYyxHQUFHTDtnQkFDekI7Z0JBRUFGLE1BQU1RLFNBQVM7WUFDakI7UUFDRjtRQUVBaEIsZ0JBQWdCLElBQUksRUFBRSxhQUFhO1FBRW5DQSxnQkFBZ0IsSUFBSSxFQUFFLGlCQUFpQjtRQUV2Q0EsZ0JBQWdCLElBQUksRUFBRSxrQkFBa0IsS0FBSztRQUU3Q0EsZ0JBQWdCLElBQUksRUFBRSw2QkFBNkI7UUFFbkRBLGdCQUFnQixJQUFJLEVBQUUseUJBQXlCLEtBQUs7UUFFcERBLGdCQUFnQixJQUFJLEVBQUUsMEJBQTBCO1FBRWhEQSxnQkFBZ0IsSUFBSSxFQUFFLDJCQUEyQjtRQUVqREEsZ0JBQWdCLElBQUksRUFBRSxrQ0FBa0M7UUFFeERBLGdCQUFnQixJQUFJLEVBQUUsV0FBVyxLQUFLO1FBRXRDLElBQUksQ0FBQ08sT0FBTyxHQUFHQTtJQUNqQjtJQUVBWCxhQUFhVSxpQkFBaUI7UUFBQztZQUM3QlgsS0FBSztZQUNMc0IsS0FBSyxTQUFTQTtnQkFDWixPQUFPLElBQUksQ0FBQ1IsVUFBVTtZQUN4QjtRQUNGO1FBQUc7WUFDRGQsS0FBSztZQUNMTyxPQUFPLFNBQVNjO2dCQUNkLGdEQUFnRDtnQkFDaEQsSUFBSUUsWUFBWSxJQUFJLENBQUNDLGtCQUFrQixNQUFNLElBQUksQ0FBQ0MsbUJBQW1CLE1BQU0sSUFBSSxDQUFDQyxnQkFBZ0I7Z0JBRWhHLElBQUlILFdBQVc7b0JBQ2IsSUFBSSxDQUFDSSxvQkFBb0I7Z0JBQzNCO2dCQUVBLElBQUliLGFBQWEsSUFBSSxDQUFDQSxVQUFVO2dCQUVoQyxJQUFJLENBQUMsSUFBSSxDQUFDYyxTQUFTLEVBQUU7b0JBQ25CO2dCQUNGO2dCQUVBLElBQUksQ0FBQ2QsWUFBWTtvQkFDZixJQUFJLENBQUNlLHVCQUF1QixHQUFHZjtvQkFDL0I7Z0JBQ0Y7Z0JBRUEsSUFBSVMsV0FBVztvQkFDYixJQUFJLENBQUNPLHNCQUFzQixHQUFHLElBQUksQ0FBQ0YsU0FBUztvQkFDNUMsSUFBSSxDQUFDQyx1QkFBdUIsR0FBR2Y7b0JBQy9CLElBQUksQ0FBQ2lCLDhCQUE4QixHQUFHLElBQUksQ0FBQ2IsaUJBQWlCO29CQUM1RCxJQUFJLENBQUNjLHFCQUFxQixHQUFHLElBQUksQ0FBQ3BCLE9BQU8sQ0FBQ3FCLGlCQUFpQixDQUFDLElBQUksQ0FBQ0wsU0FBUyxFQUFFZCxZQUFZLElBQUksQ0FBQ0ksaUJBQWlCO2dCQUNoSDtZQUNGO1FBQ0Y7UUFBRztZQUNEbEIsS0FBSztZQUNMTyxPQUFPLFNBQVMyQixpQkFBaUJDLFlBQVk7Z0JBQzNDLElBQUlBLGlCQUFpQixJQUFJLENBQUNQLFNBQVMsRUFBRTtvQkFDbkM7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDQSxTQUFTLEdBQUdPO2dCQUNqQixJQUFJLENBQUNkLFNBQVM7WUFDaEI7UUFDRjtRQUFHO1lBQ0RyQixLQUFLO1lBQ0xzQixLQUFLLFNBQVNBO2dCQUNaLE9BQU8sSUFBSSxDQUFDYyx5QkFBeUI7WUFDdkM7WUFDQUMsS0FBSyxTQUFTQSxJQUFJckIsT0FBTztnQkFDdkIsSUFBSSxDQUFDb0IseUJBQXlCLEdBQUdwQjtZQUNuQztRQUNGO1FBQUc7WUFDRGhCLEtBQUs7WUFDTE8sT0FBTyxTQUFTaUI7Z0JBQ2QsT0FBTyxJQUFJLENBQUNNLHNCQUFzQixLQUFLLElBQUksQ0FBQ0YsU0FBUztZQUN2RDtRQUNGO1FBQUc7WUFDRDVCLEtBQUs7WUFDTE8sT0FBTyxTQUFTa0I7Z0JBQ2QsT0FBTyxJQUFJLENBQUNJLHVCQUF1QixLQUFLLElBQUksQ0FBQ2YsVUFBVTtZQUN6RDtRQUNGO1FBQUc7WUFDRGQsS0FBSztZQUNMTyxPQUFPLFNBQVNtQjtnQkFDZCxPQUFPLENBQUNsQixxRUFBWUEsQ0FBQyxJQUFJLENBQUN1Qiw4QkFBOEIsRUFBRSxJQUFJLENBQUNiLGlCQUFpQjtZQUNsRjtRQUNGO1FBQUc7WUFDRGxCLEtBQUs7WUFDTE8sT0FBTyxTQUFTb0I7Z0JBQ2QsSUFBSSxJQUFJLENBQUNLLHFCQUFxQixFQUFFO29CQUM5QixJQUFJLENBQUNBLHFCQUFxQjtvQkFDMUIsSUFBSSxDQUFDQSxxQkFBcUIsR0FBR007Z0JBQy9CO1lBQ0Y7UUFDRjtRQUFHO1lBQ0R0QyxLQUFLO1lBQ0xzQixLQUFLLFNBQVNBO2dCQUNaLE9BQU8sSUFBSSxDQUFDRixjQUFjLElBQUksSUFBSSxDQUFDRCxhQUFhLElBQUksSUFBSSxDQUFDQSxhQUFhLENBQUNvQixPQUFPO1lBQ2hGO1FBQ0Y7UUFBRztZQUNEdkMsS0FBSztZQUNMTyxPQUFPLFNBQVNVO2dCQUNkLElBQUksQ0FBQ0UsYUFBYSxHQUFHO2dCQUNyQixJQUFJLENBQUNDLGNBQWMsR0FBRztZQUN4QjtRQUNGO0tBQUU7SUFFRixPQUFPVDtBQUNULElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2ludGVybmFscy9UYXJnZXRDb25uZWN0b3IuanM/YjBiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBfY2xhc3NDYWxsQ2hlY2soaW5zdGFuY2UsIENvbnN0cnVjdG9yKSB7IGlmICghKGluc3RhbmNlIGluc3RhbmNlb2YgQ29uc3RydWN0b3IpKSB7IHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgY2FsbCBhIGNsYXNzIGFzIGEgZnVuY3Rpb25cIik7IH0gfVxuXG5mdW5jdGlvbiBfZGVmaW5lUHJvcGVydGllcyh0YXJnZXQsIHByb3BzKSB7IGZvciAodmFyIGkgPSAwOyBpIDwgcHJvcHMubGVuZ3RoOyBpKyspIHsgdmFyIGRlc2NyaXB0b3IgPSBwcm9wc1tpXTsgZGVzY3JpcHRvci5lbnVtZXJhYmxlID0gZGVzY3JpcHRvci5lbnVtZXJhYmxlIHx8IGZhbHNlOyBkZXNjcmlwdG9yLmNvbmZpZ3VyYWJsZSA9IHRydWU7IGlmIChcInZhbHVlXCIgaW4gZGVzY3JpcHRvcikgZGVzY3JpcHRvci53cml0YWJsZSA9IHRydWU7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGRlc2NyaXB0b3Iua2V5LCBkZXNjcmlwdG9yKTsgfSB9XG5cbmZ1bmN0aW9uIF9jcmVhdGVDbGFzcyhDb25zdHJ1Y3RvciwgcHJvdG9Qcm9wcywgc3RhdGljUHJvcHMpIHsgaWYgKHByb3RvUHJvcHMpIF9kZWZpbmVQcm9wZXJ0aWVzKENvbnN0cnVjdG9yLnByb3RvdHlwZSwgcHJvdG9Qcm9wcyk7IGlmIChzdGF0aWNQcm9wcykgX2RlZmluZVByb3BlcnRpZXMoQ29uc3RydWN0b3IsIHN0YXRpY1Byb3BzKTsgcmV0dXJuIENvbnN0cnVjdG9yOyB9XG5cbmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwgdmFsdWUpIHsgaWYgKGtleSBpbiBvYmopIHsgT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iaiwga2V5LCB7IHZhbHVlOiB2YWx1ZSwgZW51bWVyYWJsZTogdHJ1ZSwgY29uZmlndXJhYmxlOiB0cnVlLCB3cml0YWJsZTogdHJ1ZSB9KTsgfSBlbHNlIHsgb2JqW2tleV0gPSB2YWx1ZTsgfSByZXR1cm4gb2JqOyB9XG5cbmltcG9ydCB7IHNoYWxsb3dFcXVhbCB9IGZyb20gJ0ByZWFjdC1kbmQvc2hhbGxvd2VxdWFsJztcbmltcG9ydCB7IHdyYXBDb25uZWN0b3JIb29rcyB9IGZyb20gJy4vd3JhcENvbm5lY3Rvckhvb2tzJztcbmltcG9ydCB7IGlzUmVmIH0gZnJvbSAnLi9pc1JlZic7XG5leHBvcnQgdmFyIFRhcmdldENvbm5lY3RvciA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIC8vIFRoZSBkcm9wIHRhcmdldCBtYXkgZWl0aGVyIGJlIGF0dGFjaGVkIHZpYSByZWYgb3IgY29ubmVjdCBmdW5jdGlvblxuICBmdW5jdGlvbiBUYXJnZXRDb25uZWN0b3IoYmFja2VuZCkge1xuICAgIHZhciBfdGhpcyA9IHRoaXM7XG5cbiAgICBfY2xhc3NDYWxsQ2hlY2sodGhpcywgVGFyZ2V0Q29ubmVjdG9yKTtcblxuICAgIF9kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImhvb2tzXCIsIHdyYXBDb25uZWN0b3JIb29rcyh7XG4gICAgICBkcm9wVGFyZ2V0OiBmdW5jdGlvbiBkcm9wVGFyZ2V0KG5vZGUsIG9wdGlvbnMpIHtcbiAgICAgICAgX3RoaXMuY2xlYXJEcm9wVGFyZ2V0KCk7XG5cbiAgICAgICAgX3RoaXMuZHJvcFRhcmdldE9wdGlvbnMgPSBvcHRpb25zO1xuXG4gICAgICAgIGlmIChpc1JlZihub2RlKSkge1xuICAgICAgICAgIF90aGlzLmRyb3BUYXJnZXRSZWYgPSBub2RlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIF90aGlzLmRyb3BUYXJnZXROb2RlID0gbm9kZTtcbiAgICAgICAgfVxuXG4gICAgICAgIF90aGlzLnJlY29ubmVjdCgpO1xuICAgICAgfVxuICAgIH0pKTtcblxuICAgIF9kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImhhbmRsZXJJZFwiLCBudWxsKTtcblxuICAgIF9kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImRyb3BUYXJnZXRSZWZcIiwgbnVsbCk7XG5cbiAgICBfZGVmaW5lUHJvcGVydHkodGhpcywgXCJkcm9wVGFyZ2V0Tm9kZVwiLCB2b2lkIDApO1xuXG4gICAgX2RlZmluZVByb3BlcnR5KHRoaXMsIFwiZHJvcFRhcmdldE9wdGlvbnNJbnRlcm5hbFwiLCBudWxsKTtcblxuICAgIF9kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInVuc3Vic2NyaWJlRHJvcFRhcmdldFwiLCB2b2lkIDApO1xuXG4gICAgX2RlZmluZVByb3BlcnR5KHRoaXMsIFwibGFzdENvbm5lY3RlZEhhbmRsZXJJZFwiLCBudWxsKTtcblxuICAgIF9kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImxhc3RDb25uZWN0ZWREcm9wVGFyZ2V0XCIsIG51bGwpO1xuXG4gICAgX2RlZmluZVByb3BlcnR5KHRoaXMsIFwibGFzdENvbm5lY3RlZERyb3BUYXJnZXRPcHRpb25zXCIsIG51bGwpO1xuXG4gICAgX2RlZmluZVByb3BlcnR5KHRoaXMsIFwiYmFja2VuZFwiLCB2b2lkIDApO1xuXG4gICAgdGhpcy5iYWNrZW5kID0gYmFja2VuZDtcbiAgfVxuXG4gIF9jcmVhdGVDbGFzcyhUYXJnZXRDb25uZWN0b3IsIFt7XG4gICAga2V5OiBcImNvbm5lY3RUYXJnZXRcIixcbiAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICAgIHJldHVybiB0aGlzLmRyb3BUYXJnZXQ7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcInJlY29ubmVjdFwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiByZWNvbm5lY3QoKSB7XG4gICAgICAvLyBpZiBub3RoaW5nIGhhcyBjaGFuZ2VkIHRoZW4gZG9uJ3QgcmVzdWJzY3JpYmVcbiAgICAgIHZhciBkaWRDaGFuZ2UgPSB0aGlzLmRpZEhhbmRsZXJJZENoYW5nZSgpIHx8IHRoaXMuZGlkRHJvcFRhcmdldENoYW5nZSgpIHx8IHRoaXMuZGlkT3B0aW9uc0NoYW5nZSgpO1xuXG4gICAgICBpZiAoZGlkQ2hhbmdlKSB7XG4gICAgICAgIHRoaXMuZGlzY29ubmVjdERyb3BUYXJnZXQoKTtcbiAgICAgIH1cblxuICAgICAgdmFyIGRyb3BUYXJnZXQgPSB0aGlzLmRyb3BUYXJnZXQ7XG5cbiAgICAgIGlmICghdGhpcy5oYW5kbGVySWQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBpZiAoIWRyb3BUYXJnZXQpIHtcbiAgICAgICAgdGhpcy5sYXN0Q29ubmVjdGVkRHJvcFRhcmdldCA9IGRyb3BUYXJnZXQ7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgaWYgKGRpZENoYW5nZSkge1xuICAgICAgICB0aGlzLmxhc3RDb25uZWN0ZWRIYW5kbGVySWQgPSB0aGlzLmhhbmRsZXJJZDtcbiAgICAgICAgdGhpcy5sYXN0Q29ubmVjdGVkRHJvcFRhcmdldCA9IGRyb3BUYXJnZXQ7XG4gICAgICAgIHRoaXMubGFzdENvbm5lY3RlZERyb3BUYXJnZXRPcHRpb25zID0gdGhpcy5kcm9wVGFyZ2V0T3B0aW9ucztcbiAgICAgICAgdGhpcy51bnN1YnNjcmliZURyb3BUYXJnZXQgPSB0aGlzLmJhY2tlbmQuY29ubmVjdERyb3BUYXJnZXQodGhpcy5oYW5kbGVySWQsIGRyb3BUYXJnZXQsIHRoaXMuZHJvcFRhcmdldE9wdGlvbnMpO1xuICAgICAgfVxuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJyZWNlaXZlSGFuZGxlcklkXCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIHJlY2VpdmVIYW5kbGVySWQobmV3SGFuZGxlcklkKSB7XG4gICAgICBpZiAobmV3SGFuZGxlcklkID09PSB0aGlzLmhhbmRsZXJJZCkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRoaXMuaGFuZGxlcklkID0gbmV3SGFuZGxlcklkO1xuICAgICAgdGhpcy5yZWNvbm5lY3QoKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZHJvcFRhcmdldE9wdGlvbnNcIixcbiAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICAgIHJldHVybiB0aGlzLmRyb3BUYXJnZXRPcHRpb25zSW50ZXJuYWw7XG4gICAgfSxcbiAgICBzZXQ6IGZ1bmN0aW9uIHNldChvcHRpb25zKSB7XG4gICAgICB0aGlzLmRyb3BUYXJnZXRPcHRpb25zSW50ZXJuYWwgPSBvcHRpb25zO1xuICAgIH1cbiAgfSwge1xuICAgIGtleTogXCJkaWRIYW5kbGVySWRDaGFuZ2VcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZGlkSGFuZGxlcklkQ2hhbmdlKCkge1xuICAgICAgcmV0dXJuIHRoaXMubGFzdENvbm5lY3RlZEhhbmRsZXJJZCAhPT0gdGhpcy5oYW5kbGVySWQ7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcImRpZERyb3BUYXJnZXRDaGFuZ2VcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZGlkRHJvcFRhcmdldENoYW5nZSgpIHtcbiAgICAgIHJldHVybiB0aGlzLmxhc3RDb25uZWN0ZWREcm9wVGFyZ2V0ICE9PSB0aGlzLmRyb3BUYXJnZXQ7XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcImRpZE9wdGlvbnNDaGFuZ2VcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZGlkT3B0aW9uc0NoYW5nZSgpIHtcbiAgICAgIHJldHVybiAhc2hhbGxvd0VxdWFsKHRoaXMubGFzdENvbm5lY3RlZERyb3BUYXJnZXRPcHRpb25zLCB0aGlzLmRyb3BUYXJnZXRPcHRpb25zKTtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiZGlzY29ubmVjdERyb3BUYXJnZXRcIixcbiAgICB2YWx1ZTogZnVuY3Rpb24gZGlzY29ubmVjdERyb3BUYXJnZXQoKSB7XG4gICAgICBpZiAodGhpcy51bnN1YnNjcmliZURyb3BUYXJnZXQpIHtcbiAgICAgICAgdGhpcy51bnN1YnNjcmliZURyb3BUYXJnZXQoKTtcbiAgICAgICAgdGhpcy51bnN1YnNjcmliZURyb3BUYXJnZXQgPSB1bmRlZmluZWQ7XG4gICAgICB9XG4gICAgfVxuICB9LCB7XG4gICAga2V5OiBcImRyb3BUYXJnZXRcIixcbiAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICAgIHJldHVybiB0aGlzLmRyb3BUYXJnZXROb2RlIHx8IHRoaXMuZHJvcFRhcmdldFJlZiAmJiB0aGlzLmRyb3BUYXJnZXRSZWYuY3VycmVudDtcbiAgICB9XG4gIH0sIHtcbiAgICBrZXk6IFwiY2xlYXJEcm9wVGFyZ2V0XCIsXG4gICAgdmFsdWU6IGZ1bmN0aW9uIGNsZWFyRHJvcFRhcmdldCgpIHtcbiAgICAgIHRoaXMuZHJvcFRhcmdldFJlZiA9IG51bGw7XG4gICAgICB0aGlzLmRyb3BUYXJnZXROb2RlID0gbnVsbDtcbiAgICB9XG4gIH1dKTtcblxuICByZXR1cm4gVGFyZ2V0Q29ubmVjdG9yO1xufSgpOyJdLCJuYW1lcyI6WyJfY2xhc3NDYWxsQ2hlY2siLCJpbnN0YW5jZSIsIkNvbnN0cnVjdG9yIiwiVHlwZUVycm9yIiwiX2RlZmluZVByb3BlcnRpZXMiLCJ0YXJnZXQiLCJwcm9wcyIsImkiLCJsZW5ndGgiLCJkZXNjcmlwdG9yIiwiZW51bWVyYWJsZSIsImNvbmZpZ3VyYWJsZSIsIndyaXRhYmxlIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJrZXkiLCJfY3JlYXRlQ2xhc3MiLCJwcm90b1Byb3BzIiwic3RhdGljUHJvcHMiLCJwcm90b3R5cGUiLCJfZGVmaW5lUHJvcGVydHkiLCJvYmoiLCJ2YWx1ZSIsInNoYWxsb3dFcXVhbCIsIndyYXBDb25uZWN0b3JIb29rcyIsImlzUmVmIiwiVGFyZ2V0Q29ubmVjdG9yIiwiYmFja2VuZCIsIl90aGlzIiwiZHJvcFRhcmdldCIsIm5vZGUiLCJvcHRpb25zIiwiY2xlYXJEcm9wVGFyZ2V0IiwiZHJvcFRhcmdldE9wdGlvbnMiLCJkcm9wVGFyZ2V0UmVmIiwiZHJvcFRhcmdldE5vZGUiLCJyZWNvbm5lY3QiLCJnZXQiLCJkaWRDaGFuZ2UiLCJkaWRIYW5kbGVySWRDaGFuZ2UiLCJkaWREcm9wVGFyZ2V0Q2hhbmdlIiwiZGlkT3B0aW9uc0NoYW5nZSIsImRpc2Nvbm5lY3REcm9wVGFyZ2V0IiwiaGFuZGxlcklkIiwibGFzdENvbm5lY3RlZERyb3BUYXJnZXQiLCJsYXN0Q29ubmVjdGVkSGFuZGxlcklkIiwibGFzdENvbm5lY3RlZERyb3BUYXJnZXRPcHRpb25zIiwidW5zdWJzY3JpYmVEcm9wVGFyZ2V0IiwiY29ubmVjdERyb3BUYXJnZXQiLCJyZWNlaXZlSGFuZGxlcklkIiwibmV3SGFuZGxlcklkIiwiZHJvcFRhcmdldE9wdGlvbnNJbnRlcm5hbCIsInNldCIsInVuZGVmaW5lZCIsImN1cnJlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/TargetConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/isRef.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/isRef.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isRef: () => (/* binding */ isRef)\n/* harmony export */ });\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n        _typeof = function _typeof(obj) {\n            return typeof obj;\n        };\n    } else {\n        _typeof = function _typeof(obj) {\n            return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n        };\n    }\n    return _typeof(obj);\n}\nfunction isRef(obj) {\n    return obj !== null && _typeof(obj) === \"object\" && Object.prototype.hasOwnProperty.call(obj, \"current\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2ludGVybmFscy9pc1JlZi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsUUFBUUMsR0FBRztJQUFJO0lBQTJCLElBQUksT0FBT0MsV0FBVyxjQUFjLE9BQU9BLE9BQU9DLFFBQVEsS0FBSyxVQUFVO1FBQUVILFVBQVUsU0FBU0EsUUFBUUMsR0FBRztZQUFJLE9BQU8sT0FBT0E7UUFBSztJQUFHLE9BQU87UUFBRUQsVUFBVSxTQUFTQSxRQUFRQyxHQUFHO1lBQUksT0FBT0EsT0FBTyxPQUFPQyxXQUFXLGNBQWNELElBQUlHLFdBQVcsS0FBS0YsVUFBVUQsUUFBUUMsT0FBT0csU0FBUyxHQUFHLFdBQVcsT0FBT0o7UUFBSztJQUFHO0lBQUUsT0FBT0QsUUFBUUM7QUFBTTtBQUVsWCxTQUFTSyxNQUFNTCxHQUFHO0lBQ3ZCLE9BQ0VBLFFBQVEsUUFBUUQsUUFBUUMsU0FBUyxZQUFZTSxPQUFPRixTQUFTLENBQUNHLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDUixLQUFLO0FBRTNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC9kaXN0L2VzbS9pbnRlcm5hbHMvaXNSZWYuanM/OGI3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBfdHlwZW9mKG9iaikgeyBcIkBiYWJlbC9oZWxwZXJzIC0gdHlwZW9mXCI7IGlmICh0eXBlb2YgU3ltYm9sID09PSBcImZ1bmN0aW9uXCIgJiYgdHlwZW9mIFN5bWJvbC5pdGVyYXRvciA9PT0gXCJzeW1ib2xcIikgeyBfdHlwZW9mID0gZnVuY3Rpb24gX3R5cGVvZihvYmopIHsgcmV0dXJuIHR5cGVvZiBvYmo7IH07IH0gZWxzZSB7IF90eXBlb2YgPSBmdW5jdGlvbiBfdHlwZW9mKG9iaikgeyByZXR1cm4gb2JqICYmIHR5cGVvZiBTeW1ib2wgPT09IFwiZnVuY3Rpb25cIiAmJiBvYmouY29uc3RydWN0b3IgPT09IFN5bWJvbCAmJiBvYmogIT09IFN5bWJvbC5wcm90b3R5cGUgPyBcInN5bWJvbFwiIDogdHlwZW9mIG9iajsgfTsgfSByZXR1cm4gX3R5cGVvZihvYmopOyB9XG5cbmV4cG9ydCBmdW5jdGlvbiBpc1JlZihvYmopIHtcbiAgcmV0dXJuICgvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcHJvdG90eXBlLWJ1aWx0aW5zXG4gICAgb2JqICE9PSBudWxsICYmIF90eXBlb2Yob2JqKSA9PT0gJ29iamVjdCcgJiYgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG9iaiwgJ2N1cnJlbnQnKVxuICApO1xufSJdLCJuYW1lcyI6WyJfdHlwZW9mIiwib2JqIiwiU3ltYm9sIiwiaXRlcmF0b3IiLCJjb25zdHJ1Y3RvciIsInByb3RvdHlwZSIsImlzUmVmIiwiT2JqZWN0IiwiaGFzT3duUHJvcGVydHkiLCJjYWxsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/isRef.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/registration.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/registration.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerSource: () => (/* binding */ registerSource),\n/* harmony export */   registerTarget: () => (/* binding */ registerTarget)\n/* harmony export */ });\nfunction registerTarget(type, target, manager) {\n    var registry = manager.getRegistry();\n    var targetId = registry.addTarget(type, target);\n    return [\n        targetId,\n        function() {\n            return registry.removeTarget(targetId);\n        }\n    ];\n}\nfunction registerSource(type, source, manager) {\n    var registry = manager.getRegistry();\n    var sourceId = registry.addSource(type, source);\n    return [\n        sourceId,\n        function() {\n            return registry.removeSource(sourceId);\n        }\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kL2Rpc3QvZXNtL2ludGVybmFscy9yZWdpc3RyYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTyxTQUFTQSxlQUFlQyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsT0FBTztJQUNsRCxJQUFJQyxXQUFXRCxRQUFRRSxXQUFXO0lBQ2xDLElBQUlDLFdBQVdGLFNBQVNHLFNBQVMsQ0FBQ04sTUFBTUM7SUFDeEMsT0FBTztRQUFDSTtRQUFVO1lBQ2hCLE9BQU9GLFNBQVNJLFlBQVksQ0FBQ0Y7UUFDL0I7S0FBRTtBQUNKO0FBQ08sU0FBU0csZUFBZVIsSUFBSSxFQUFFUyxNQUFNLEVBQUVQLE9BQU87SUFDbEQsSUFBSUMsV0FBV0QsUUFBUUUsV0FBVztJQUNsQyxJQUFJTSxXQUFXUCxTQUFTUSxTQUFTLENBQUNYLE1BQU1TO0lBQ3hDLE9BQU87UUFBQ0M7UUFBVTtZQUNoQixPQUFPUCxTQUFTUyxZQUFZLENBQUNGO1FBQy9CO0tBQUU7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQvZGlzdC9lc20vaW50ZXJuYWxzL3JlZ2lzdHJhdGlvbi5qcz82YmY2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiByZWdpc3RlclRhcmdldCh0eXBlLCB0YXJnZXQsIG1hbmFnZXIpIHtcbiAgdmFyIHJlZ2lzdHJ5ID0gbWFuYWdlci5nZXRSZWdpc3RyeSgpO1xuICB2YXIgdGFyZ2V0SWQgPSByZWdpc3RyeS5hZGRUYXJnZXQodHlwZSwgdGFyZ2V0KTtcbiAgcmV0dXJuIFt0YXJnZXRJZCwgZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiByZWdpc3RyeS5yZW1vdmVUYXJnZXQodGFyZ2V0SWQpO1xuICB9XTtcbn1cbmV4cG9ydCBmdW5jdGlvbiByZWdpc3RlclNvdXJjZSh0eXBlLCBzb3VyY2UsIG1hbmFnZXIpIHtcbiAgdmFyIHJlZ2lzdHJ5ID0gbWFuYWdlci5nZXRSZWdpc3RyeSgpO1xuICB2YXIgc291cmNlSWQgPSByZWdpc3RyeS5hZGRTb3VyY2UodHlwZSwgc291cmNlKTtcbiAgcmV0dXJuIFtzb3VyY2VJZCwgZnVuY3Rpb24gKCkge1xuICAgIHJldHVybiByZWdpc3RyeS5yZW1vdmVTb3VyY2Uoc291cmNlSWQpO1xuICB9XTtcbn0iXSwibmFtZXMiOlsicmVnaXN0ZXJUYXJnZXQiLCJ0eXBlIiwidGFyZ2V0IiwibWFuYWdlciIsInJlZ2lzdHJ5IiwiZ2V0UmVnaXN0cnkiLCJ0YXJnZXRJZCIsImFkZFRhcmdldCIsInJlbW92ZVRhcmdldCIsInJlZ2lzdGVyU291cmNlIiwic291cmNlIiwic291cmNlSWQiLCJhZGRTb3VyY2UiLCJyZW1vdmVTb3VyY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/registration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd/dist/esm/internals/wrapConnectorHooks.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-dnd/dist/esm/internals/wrapConnectorHooks.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wrapConnectorHooks: () => (/* binding */ wrapConnectorHooks)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction throwIfCompositeComponentElement(element) {\n    // Custom components can no longer be wrapped directly in React DnD 2.0\n    // so that we don't need to depend on findDOMNode() from react-dom.\n    if (typeof element.type === \"string\") {\n        return;\n    }\n    var displayName = element.type.displayName || element.type.name || \"the component\";\n    throw new Error(\"Only native element nodes can now be passed to React DnD connectors.\" + \"You can either wrap \".concat(displayName, \" into a <div>, or turn it into a \") + \"drag source or a drop target itself.\");\n}\nfunction wrapHookToRecognizeElement(hook) {\n    return function() {\n        var elementOrNode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n        // When passed a node, call the hook straight away.\n        if (!/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.isValidElement)(elementOrNode)) {\n            var node = elementOrNode;\n            hook(node, options); // return the node so it can be chained (e.g. when within callback refs\n            // <div ref={node => connectDragSource(connectDropTarget(node))}/>\n            return node;\n        } // If passed a ReactElement, clone it and attach this function as a ref.\n        // This helps us achieve a neat API where user doesn't even know that refs\n        // are being used under the hood.\n        var element = elementOrNode;\n        throwIfCompositeComponentElement(element); // When no options are passed, use the hook directly\n        var ref = options ? function(node) {\n            return hook(node, options);\n        } : hook;\n        return cloneWithRef(element, ref);\n    };\n}\nfunction wrapConnectorHooks(hooks) {\n    var wrappedHooks = {};\n    Object.keys(hooks).forEach(function(key) {\n        var hook = hooks[key]; // ref objects should be passed straight through without wrapping\n        if (key.endsWith(\"Ref\")) {\n            wrappedHooks[key] = hooks[key];\n        } else {\n            var wrappedHook = wrapHookToRecognizeElement(hook);\n            wrappedHooks[key] = function() {\n                return wrappedHook;\n            };\n        }\n    });\n    return wrappedHooks;\n}\nfunction setRef(ref, node) {\n    if (typeof ref === \"function\") {\n        ref(node);\n    } else {\n        ref.current = node;\n    }\n}\nfunction cloneWithRef(element, newRef) {\n    var previousRef = element.ref;\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof previousRef !== \"string\", \"Cannot connect React DnD to an element with an existing string ref. \" + \"Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. \" + \"Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs\");\n    if (!previousRef) {\n        // When there is no ref on the element, use the new ref directly\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(element, {\n            ref: newRef\n        });\n    } else {\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.cloneElement)(element, {\n            ref: function ref(node) {\n                setRef(previousRef, node);\n                setRef(newRef, node);\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd/dist/esm/internals/wrapConnectorHooks.js\n");

/***/ })

};
;