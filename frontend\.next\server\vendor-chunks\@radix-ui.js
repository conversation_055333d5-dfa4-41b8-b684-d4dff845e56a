"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNENBQTRDO0FBQzVDLFNBQVNBLHFCQUFxQkMsb0JBQW9CLEVBQUVDLGVBQWUsRUFBRSxFQUFFQywyQkFBMkIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQzNHLE9BQU8sU0FBU0MsWUFBWUMsS0FBSztRQUMvQkosdUJBQXVCSTtRQUN2QixJQUFJRiw2QkFBNkIsU0FBUyxDQUFDRSxNQUFNQyxnQkFBZ0IsRUFBRTtZQUNqRSxPQUFPSixrQkFBa0JHO1FBQzNCO0lBQ0Y7QUFDRjtBQUdFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/MTg2OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJjb21wb3NlRXZlbnRIYW5kbGVycyIsIm9yaWdpbmFsRXZlbnRIYW5kbGVyIiwib3VyRXZlbnRIYW5kbGVyIiwiY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkIiwiaGFuZGxlRXZlbnQiLCJldmVudCIsImRlZmF1bHRQcmV2ZW50ZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLGdEQUFnRDtBQUNqQjtBQUNTO0FBQ3hDLFNBQVNFLGVBQWVDLGlCQUFpQixFQUFFQyxjQUFjO0lBQ3ZELE1BQU1DLHdCQUFVTCxnREFBbUIsQ0FBQ0k7SUFDcEMsTUFBTUcsV0FBVyxDQUFDQztRQUNoQixNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxTQUFTLEdBQUdGO1FBQ2pDLE1BQU1HLFFBQVFYLDBDQUFhLENBQUMsSUFBTVUsU0FBU0csT0FBT0MsTUFBTSxDQUFDSjtRQUN6RCxPQUFPLGFBQWEsR0FBR1Qsc0RBQUdBLENBQUNJLFFBQVFFLFFBQVEsRUFBRTtZQUFFSTtZQUFPRjtRQUFTO0lBQ2pFO0lBQ0FGLFNBQVNRLFdBQVcsR0FBR1osb0JBQW9CO0lBQzNDLFNBQVNhLFlBQVlDLFlBQVk7UUFDL0IsTUFBTVAsVUFBVVYsNkNBQWdCLENBQUNLO1FBQ2pDLElBQUlLLFNBQVMsT0FBT0E7UUFDcEIsSUFBSU4sbUJBQW1CLEtBQUssR0FBRyxPQUFPQTtRQUN0QyxNQUFNLElBQUllLE1BQU0sQ0FBQyxFQUFFLEVBQUVGLGFBQWEseUJBQXlCLEVBQUVkLGtCQUFrQixFQUFFLENBQUM7SUFDcEY7SUFDQSxPQUFPO1FBQUNJO1FBQVVTO0tBQVk7QUFDaEM7QUFDQSxTQUFTSSxtQkFBbUJDLFNBQVMsRUFBRUMseUJBQXlCLEVBQUU7SUFDaEUsSUFBSUMsa0JBQWtCLEVBQUU7SUFDeEIsU0FBU0MsZUFBZXJCLGlCQUFpQixFQUFFQyxjQUFjO1FBQ3ZELE1BQU1xQiw0QkFBY3pCLGdEQUFtQixDQUFDSTtRQUN4QyxNQUFNc0IsUUFBUUgsZ0JBQWdCSSxNQUFNO1FBQ3BDSixrQkFBa0I7ZUFBSUE7WUFBaUJuQjtTQUFlO1FBQ3RELE1BQU1HLFdBQVcsQ0FBQ0M7WUFDaEIsTUFBTSxFQUFFb0IsS0FBSyxFQUFFbkIsUUFBUSxFQUFFLEdBQUdDLFNBQVMsR0FBR0Y7WUFDeEMsTUFBTUgsVUFBVXVCLE9BQU8sQ0FBQ1AsVUFBVSxFQUFFLENBQUNLLE1BQU0sSUFBSUQ7WUFDL0MsTUFBTWQsUUFBUVgsMENBQWEsQ0FBQyxJQUFNVSxTQUFTRyxPQUFPQyxNQUFNLENBQUNKO1lBQ3pELE9BQU8sYUFBYSxHQUFHVCxzREFBR0EsQ0FBQ0ksUUFBUUUsUUFBUSxFQUFFO2dCQUFFSTtnQkFBT0Y7WUFBUztRQUNqRTtRQUNBRixTQUFTUSxXQUFXLEdBQUdaLG9CQUFvQjtRQUMzQyxTQUFTYSxZQUFZQyxZQUFZLEVBQUVXLEtBQUs7WUFDdEMsTUFBTXZCLFVBQVV1QixPQUFPLENBQUNQLFVBQVUsRUFBRSxDQUFDSyxNQUFNLElBQUlEO1lBQy9DLE1BQU1mLFVBQVVWLDZDQUFnQixDQUFDSztZQUNqQyxJQUFJSyxTQUFTLE9BQU9BO1lBQ3BCLElBQUlOLG1CQUFtQixLQUFLLEdBQUcsT0FBT0E7WUFDdEMsTUFBTSxJQUFJZSxNQUFNLENBQUMsRUFBRSxFQUFFRixhQUFhLHlCQUF5QixFQUFFZCxrQkFBa0IsRUFBRSxDQUFDO1FBQ3BGO1FBQ0EsT0FBTztZQUFDSTtZQUFVUztTQUFZO0lBQ2hDO0lBQ0EsTUFBTWEsY0FBYztRQUNsQixNQUFNQyxnQkFBZ0JQLGdCQUFnQlEsR0FBRyxDQUFDLENBQUMzQjtZQUN6QyxxQkFBT0osZ0RBQW1CLENBQUNJO1FBQzdCO1FBQ0EsT0FBTyxTQUFTNEIsU0FBU0osS0FBSztZQUM1QixNQUFNSyxXQUFXTCxPQUFPLENBQUNQLFVBQVUsSUFBSVM7WUFDdkMsT0FBTzlCLDBDQUFhLENBQ2xCLElBQU87b0JBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRXFCLFVBQVUsQ0FBQyxDQUFDLEVBQUU7d0JBQUUsR0FBR08sS0FBSzt3QkFBRSxDQUFDUCxVQUFVLEVBQUVZO29CQUFTO2dCQUFFLElBQ3RFO2dCQUFDTDtnQkFBT0s7YUFBUztRQUVyQjtJQUNGO0lBQ0FKLFlBQVlSLFNBQVMsR0FBR0E7SUFDeEIsT0FBTztRQUFDRztRQUFnQlUscUJBQXFCTCxnQkFBZ0JQO0tBQXdCO0FBQ3ZGO0FBQ0EsU0FBU1kscUJBQXFCLEdBQUdDLE1BQU07SUFDckMsTUFBTUMsWUFBWUQsTUFBTSxDQUFDLEVBQUU7SUFDM0IsSUFBSUEsT0FBT1IsTUFBTSxLQUFLLEdBQUcsT0FBT1M7SUFDaEMsTUFBTVAsY0FBYztRQUNsQixNQUFNUSxhQUFhRixPQUFPSixHQUFHLENBQUMsQ0FBQ08sZUFBa0I7Z0JBQy9DTixVQUFVTTtnQkFDVmpCLFdBQVdpQixhQUFhakIsU0FBUztZQUNuQztRQUNBLE9BQU8sU0FBU2tCLGtCQUFrQkMsY0FBYztZQUM5QyxNQUFNQyxhQUFhSixXQUFXSyxNQUFNLENBQUMsQ0FBQ0MsYUFBYSxFQUFFWCxRQUFRLEVBQUVYLFNBQVMsRUFBRTtnQkFDeEUsTUFBTXVCLGFBQWFaLFNBQVNRO2dCQUM1QixNQUFNSyxlQUFlRCxVQUFVLENBQUMsQ0FBQyxPQUFPLEVBQUV2QixVQUFVLENBQUMsQ0FBQztnQkFDdEQsT0FBTztvQkFBRSxHQUFHc0IsV0FBVztvQkFBRSxHQUFHRSxZQUFZO2dCQUFDO1lBQzNDLEdBQUcsQ0FBQztZQUNKLE9BQU83QywwQ0FBYSxDQUFDLElBQU87b0JBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRW9DLFVBQVVmLFNBQVMsQ0FBQyxDQUFDLENBQUMsRUFBRW9CO2dCQUFXLElBQUk7Z0JBQUNBO2FBQVc7UUFDOUY7SUFDRjtJQUNBWixZQUFZUixTQUFTLEdBQUdlLFVBQVVmLFNBQVM7SUFDM0MsT0FBT1E7QUFDVDtBQUlFLENBQ0Ysa0NBQWtDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb250ZXh0L2Rpc3QvaW5kZXgubWpzPzEwODEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvY29udGV4dC9zcmMvY3JlYXRlLWNvbnRleHQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZnVuY3Rpb24gY3JlYXRlQ29udGV4dDIocm9vdENvbXBvbmVudE5hbWUsIGRlZmF1bHRDb250ZXh0KSB7XG4gIGNvbnN0IENvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGRlZmF1bHRDb250ZXh0KTtcbiAgY29uc3QgUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICBjb25zdCB7IGNoaWxkcmVuLCAuLi5jb250ZXh0IH0gPSBwcm9wcztcbiAgICBjb25zdCB2YWx1ZSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gY29udGV4dCwgT2JqZWN0LnZhbHVlcyhjb250ZXh0KSk7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZSwgY2hpbGRyZW4gfSk7XG4gIH07XG4gIFByb3ZpZGVyLmRpc3BsYXlOYW1lID0gcm9vdENvbXBvbmVudE5hbWUgKyBcIlByb3ZpZGVyXCI7XG4gIGZ1bmN0aW9uIHVzZUNvbnRleHQyKGNvbnN1bWVyTmFtZSkge1xuICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KENvbnRleHQpO1xuICAgIGlmIChjb250ZXh0KSByZXR1cm4gY29udGV4dDtcbiAgICBpZiAoZGVmYXVsdENvbnRleHQgIT09IHZvaWQgMCkgcmV0dXJuIGRlZmF1bHRDb250ZXh0O1xuICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtjb25zdW1lck5hbWV9XFxgIG11c3QgYmUgdXNlZCB3aXRoaW4gXFxgJHtyb290Q29tcG9uZW50TmFtZX1cXGBgKTtcbiAgfVxuICByZXR1cm4gW1Byb3ZpZGVyLCB1c2VDb250ZXh0Ml07XG59XG5mdW5jdGlvbiBjcmVhdGVDb250ZXh0U2NvcGUoc2NvcGVOYW1lLCBjcmVhdGVDb250ZXh0U2NvcGVEZXBzID0gW10pIHtcbiAgbGV0IGRlZmF1bHRDb250ZXh0cyA9IFtdO1xuICBmdW5jdGlvbiBjcmVhdGVDb250ZXh0Myhyb290Q29tcG9uZW50TmFtZSwgZGVmYXVsdENvbnRleHQpIHtcbiAgICBjb25zdCBCYXNlQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpO1xuICAgIGNvbnN0IGluZGV4ID0gZGVmYXVsdENvbnRleHRzLmxlbmd0aDtcbiAgICBkZWZhdWx0Q29udGV4dHMgPSBbLi4uZGVmYXVsdENvbnRleHRzLCBkZWZhdWx0Q29udGV4dF07XG4gICAgY29uc3QgUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICAgIGNvbnN0IHsgc2NvcGUsIGNoaWxkcmVuLCAuLi5jb250ZXh0IH0gPSBwcm9wcztcbiAgICAgIGNvbnN0IENvbnRleHQgPSBzY29wZT8uW3Njb3BlTmFtZV0/LltpbmRleF0gfHwgQmFzZUNvbnRleHQ7XG4gICAgICBjb25zdCB2YWx1ZSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4gY29udGV4dCwgT2JqZWN0LnZhbHVlcyhjb250ZXh0KSk7XG4gICAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlLCBjaGlsZHJlbiB9KTtcbiAgICB9O1xuICAgIFByb3ZpZGVyLmRpc3BsYXlOYW1lID0gcm9vdENvbXBvbmVudE5hbWUgKyBcIlByb3ZpZGVyXCI7XG4gICAgZnVuY3Rpb24gdXNlQ29udGV4dDIoY29uc3VtZXJOYW1lLCBzY29wZSkge1xuICAgICAgY29uc3QgQ29udGV4dCA9IHNjb3BlPy5bc2NvcGVOYW1lXT8uW2luZGV4XSB8fCBCYXNlQ29udGV4dDtcbiAgICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KENvbnRleHQpO1xuICAgICAgaWYgKGNvbnRleHQpIHJldHVybiBjb250ZXh0O1xuICAgICAgaWYgKGRlZmF1bHRDb250ZXh0ICE9PSB2b2lkIDApIHJldHVybiBkZWZhdWx0Q29udGV4dDtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgXFxgJHtjb25zdW1lck5hbWV9XFxgIG11c3QgYmUgdXNlZCB3aXRoaW4gXFxgJHtyb290Q29tcG9uZW50TmFtZX1cXGBgKTtcbiAgICB9XG4gICAgcmV0dXJuIFtQcm92aWRlciwgdXNlQ29udGV4dDJdO1xuICB9XG4gIGNvbnN0IGNyZWF0ZVNjb3BlID0gKCkgPT4ge1xuICAgIGNvbnN0IHNjb3BlQ29udGV4dHMgPSBkZWZhdWx0Q29udGV4dHMubWFwKChkZWZhdWx0Q29udGV4dCkgPT4ge1xuICAgICAgcmV0dXJuIFJlYWN0LmNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpO1xuICAgIH0pO1xuICAgIHJldHVybiBmdW5jdGlvbiB1c2VTY29wZShzY29wZSkge1xuICAgICAgY29uc3QgY29udGV4dHMgPSBzY29wZT8uW3Njb3BlTmFtZV0gfHwgc2NvcGVDb250ZXh0cztcbiAgICAgIHJldHVybiBSZWFjdC51c2VNZW1vKFxuICAgICAgICAoKSA9PiAoeyBbYF9fc2NvcGUke3Njb3BlTmFtZX1gXTogeyAuLi5zY29wZSwgW3Njb3BlTmFtZV06IGNvbnRleHRzIH0gfSksXG4gICAgICAgIFtzY29wZSwgY29udGV4dHNdXG4gICAgICApO1xuICAgIH07XG4gIH07XG4gIGNyZWF0ZVNjb3BlLnNjb3BlTmFtZSA9IHNjb3BlTmFtZTtcbiAgcmV0dXJuIFtjcmVhdGVDb250ZXh0MywgY29tcG9zZUNvbnRleHRTY29wZXMoY3JlYXRlU2NvcGUsIC4uLmNyZWF0ZUNvbnRleHRTY29wZURlcHMpXTtcbn1cbmZ1bmN0aW9uIGNvbXBvc2VDb250ZXh0U2NvcGVzKC4uLnNjb3Blcykge1xuICBjb25zdCBiYXNlU2NvcGUgPSBzY29wZXNbMF07XG4gIGlmIChzY29wZXMubGVuZ3RoID09PSAxKSByZXR1cm4gYmFzZVNjb3BlO1xuICBjb25zdCBjcmVhdGVTY29wZSA9ICgpID0+IHtcbiAgICBjb25zdCBzY29wZUhvb2tzID0gc2NvcGVzLm1hcCgoY3JlYXRlU2NvcGUyKSA9PiAoe1xuICAgICAgdXNlU2NvcGU6IGNyZWF0ZVNjb3BlMigpLFxuICAgICAgc2NvcGVOYW1lOiBjcmVhdGVTY29wZTIuc2NvcGVOYW1lXG4gICAgfSkpO1xuICAgIHJldHVybiBmdW5jdGlvbiB1c2VDb21wb3NlZFNjb3BlcyhvdmVycmlkZVNjb3Blcykge1xuICAgICAgY29uc3QgbmV4dFNjb3BlcyA9IHNjb3BlSG9va3MucmVkdWNlKChuZXh0U2NvcGVzMiwgeyB1c2VTY29wZSwgc2NvcGVOYW1lIH0pID0+IHtcbiAgICAgICAgY29uc3Qgc2NvcGVQcm9wcyA9IHVzZVNjb3BlKG92ZXJyaWRlU2NvcGVzKTtcbiAgICAgICAgY29uc3QgY3VycmVudFNjb3BlID0gc2NvcGVQcm9wc1tgX19zY29wZSR7c2NvcGVOYW1lfWBdO1xuICAgICAgICByZXR1cm4geyAuLi5uZXh0U2NvcGVzMiwgLi4uY3VycmVudFNjb3BlIH07XG4gICAgICB9LCB7fSk7XG4gICAgICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoeyBbYF9fc2NvcGUke2Jhc2VTY29wZS5zY29wZU5hbWV9YF06IG5leHRTY29wZXMgfSksIFtuZXh0U2NvcGVzXSk7XG4gICAgfTtcbiAgfTtcbiAgY3JlYXRlU2NvcGUuc2NvcGVOYW1lID0gYmFzZVNjb3BlLnNjb3BlTmFtZTtcbiAgcmV0dXJuIGNyZWF0ZVNjb3BlO1xufVxuZXhwb3J0IHtcbiAgY3JlYXRlQ29udGV4dDIgYXMgY3JlYXRlQ29udGV4dCxcbiAgY3JlYXRlQ29udGV4dFNjb3BlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwianN4IiwiY3JlYXRlQ29udGV4dDIiLCJyb290Q29tcG9uZW50TmFtZSIsImRlZmF1bHRDb250ZXh0IiwiQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJQcm92aWRlciIsInByb3BzIiwiY2hpbGRyZW4iLCJjb250ZXh0IiwidmFsdWUiLCJ1c2VNZW1vIiwiT2JqZWN0IiwidmFsdWVzIiwiZGlzcGxheU5hbWUiLCJ1c2VDb250ZXh0MiIsImNvbnN1bWVyTmFtZSIsInVzZUNvbnRleHQiLCJFcnJvciIsImNyZWF0ZUNvbnRleHRTY29wZSIsInNjb3BlTmFtZSIsImNyZWF0ZUNvbnRleHRTY29wZURlcHMiLCJkZWZhdWx0Q29udGV4dHMiLCJjcmVhdGVDb250ZXh0MyIsIkJhc2VDb250ZXh0IiwiaW5kZXgiLCJsZW5ndGgiLCJzY29wZSIsImNyZWF0ZVNjb3BlIiwic2NvcGVDb250ZXh0cyIsIm1hcCIsInVzZVNjb3BlIiwiY29udGV4dHMiLCJjb21wb3NlQ29udGV4dFNjb3BlcyIsInNjb3BlcyIsImJhc2VTY29wZSIsInNjb3BlSG9va3MiLCJjcmVhdGVTY29wZTIiLCJ1c2VDb21wb3NlZFNjb3BlcyIsIm92ZXJyaWRlU2NvcGVzIiwibmV4dFNjb3BlcyIsInJlZHVjZSIsIm5leHRTY29wZXMyIiwic2NvcGVQcm9wcyIsImN1cnJlbnRTY29wZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const childrenRef = getElementRef(children);\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyREFBMkQ7QUFDNUI7QUFDL0IsU0FBU0MsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxjQUFjSCx5Q0FBWSxDQUFDRTtJQUNqQ0YsNENBQWUsQ0FBQztRQUNkRyxZQUFZRyxPQUFPLEdBQUdKO0lBQ3hCO0lBQ0EsT0FBT0YsMENBQWEsQ0FBQyxJQUFNLENBQUMsR0FBR1EsT0FBU0wsWUFBWUcsT0FBTyxNQUFNRSxPQUFPLEVBQUU7QUFDNUU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcz8xMWYwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZS1jYWxsYmFjay1yZWYudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNhbGxiYWNrUmVmKGNhbGxiYWNrKSB7XG4gIGNvbnN0IGNhbGxiYWNrUmVmID0gUmVhY3QudXNlUmVmKGNhbGxiYWNrKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gY2FsbGJhY2tSZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VDYWxsYmFja1JlZlxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUNhbGxiYWNrUmVmIiwiY2FsbGJhY2siLCJjYWxsYmFja1JlZiIsInVzZVJlZiIsInVzZUVmZmVjdCIsImN1cnJlbnQiLCJ1c2VNZW1vIiwiYXJncyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{}, caller }) {\n    const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== void 0;\n    const value = isControlled ? prop : uncontrolledProp;\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n            if (value2 !== prop) {\n                onChangeRef.current?.(value2);\n            }\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        onChangeRef\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    useInsertionEffect(()=>{\n        onChangeRef.current = onChange;\n    }, [\n        onChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            onChangeRef.current?.(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef\n    ]);\n    return [\n        value,\n        setValue,\n        onChangeRef\n    ];\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n    const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n    const isControlled = controlledState !== void 0;\n    const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const args = [\n        {\n            ...initialArg,\n            state: defaultProp\n        }\n    ];\n    if (init) {\n        args.push(init);\n    }\n    const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action)=>{\n        if (action.type === SYNC_STATE) {\n            return {\n                ...state2,\n                state: action.state\n            };\n        }\n        const next = reducer(state2, action);\n        if (isControlled && !Object.is(next.state, state2.state)) {\n            onChange(next.state);\n        }\n        return next;\n    }, ...args);\n    const uncontrolledState = internalState.state;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== uncontrolledState) {\n            prevValueRef.current = uncontrolledState;\n            if (!isControlled) {\n                onChange(uncontrolledState);\n            }\n        }\n    }, [\n        onChange,\n        uncontrolledState,\n        prevValueRef,\n        isControlled\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const isControlled2 = controlledState !== void 0;\n        if (isControlled2) {\n            return {\n                ...internalState,\n                state: controlledState\n            };\n        }\n        return internalState;\n    }, [\n        internalState,\n        controlledState\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isControlled && !Object.is(controlledState, internalState.state)) {\n            dispatch({\n                type: SYNC_STATE,\n                state: controlledState\n            });\n        }\n    }, [\n        controlledState,\n        internalState.state,\n        isControlled\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n    if (typeof useReactEffectEvent === \"function\") {\n        return useReactEffectEvent(callback);\n    }\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n        throw new Error(\"Cannot call an event handler while rendering.\");\n    });\n    if (typeof useReactInsertionEffect === \"function\") {\n        useReactInsertionEffect(()=>{\n            ref.current = callback;\n        });\n    } else {\n        (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n            ref.current = callback;\n        });\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>ref.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n    const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\") {\n                onEscapeKeyDown(event);\n            }\n        };\n        ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n    }, [\n        onEscapeKeyDown,\n        ownerDocument\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QvZGlzdC9pbmRleC5tanM/MmQ2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlLWxheW91dC1lZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VMYXlvdXRFZmZlY3QyIiwiZ2xvYmFsVGhpcyIsImRvY3VtZW50IiwidXNlTGF5b3V0RWZmZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n    // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n    position: \"absolute\",\n    border: 0,\n    width: 1,\n    height: 1,\n    padding: 0,\n    margin: -1,\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span, {\n        ...props,\n        ref: forwardedRef,\n        style: {\n            ...VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        }\n    });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;