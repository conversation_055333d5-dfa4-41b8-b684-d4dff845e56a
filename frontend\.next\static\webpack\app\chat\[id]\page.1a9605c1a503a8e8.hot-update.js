"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/file-explorer.tsx":
/*!**************************************!*\
  !*** ./components/file-explorer.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileExplorer: function() { return /* binding */ FileExplorer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _custom_file_tree__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./custom-file-tree */ \"(app-pages-browser)/./components/custom-file-tree.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Folder,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Folder,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Folder,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ FileExplorer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FileExplorer(param) {\n    let { version } = param;\n    _s();\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (version) {\n            loadFiles();\n        } else {\n            setFiles([]);\n            setTreeData([]);\n            setSelectedFile(null);\n        }\n    }, [\n        version\n    ]);\n    const loadFiles = async ()=>{\n        if (!version) return;\n        setIsLoading(true);\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getVersionPreview(version.chat_id, version.id);\n            setFiles(data.files || []);\n            buildFileTree(data.files || []);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load files\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const buildFileTree = (files)=>{\n        const nodeMap = new Map();\n        const rootNodes = [];\n        // Sort files by path\n        const sortedFiles = files.sort((a, b)=>a.path.localeCompare(b.path));\n        for (const file of sortedFiles){\n            const pathParts = file.path.split(\"/\").filter(Boolean);\n            let currentPath = \"\";\n            for(let i = 0; i < pathParts.length; i++){\n                const part = pathParts[i];\n                const parentPath = currentPath;\n                currentPath = currentPath ? \"\".concat(currentPath, \"/\").concat(part) : part;\n                const isFile = i === pathParts.length - 1;\n                if (!nodeMap.has(currentPath)) {\n                    const newNode = {\n                        id: currentPath,\n                        name: part,\n                        type: isFile ? \"file\" : \"folder\",\n                        path: currentPath,\n                        children: isFile ? undefined : [],\n                        file: isFile ? file : undefined\n                    };\n                    if (parentPath === \"\") {\n                        // Root level\n                        rootNodes.push(newNode);\n                    } else {\n                        const parentNode = nodeMap.get(parentPath);\n                        if (parentNode && parentNode.children) {\n                            parentNode.children.push(newNode);\n                        }\n                    }\n                    nodeMap.set(currentPath, newNode);\n                }\n            }\n        }\n        setTreeData(rootNodes);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n                return \"javascript\";\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n                return \"javascript\";\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"java\":\n                return \"java\";\n            case \"cpp\":\n            case \"c\":\n                return \"cpp\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    if (!version) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center text-muted-foreground\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No version selected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-[#0d1117]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 border-b border-[#21262d]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-sm text-[#f0f6fc] flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"app\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc] rounded\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 16 16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-2\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-6 h-6 animate-spin text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this) : !treeData || treeData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-[#7d8590]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"No files yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_custom_file_tree__WEBPACK_IMPORTED_MODULE_4__.CustomFileTree, {\n                        data: treeData,\n                        onFileSelect: handleFileSelect,\n                        selectedPath: selectedFile === null || selectedFile === void 0 ? void 0 : selectedFile.path\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(FileExplorer, \"rWgTg2L5GgEIVJeU5qp2Tp8/9d0=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = FileExplorer;\nvar _c;\n$RefreshReg$(_c, \"FileExplorer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/file-explorer.tsx\n"));

/***/ })

});