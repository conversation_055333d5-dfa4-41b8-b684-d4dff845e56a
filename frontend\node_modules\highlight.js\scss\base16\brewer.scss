pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Brewer
  Author: <PERSON><PERSON><PERSON> (http://github.com/tpoisot)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme brewer
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #0c0d0e  Default Background
base01  #2e2f30  Lighter Background (Used for status bars, line number and folding marks)
base02  #515253  Selection Background
base03  #737475  Comments, Invisibles, Line Highlighting
base04  #959697  Dark Foreground (Used for status bars)
base05  #b7b8b9  Default Foreground, Caret, Delimiters, Operators
base06  #dadbdc  Light Foreground (Not often used)
base07  #fcfdfe  Light Background (Not often used)
base08  #e31a1c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #e6550d  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #dca060  Classes, Markup Bold, Search Text Background
base0B  #31a354  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #80b1d3  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #3182bd  Functions, Methods, Attribute IDs, Headings
base0E  #756bb1  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b15928  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #b7b8b9;
  background: #0c0d0e
}
.hljs::selection,
.hljs ::selection {
  background-color: #515253;
  color: #b7b8b9
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #737475 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #737475
}
/* base04 - #959697 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #959697
}
/* base05 - #b7b8b9 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #b7b8b9
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e31a1c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #e6550d
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #dca060
}
.hljs-strong {
  font-weight: bold;
  color: #dca060
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #31a354
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #80b1d3
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #3182bd
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #756bb1
}
.hljs-emphasis {
  color: #756bb1;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b15928
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}