"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/es-define-property";
exports.ids = ["vendor-chunks/es-define-property"];
exports.modules = {

/***/ "(ssr)/./node_modules/es-define-property/index.js":
/*!**************************************************!*\
  !*** ./node_modules/es-define-property/index.js ***!
  \**************************************************/
/***/ ((module) => {

eval("\n/** @type {import('.')} */ var $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n    try {\n        $defineProperty({}, \"a\", {\n            value: 1\n        });\n    } catch (e) {\n        // IE 8 has a broken defineProperty\n        $defineProperty = false;\n    }\n}\nmodule.exports = $defineProperty;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZXMtZGVmaW5lLXByb3BlcnR5L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsd0JBQXdCLEdBQ3hCLElBQUlBLGtCQUFrQkMsT0FBT0MsY0FBYyxJQUFJO0FBQy9DLElBQUlGLGlCQUFpQjtJQUNwQixJQUFJO1FBQ0hBLGdCQUFnQixDQUFDLEdBQUcsS0FBSztZQUFFRyxPQUFPO1FBQUU7SUFDckMsRUFBRSxPQUFPQyxHQUFHO1FBQ1gsbUNBQW1DO1FBQ25DSixrQkFBa0I7SUFDbkI7QUFDRDtBQUVBSyxPQUFPQyxPQUFPLEdBQUdOIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2VzLWRlZmluZS1wcm9wZXJ0eS9pbmRleC5qcz9iMGIzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqIEB0eXBlIHtpbXBvcnQoJy4nKX0gKi9cbnZhciAkZGVmaW5lUHJvcGVydHkgPSBPYmplY3QuZGVmaW5lUHJvcGVydHkgfHwgZmFsc2U7XG5pZiAoJGRlZmluZVByb3BlcnR5KSB7XG5cdHRyeSB7XG5cdFx0JGRlZmluZVByb3BlcnR5KHt9LCAnYScsIHsgdmFsdWU6IDEgfSk7XG5cdH0gY2F0Y2ggKGUpIHtcblx0XHQvLyBJRSA4IGhhcyBhIGJyb2tlbiBkZWZpbmVQcm9wZXJ0eVxuXHRcdCRkZWZpbmVQcm9wZXJ0eSA9IGZhbHNlO1xuXHR9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gJGRlZmluZVByb3BlcnR5O1xuIl0sIm5hbWVzIjpbIiRkZWZpbmVQcm9wZXJ0eSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwidmFsdWUiLCJlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/es-define-property/index.js\n");

/***/ })

};
;