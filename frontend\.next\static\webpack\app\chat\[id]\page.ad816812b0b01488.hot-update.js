"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(app-pages-browser)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(app-pages-browser)/./components/website-preview.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage(param) {\n    let { params } = param;\n    var _currentVersion_id;\n    _s();\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return \"javascript\";\n            case \"ts\":\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    if (authLoading || !user && !authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: chat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (currentVersion === null || currentVersion === void 0 ? void 0 : (_currentVersion_id = currentVersion.id) === null || _currentVersion_id === void 0 ? void 0 : _currentVersion_id.toString()) || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id.toString() === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id.toString(),\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 bg-[#0d1117] border-r border-[#21262d]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                    version: currentVersion,\n                                    onFileSelect: handleFileSelect\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-[#0d1117]\",\n                                children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                    version: currentVersion\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center px-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-[#7d8590]\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#f0f6fc] text-sm font-medium\",\n                                                        children: selectedFile ? selectedFile.path.split(\"/\").pop() : \"No file selected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                height: \"100%\",\n                                                language: getFileLanguage(selectedFile.path),\n                                                value: selectedFile.content || \"// No content available\",\n                                                theme: \"vs-dark\",\n                                                options: {\n                                                    readOnly: true,\n                                                    minimap: {\n                                                        enabled: false\n                                                    },\n                                                    scrollBeyondLastLine: false,\n                                                    fontSize: 14,\n                                                    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n                                                    lineNumbers: \"on\",\n                                                    glyphMargin: false,\n                                                    folding: true,\n                                                    lineDecorationsWidth: 0,\n                                                    lineNumbersMinChars: 3,\n                                                    renderLineHighlight: \"line\",\n                                                    selectOnLineNumbers: true,\n                                                    wordWrap: \"on\",\n                                                    automaticLayout: true\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 mx-auto mb-4 text-[#7d8590]\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-[#f0f6fc] mb-2\",\n                                                            children: \"Code Editor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[#7d8590]\",\n                                                            children: \"Select a file from the explorer to view its content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"851n8SFiz3SRoLlPT4KA3In4dKg=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chat/[id]/page.tsx\n"));

/***/ })

});