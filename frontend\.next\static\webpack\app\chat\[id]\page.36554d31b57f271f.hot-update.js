"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(app-pages-browser)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(app-pages-browser)/./components/website-preview.tsx\");\n/* harmony import */ var react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-resizable-panels */ \"(app-pages-browser)/./node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage(param) {\n    let { params } = param;\n    _s();\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return \"javascript\";\n            case \"ts\":\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    if (authLoading || !user && !authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (currentVersion === null || currentVersion === void 0 ? void 0 : currentVersion.id) || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id,\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelGroup, {\n                            direction: \"horizontal\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 35,\n                                    minSize: 25,\n                                    maxSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                            version: currentVersion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelResizeHandle, {\n                                    className: \"w-1 bg-[#21262d] hover:bg-[#0969da] transition-colors cursor-col-resize\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 65,\n                                    minSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                            version: currentVersion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full bg-[#0d1117] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-16 h-16 mx-auto mb-4 text-[#7d8590]\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-[#f0f6fc] mb-2\",\n                                                        children: \"Code Editor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-[#7d8590]\",\n                                                        children: \"Select a file from the explorer to view its content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"851n8SFiz3SRoLlPT4KA3In4dKg=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chat/[id]/page.tsx\n"));

/***/ })

});