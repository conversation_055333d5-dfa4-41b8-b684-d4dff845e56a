"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(app-pages-browser)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(app-pages-browser)/./components/website-preview.tsx\");\n/* harmony import */ var react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-resizable-panels */ \"(app-pages-browser)/./node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage(param) {\n    let { params } = param;\n    _s();\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return \"javascript\";\n            case \"ts\":\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    if (authLoading || !user && !authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (currentVersion === null || currentVersion === void 0 ? void 0 : currentVersion.id) || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id,\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelGroup, {\n                            direction: \"horizontal\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 35,\n                                    minSize: 25,\n                                    maxSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                            version: currentVersion,\n                                            onFileSelect: handleFileSelect\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelResizeHandle, {\n                                    className: \"w-1 bg-[#21262d] hover:bg-[#0969da] transition-colors cursor-col-resize\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 65,\n                                    minSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                            version: currentVersion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full bg-[#0d1117] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-16 h-16 mx-auto mb-4 text-[#7d8590]\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-[#f0f6fc] mb-2\",\n                                                        children: \"Code Editor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-[#7d8590]\",\n                                                        children: \"Select a file from the explorer to view its content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"851n8SFiz3SRoLlPT4KA3In4dKg=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chat/[id]/page.tsx\n"));

/***/ })

});