"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dnd-core";
exports.ids = ["vendor-chunks/dnd-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/beginDrag.js":
/*!**********************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/beginDrag.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBeginDrag: () => (/* binding */ createBeginDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _local_setClientOffset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./local/setClientOffset */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/local/setClientOffset.js\");\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/js_utils */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n\n\n\n\nvar ResetCoordinatesAction = {\n    type: _types__WEBPACK_IMPORTED_MODULE_1__.INIT_COORDS,\n    payload: {\n        clientOffset: null,\n        sourceClientOffset: null\n    }\n};\nfunction createBeginDrag(manager) {\n    return function beginDrag() {\n        var sourceIds = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n            publishSource: true\n        };\n        var _options$publishSourc = options.publishSource, publishSource = _options$publishSourc === void 0 ? true : _options$publishSourc, clientOffset = options.clientOffset, getSourceClientOffset = options.getSourceClientOffset;\n        var monitor = manager.getMonitor();\n        var registry = manager.getRegistry(); // Initialize the coordinates using the client offset\n        manager.dispatch((0,_local_setClientOffset__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset));\n        verifyInvariants(sourceIds, monitor, registry); // Get the draggable source\n        var sourceId = getDraggableSource(sourceIds, monitor);\n        if (sourceId === null) {\n            manager.dispatch(ResetCoordinatesAction);\n            return;\n        } // Get the source client offset\n        var sourceClientOffset = null;\n        if (clientOffset) {\n            if (!getSourceClientOffset) {\n                throw new Error(\"getSourceClientOffset must be defined\");\n            }\n            verifyGetSourceClientOffsetIsFunction(getSourceClientOffset);\n            sourceClientOffset = getSourceClientOffset(sourceId);\n        } // Initialize the full coordinates\n        manager.dispatch((0,_local_setClientOffset__WEBPACK_IMPORTED_MODULE_2__.setClientOffset)(clientOffset, sourceClientOffset));\n        var source = registry.getSource(sourceId);\n        var item = source.beginDrag(monitor, sourceId); // If source.beginDrag returns null, this is an indicator to cancel the drag\n        if (item == null) {\n            return undefined;\n        }\n        verifyItemIsObject(item);\n        registry.pinSource(sourceId);\n        var itemType = registry.getSourceType(sourceId);\n        return {\n            type: _types__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG,\n            payload: {\n                itemType: itemType,\n                item: item,\n                sourceId: sourceId,\n                clientOffset: clientOffset || null,\n                sourceClientOffset: sourceClientOffset || null,\n                isSourcePublic: !!publishSource\n            }\n        };\n    };\n}\nfunction verifyInvariants(sourceIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.isDragging(), \"Cannot call beginDrag while dragging.\");\n    sourceIds.forEach(function(sourceId) {\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(registry.getSource(sourceId), \"Expected sourceIds to be registered.\");\n    });\n}\nfunction verifyGetSourceClientOffsetIsFunction(getSourceClientOffset) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof getSourceClientOffset === \"function\", \"When clientOffset is provided, getSourceClientOffset must be a function.\");\n}\nfunction verifyItemIsObject(item) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)((0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_3__.isObject)(item), \"Item must be an object.\");\n}\nfunction getDraggableSource(sourceIds, monitor) {\n    var sourceId = null;\n    for(var i = sourceIds.length - 1; i >= 0; i--){\n        if (monitor.canDragSource(sourceIds[i])) {\n            sourceId = sourceIds[i];\n            break;\n        }\n    }\n    return sourceId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/beginDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/drop.js":
/*!*****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/drop.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDrop: () => (/* binding */ createDrop)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/js_utils */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\");\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) {\n            symbols = symbols.filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n            });\n        }\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\nfunction createDrop(manager) {\n    return function drop() {\n        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var monitor = manager.getMonitor();\n        var registry = manager.getRegistry();\n        verifyInvariants(monitor);\n        var targetIds = getDroppableTargets(monitor); // Multiple actions are dispatched here, which is why this doesn't return an action\n        targetIds.forEach(function(targetId, index) {\n            var dropResult = determineDropResult(targetId, index, registry, monitor);\n            var action = {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.DROP,\n                payload: {\n                    dropResult: _objectSpread(_objectSpread({}, options), dropResult)\n                }\n            };\n            manager.dispatch(action);\n        });\n    };\n}\nfunction verifyInvariants(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), \"Cannot call drop while not dragging.\");\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), \"Cannot call drop twice during one drag operation.\");\n}\nfunction determineDropResult(targetId, index, registry, monitor) {\n    var target = registry.getTarget(targetId);\n    var dropResult = target ? target.drop(monitor, targetId) : undefined;\n    verifyDropResultType(dropResult);\n    if (typeof dropResult === \"undefined\") {\n        dropResult = index === 0 ? {} : monitor.getDropResult();\n    }\n    return dropResult;\n}\nfunction verifyDropResultType(dropResult) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof dropResult === \"undefined\" || (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_2__.isObject)(dropResult), \"Drop result must either be an object or undefined.\");\n}\nfunction getDroppableTargets(monitor) {\n    var targetIds = monitor.getTargetIds().filter(monitor.canDropOnTarget, monitor);\n    targetIds.reverse();\n    return targetIds;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/endDrag.js":
/*!********************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/endDrag.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEndDrag: () => (/* binding */ createEndDrag)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n\n\nfunction createEndDrag(manager) {\n    return function endDrag() {\n        var monitor = manager.getMonitor();\n        var registry = manager.getRegistry();\n        verifyIsDragging(monitor);\n        var sourceId = monitor.getSourceId();\n        if (sourceId != null) {\n            var source = registry.getSource(sourceId, true);\n            source.endDrag(monitor, sourceId);\n            registry.unpinSource();\n        }\n        return {\n            type: _types__WEBPACK_IMPORTED_MODULE_1__.END_DRAG\n        };\n    };\n}\nfunction verifyIsDragging(monitor) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), \"Cannot call endDrag while not dragging.\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/endDrag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/hover.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/hover.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHover: () => (/* binding */ createHover)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _utils_matchesType__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/matchesType */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/matchesType.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n\n\n\nfunction createHover(manager) {\n    return function hover(targetIdsArg) {\n        var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}, clientOffset = _ref.clientOffset;\n        verifyTargetIdsIsArray(targetIdsArg);\n        var targetIds = targetIdsArg.slice(0);\n        var monitor = manager.getMonitor();\n        var registry = manager.getRegistry();\n        checkInvariants(targetIds, monitor, registry);\n        var draggedItemType = monitor.getItemType();\n        removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n        hoverAllTargets(targetIds, monitor, registry);\n        return {\n            type: _types__WEBPACK_IMPORTED_MODULE_1__.HOVER,\n            payload: {\n                targetIds: targetIds,\n                clientOffset: clientOffset || null\n            }\n        };\n    };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(Array.isArray(targetIdsArg), \"Expected targetIds to be an array.\");\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(monitor.isDragging(), \"Cannot call hover while not dragging.\");\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(!monitor.didDrop(), \"Cannot call hover after drop.\");\n    for(var i = 0; i < targetIds.length; i++){\n        var targetId = targetIds[i];\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(targetIds.lastIndexOf(targetId) === i, \"Expected targetIds to be unique in the passed array.\");\n        var target = registry.getTarget(targetId);\n        (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, \"Expected targetIds to be registered.\");\n    }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n    // Remove those targetIds that don't match the targetType.  This\n    // fixes shallow isOver which would only be non-shallow because of\n    // non-matching targets.\n    for(var i = targetIds.length - 1; i >= 0; i--){\n        var targetId = targetIds[i];\n        var targetType = registry.getTargetType(targetId);\n        if (!(0,_utils_matchesType__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n            targetIds.splice(i, 1);\n        }\n    }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n    // Finally call hover on all matching targets.\n    targetIds.forEach(function(targetId) {\n        var target = registry.getTarget(targetId);\n        target.hover(monitor, targetId);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vYWN0aW9ucy9kcmFnRHJvcC9ob3Zlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWlEO0FBQ0s7QUFDdEI7QUFDekIsU0FBU0csWUFBWUMsT0FBTztJQUNqQyxPQUFPLFNBQVNDLE1BQU1DLFlBQVk7UUFDaEMsSUFBSUMsT0FBT0MsVUFBVUMsTUFBTSxHQUFHLEtBQUtELFNBQVMsQ0FBQyxFQUFFLEtBQUtFLFlBQVlGLFNBQVMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxHQUM1RUcsZUFBZUosS0FBS0ksWUFBWTtRQUVwQ0MsdUJBQXVCTjtRQUN2QixJQUFJTyxZQUFZUCxhQUFhUSxLQUFLLENBQUM7UUFDbkMsSUFBSUMsVUFBVVgsUUFBUVksVUFBVTtRQUNoQyxJQUFJQyxXQUFXYixRQUFRYyxXQUFXO1FBQ2xDQyxnQkFBZ0JOLFdBQVdFLFNBQVNFO1FBQ3BDLElBQUlHLGtCQUFrQkwsUUFBUU0sV0FBVztRQUN6Q0MsMkJBQTJCVCxXQUFXSSxVQUFVRztRQUNoREcsZ0JBQWdCVixXQUFXRSxTQUFTRTtRQUNwQyxPQUFPO1lBQ0xPLE1BQU10Qix5Q0FBS0E7WUFDWHVCLFNBQVM7Z0JBQ1BaLFdBQVdBO2dCQUNYRixjQUFjQSxnQkFBZ0I7WUFDaEM7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxTQUFTQyx1QkFBdUJOLFlBQVk7SUFDMUNOLCtEQUFTQSxDQUFDMEIsTUFBTUMsT0FBTyxDQUFDckIsZUFBZTtBQUN6QztBQUVBLFNBQVNhLGdCQUFnQk4sU0FBUyxFQUFFRSxPQUFPLEVBQUVFLFFBQVE7SUFDbkRqQiwrREFBU0EsQ0FBQ2UsUUFBUWEsVUFBVSxJQUFJO0lBQ2hDNUIsK0RBQVNBLENBQUMsQ0FBQ2UsUUFBUWMsT0FBTyxJQUFJO0lBRTlCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJakIsVUFBVUosTUFBTSxFQUFFcUIsSUFBSztRQUN6QyxJQUFJQyxXQUFXbEIsU0FBUyxDQUFDaUIsRUFBRTtRQUMzQjlCLCtEQUFTQSxDQUFDYSxVQUFVbUIsV0FBVyxDQUFDRCxjQUFjRCxHQUFHO1FBQ2pELElBQUlHLFNBQVNoQixTQUFTaUIsU0FBUyxDQUFDSDtRQUNoQy9CLCtEQUFTQSxDQUFDaUMsUUFBUTtJQUNwQjtBQUNGO0FBRUEsU0FBU1gsMkJBQTJCVCxTQUFTLEVBQUVJLFFBQVEsRUFBRUcsZUFBZTtJQUN0RSxnRUFBZ0U7SUFDaEUsa0VBQWtFO0lBQ2xFLHdCQUF3QjtJQUN4QixJQUFLLElBQUlVLElBQUlqQixVQUFVSixNQUFNLEdBQUcsR0FBR3FCLEtBQUssR0FBR0EsSUFBSztRQUM5QyxJQUFJQyxXQUFXbEIsU0FBUyxDQUFDaUIsRUFBRTtRQUMzQixJQUFJSyxhQUFhbEIsU0FBU21CLGFBQWEsQ0FBQ0w7UUFFeEMsSUFBSSxDQUFDOUIsK0RBQVdBLENBQUNrQyxZQUFZZixrQkFBa0I7WUFDN0NQLFVBQVV3QixNQUFNLENBQUNQLEdBQUc7UUFDdEI7SUFDRjtBQUNGO0FBRUEsU0FBU1AsZ0JBQWdCVixTQUFTLEVBQUVFLE9BQU8sRUFBRUUsUUFBUTtJQUNuRCw4Q0FBOEM7SUFDOUNKLFVBQVV5QixPQUFPLENBQUMsU0FBVVAsUUFBUTtRQUNsQyxJQUFJRSxTQUFTaEIsU0FBU2lCLFNBQVMsQ0FBQ0g7UUFDaENFLE9BQU81QixLQUFLLENBQUNVLFNBQVNnQjtJQUN4QjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZHJhZ0Ryb3AvaG92ZXIuanM/NTgyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICdAcmVhY3QtZG5kL2ludmFyaWFudCc7XG5pbXBvcnQgeyBtYXRjaGVzVHlwZSB9IGZyb20gJy4uLy4uL3V0aWxzL21hdGNoZXNUeXBlJztcbmltcG9ydCB7IEhPVkVSIH0gZnJvbSAnLi90eXBlcyc7XG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlSG92ZXIobWFuYWdlcikge1xuICByZXR1cm4gZnVuY3Rpb24gaG92ZXIodGFyZ2V0SWRzQXJnKSB7XG4gICAgdmFyIF9yZWYgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6IHt9LFxuICAgICAgICBjbGllbnRPZmZzZXQgPSBfcmVmLmNsaWVudE9mZnNldDtcblxuICAgIHZlcmlmeVRhcmdldElkc0lzQXJyYXkodGFyZ2V0SWRzQXJnKTtcbiAgICB2YXIgdGFyZ2V0SWRzID0gdGFyZ2V0SWRzQXJnLnNsaWNlKDApO1xuICAgIHZhciBtb25pdG9yID0gbWFuYWdlci5nZXRNb25pdG9yKCk7XG4gICAgdmFyIHJlZ2lzdHJ5ID0gbWFuYWdlci5nZXRSZWdpc3RyeSgpO1xuICAgIGNoZWNrSW52YXJpYW50cyh0YXJnZXRJZHMsIG1vbml0b3IsIHJlZ2lzdHJ5KTtcbiAgICB2YXIgZHJhZ2dlZEl0ZW1UeXBlID0gbW9uaXRvci5nZXRJdGVtVHlwZSgpO1xuICAgIHJlbW92ZU5vbk1hdGNoaW5nVGFyZ2V0SWRzKHRhcmdldElkcywgcmVnaXN0cnksIGRyYWdnZWRJdGVtVHlwZSk7XG4gICAgaG92ZXJBbGxUYXJnZXRzKHRhcmdldElkcywgbW9uaXRvciwgcmVnaXN0cnkpO1xuICAgIHJldHVybiB7XG4gICAgICB0eXBlOiBIT1ZFUixcbiAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgdGFyZ2V0SWRzOiB0YXJnZXRJZHMsXG4gICAgICAgIGNsaWVudE9mZnNldDogY2xpZW50T2Zmc2V0IHx8IG51bGxcbiAgICAgIH1cbiAgICB9O1xuICB9O1xufVxuXG5mdW5jdGlvbiB2ZXJpZnlUYXJnZXRJZHNJc0FycmF5KHRhcmdldElkc0FyZykge1xuICBpbnZhcmlhbnQoQXJyYXkuaXNBcnJheSh0YXJnZXRJZHNBcmcpLCAnRXhwZWN0ZWQgdGFyZ2V0SWRzIHRvIGJlIGFuIGFycmF5LicpO1xufVxuXG5mdW5jdGlvbiBjaGVja0ludmFyaWFudHModGFyZ2V0SWRzLCBtb25pdG9yLCByZWdpc3RyeSkge1xuICBpbnZhcmlhbnQobW9uaXRvci5pc0RyYWdnaW5nKCksICdDYW5ub3QgY2FsbCBob3ZlciB3aGlsZSBub3QgZHJhZ2dpbmcuJyk7XG4gIGludmFyaWFudCghbW9uaXRvci5kaWREcm9wKCksICdDYW5ub3QgY2FsbCBob3ZlciBhZnRlciBkcm9wLicpO1xuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgdGFyZ2V0SWRzLmxlbmd0aDsgaSsrKSB7XG4gICAgdmFyIHRhcmdldElkID0gdGFyZ2V0SWRzW2ldO1xuICAgIGludmFyaWFudCh0YXJnZXRJZHMubGFzdEluZGV4T2YodGFyZ2V0SWQpID09PSBpLCAnRXhwZWN0ZWQgdGFyZ2V0SWRzIHRvIGJlIHVuaXF1ZSBpbiB0aGUgcGFzc2VkIGFycmF5LicpO1xuICAgIHZhciB0YXJnZXQgPSByZWdpc3RyeS5nZXRUYXJnZXQodGFyZ2V0SWQpO1xuICAgIGludmFyaWFudCh0YXJnZXQsICdFeHBlY3RlZCB0YXJnZXRJZHMgdG8gYmUgcmVnaXN0ZXJlZC4nKTtcbiAgfVxufVxuXG5mdW5jdGlvbiByZW1vdmVOb25NYXRjaGluZ1RhcmdldElkcyh0YXJnZXRJZHMsIHJlZ2lzdHJ5LCBkcmFnZ2VkSXRlbVR5cGUpIHtcbiAgLy8gUmVtb3ZlIHRob3NlIHRhcmdldElkcyB0aGF0IGRvbid0IG1hdGNoIHRoZSB0YXJnZXRUeXBlLiAgVGhpc1xuICAvLyBmaXhlcyBzaGFsbG93IGlzT3ZlciB3aGljaCB3b3VsZCBvbmx5IGJlIG5vbi1zaGFsbG93IGJlY2F1c2Ugb2ZcbiAgLy8gbm9uLW1hdGNoaW5nIHRhcmdldHMuXG4gIGZvciAodmFyIGkgPSB0YXJnZXRJZHMubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHtcbiAgICB2YXIgdGFyZ2V0SWQgPSB0YXJnZXRJZHNbaV07XG4gICAgdmFyIHRhcmdldFR5cGUgPSByZWdpc3RyeS5nZXRUYXJnZXRUeXBlKHRhcmdldElkKTtcblxuICAgIGlmICghbWF0Y2hlc1R5cGUodGFyZ2V0VHlwZSwgZHJhZ2dlZEl0ZW1UeXBlKSkge1xuICAgICAgdGFyZ2V0SWRzLnNwbGljZShpLCAxKTtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gaG92ZXJBbGxUYXJnZXRzKHRhcmdldElkcywgbW9uaXRvciwgcmVnaXN0cnkpIHtcbiAgLy8gRmluYWxseSBjYWxsIGhvdmVyIG9uIGFsbCBtYXRjaGluZyB0YXJnZXRzLlxuICB0YXJnZXRJZHMuZm9yRWFjaChmdW5jdGlvbiAodGFyZ2V0SWQpIHtcbiAgICB2YXIgdGFyZ2V0ID0gcmVnaXN0cnkuZ2V0VGFyZ2V0KHRhcmdldElkKTtcbiAgICB0YXJnZXQuaG92ZXIobW9uaXRvciwgdGFyZ2V0SWQpO1xuICB9KTtcbn0iXSwibmFtZXMiOlsiaW52YXJpYW50IiwibWF0Y2hlc1R5cGUiLCJIT1ZFUiIsImNyZWF0ZUhvdmVyIiwibWFuYWdlciIsImhvdmVyIiwidGFyZ2V0SWRzQXJnIiwiX3JlZiIsImFyZ3VtZW50cyIsImxlbmd0aCIsInVuZGVmaW5lZCIsImNsaWVudE9mZnNldCIsInZlcmlmeVRhcmdldElkc0lzQXJyYXkiLCJ0YXJnZXRJZHMiLCJzbGljZSIsIm1vbml0b3IiLCJnZXRNb25pdG9yIiwicmVnaXN0cnkiLCJnZXRSZWdpc3RyeSIsImNoZWNrSW52YXJpYW50cyIsImRyYWdnZWRJdGVtVHlwZSIsImdldEl0ZW1UeXBlIiwicmVtb3ZlTm9uTWF0Y2hpbmdUYXJnZXRJZHMiLCJob3ZlckFsbFRhcmdldHMiLCJ0eXBlIiwicGF5bG9hZCIsIkFycmF5IiwiaXNBcnJheSIsImlzRHJhZ2dpbmciLCJkaWREcm9wIiwiaSIsInRhcmdldElkIiwibGFzdEluZGV4T2YiLCJ0YXJnZXQiLCJnZXRUYXJnZXQiLCJ0YXJnZXRUeXBlIiwiZ2V0VGFyZ2V0VHlwZSIsInNwbGljZSIsImZvckVhY2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/hover.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.DROP),\n/* harmony export */   END_DRAG: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.END_DRAG),\n/* harmony export */   HOVER: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.HOVER),\n/* harmony export */   INIT_COORDS: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE),\n/* harmony export */   createDragDropActions: () => (/* binding */ createDragDropActions)\n/* harmony export */ });\n/* harmony import */ var _beginDrag__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./beginDrag */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/beginDrag.js\");\n/* harmony import */ var _publishDragSource__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./publishDragSource */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/publishDragSource.js\");\n/* harmony import */ var _hover__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hover */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/hover.js\");\n/* harmony import */ var _drop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drop */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/drop.js\");\n/* harmony import */ var _endDrag__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./endDrag */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/endDrag.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n\n\n\n\n\n\nfunction createDragDropActions(manager) {\n    return {\n        beginDrag: (0,_beginDrag__WEBPACK_IMPORTED_MODULE_1__.createBeginDrag)(manager),\n        publishDragSource: (0,_publishDragSource__WEBPACK_IMPORTED_MODULE_2__.createPublishDragSource)(manager),\n        hover: (0,_hover__WEBPACK_IMPORTED_MODULE_3__.createHover)(manager),\n        drop: (0,_drop__WEBPACK_IMPORTED_MODULE_4__.createDrop)(manager),\n        endDrag: (0,_endDrag__WEBPACK_IMPORTED_MODULE_5__.createEndDrag)(manager)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vYWN0aW9ucy9kcmFnRHJvcC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQ2dCO0FBQ3hCO0FBQ0Y7QUFDTTtBQUNsQjtBQUNqQixTQUFTSyxzQkFBc0JDLE9BQU87SUFDM0MsT0FBTztRQUNMQyxXQUFXUCwyREFBZUEsQ0FBQ007UUFDM0JFLG1CQUFtQlAsMkVBQXVCQSxDQUFDSztRQUMzQ0csT0FBT1AsbURBQVdBLENBQUNJO1FBQ25CSSxNQUFNUCxpREFBVUEsQ0FBQ0c7UUFDakJLLFNBQVNQLHVEQUFhQSxDQUFDRTtJQUN6QjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZHJhZ0Ryb3AvaW5kZXguanM/ZGY5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCZWdpbkRyYWcgfSBmcm9tICcuL2JlZ2luRHJhZyc7XG5pbXBvcnQgeyBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZSB9IGZyb20gJy4vcHVibGlzaERyYWdTb3VyY2UnO1xuaW1wb3J0IHsgY3JlYXRlSG92ZXIgfSBmcm9tICcuL2hvdmVyJztcbmltcG9ydCB7IGNyZWF0ZURyb3AgfSBmcm9tICcuL2Ryb3AnO1xuaW1wb3J0IHsgY3JlYXRlRW5kRHJhZyB9IGZyb20gJy4vZW5kRHJhZyc7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVEcmFnRHJvcEFjdGlvbnMobWFuYWdlcikge1xuICByZXR1cm4ge1xuICAgIGJlZ2luRHJhZzogY3JlYXRlQmVnaW5EcmFnKG1hbmFnZXIpLFxuICAgIHB1Ymxpc2hEcmFnU291cmNlOiBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZShtYW5hZ2VyKSxcbiAgICBob3ZlcjogY3JlYXRlSG92ZXIobWFuYWdlciksXG4gICAgZHJvcDogY3JlYXRlRHJvcChtYW5hZ2VyKSxcbiAgICBlbmREcmFnOiBjcmVhdGVFbmREcmFnKG1hbmFnZXIpXG4gIH07XG59Il0sIm5hbWVzIjpbImNyZWF0ZUJlZ2luRHJhZyIsImNyZWF0ZVB1Ymxpc2hEcmFnU291cmNlIiwiY3JlYXRlSG92ZXIiLCJjcmVhdGVEcm9wIiwiY3JlYXRlRW5kRHJhZyIsImNyZWF0ZURyYWdEcm9wQWN0aW9ucyIsIm1hbmFnZXIiLCJiZWdpbkRyYWciLCJwdWJsaXNoRHJhZ1NvdXJjZSIsImhvdmVyIiwiZHJvcCIsImVuZERyYWciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/local/setClientOffset.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/local/setClientOffset.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setClientOffset: () => (/* binding */ setClientOffset)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n\nfunction setClientOffset(clientOffset, sourceClientOffset) {\n    return {\n        type: _types__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS,\n        payload: {\n            sourceClientOffset: sourceClientOffset || null,\n            clientOffset: clientOffset || null\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vYWN0aW9ucy9kcmFnRHJvcC9sb2NhbC9zZXRDbGllbnRPZmZzZXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7QUFDaEMsU0FBU0MsZ0JBQWdCQyxZQUFZLEVBQUVDLGtCQUFrQjtJQUM5RCxPQUFPO1FBQ0xDLE1BQU1KLCtDQUFXQTtRQUNqQkssU0FBUztZQUNQRixvQkFBb0JBLHNCQUFzQjtZQUMxQ0QsY0FBY0EsZ0JBQWdCO1FBQ2hDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2VzbS9hY3Rpb25zL2RyYWdEcm9wL2xvY2FsL3NldENsaWVudE9mZnNldC5qcz8xZmJkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IElOSVRfQ09PUkRTIH0gZnJvbSAnLi4vdHlwZXMnO1xuZXhwb3J0IGZ1bmN0aW9uIHNldENsaWVudE9mZnNldChjbGllbnRPZmZzZXQsIHNvdXJjZUNsaWVudE9mZnNldCkge1xuICByZXR1cm4ge1xuICAgIHR5cGU6IElOSVRfQ09PUkRTLFxuICAgIHBheWxvYWQ6IHtcbiAgICAgIHNvdXJjZUNsaWVudE9mZnNldDogc291cmNlQ2xpZW50T2Zmc2V0IHx8IG51bGwsXG4gICAgICBjbGllbnRPZmZzZXQ6IGNsaWVudE9mZnNldCB8fCBudWxsXG4gICAgfVxuICB9O1xufSJdLCJuYW1lcyI6WyJJTklUX0NPT1JEUyIsInNldENsaWVudE9mZnNldCIsImNsaWVudE9mZnNldCIsInNvdXJjZUNsaWVudE9mZnNldCIsInR5cGUiLCJwYXlsb2FkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/local/setClientOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/publishDragSource.js":
/*!******************************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/publishDragSource.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPublishDragSource: () => (/* binding */ createPublishDragSource)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n\nfunction createPublishDragSource(manager) {\n    return function publishDragSource() {\n        var monitor = manager.getMonitor();\n        if (monitor.isDragging()) {\n            return {\n                type: _types__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE\n            };\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vYWN0aW9ucy9kcmFnRHJvcC9wdWJsaXNoRHJhZ1NvdXJjZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUN2QyxTQUFTQyx3QkFBd0JDLE9BQU87SUFDN0MsT0FBTyxTQUFTQztRQUNkLElBQUlDLFVBQVVGLFFBQVFHLFVBQVU7UUFFaEMsSUFBSUQsUUFBUUUsVUFBVSxJQUFJO1lBQ3hCLE9BQU87Z0JBQ0xDLE1BQU1QLHVEQUFtQkE7WUFDM0I7UUFDRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vYWN0aW9ucy9kcmFnRHJvcC9wdWJsaXNoRHJhZ1NvdXJjZS5qcz9jNGQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBVQkxJU0hfRFJBR19TT1VSQ0UgfSBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVQdWJsaXNoRHJhZ1NvdXJjZShtYW5hZ2VyKSB7XG4gIHJldHVybiBmdW5jdGlvbiBwdWJsaXNoRHJhZ1NvdXJjZSgpIHtcbiAgICB2YXIgbW9uaXRvciA9IG1hbmFnZXIuZ2V0TW9uaXRvcigpO1xuXG4gICAgaWYgKG1vbml0b3IuaXNEcmFnZ2luZygpKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiBQVUJMSVNIX0RSQUdfU09VUkNFXG4gICAgICB9O1xuICAgIH1cbiAgfTtcbn0iXSwibmFtZXMiOlsiUFVCTElTSF9EUkFHX1NPVVJDRSIsImNyZWF0ZVB1Ymxpc2hEcmFnU291cmNlIiwibWFuYWdlciIsInB1Ymxpc2hEcmFnU291cmNlIiwibW9uaXRvciIsImdldE1vbml0b3IiLCJpc0RyYWdnaW5nIiwidHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/publishDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BEGIN_DRAG: () => (/* binding */ BEGIN_DRAG),\n/* harmony export */   DROP: () => (/* binding */ DROP),\n/* harmony export */   END_DRAG: () => (/* binding */ END_DRAG),\n/* harmony export */   HOVER: () => (/* binding */ HOVER),\n/* harmony export */   INIT_COORDS: () => (/* binding */ INIT_COORDS),\n/* harmony export */   PUBLISH_DRAG_SOURCE: () => (/* binding */ PUBLISH_DRAG_SOURCE)\n/* harmony export */ });\nvar INIT_COORDS = \"dnd-core/INIT_COORDS\";\nvar BEGIN_DRAG = \"dnd-core/BEGIN_DRAG\";\nvar PUBLISH_DRAG_SOURCE = \"dnd-core/PUBLISH_DRAG_SOURCE\";\nvar HOVER = \"dnd-core/HOVER\";\nvar DROP = \"dnd-core/DROP\";\nvar END_DRAG = \"dnd-core/END_DRAG\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vYWN0aW9ucy9kcmFnRHJvcC90eXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBTyxJQUFJQSxjQUFjLHVCQUF1QjtBQUN6QyxJQUFJQyxhQUFhLHNCQUFzQjtBQUN2QyxJQUFJQyxzQkFBc0IsK0JBQStCO0FBQ3pELElBQUlDLFFBQVEsaUJBQWlCO0FBQzdCLElBQUlDLE9BQU8sZ0JBQWdCO0FBQzNCLElBQUlDLFdBQVcsb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvZHJhZ0Ryb3AvdHlwZXMuanM/YjQwZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIElOSVRfQ09PUkRTID0gJ2RuZC1jb3JlL0lOSVRfQ09PUkRTJztcbmV4cG9ydCB2YXIgQkVHSU5fRFJBRyA9ICdkbmQtY29yZS9CRUdJTl9EUkFHJztcbmV4cG9ydCB2YXIgUFVCTElTSF9EUkFHX1NPVVJDRSA9ICdkbmQtY29yZS9QVUJMSVNIX0RSQUdfU09VUkNFJztcbmV4cG9ydCB2YXIgSE9WRVIgPSAnZG5kLWNvcmUvSE9WRVInO1xuZXhwb3J0IHZhciBEUk9QID0gJ2RuZC1jb3JlL0RST1AnO1xuZXhwb3J0IHZhciBFTkRfRFJBRyA9ICdkbmQtY29yZS9FTkRfRFJBRyc7Il0sIm5hbWVzIjpbIklOSVRfQ09PUkRTIiwiQkVHSU5fRFJBRyIsIlBVQkxJU0hfRFJBR19TT1VSQ0UiLCJIT1ZFUiIsIkRST1AiLCJFTkRfRFJBRyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/actions/registry.js":
/*!************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/actions/registry.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_SOURCE: () => (/* binding */ ADD_SOURCE),\n/* harmony export */   ADD_TARGET: () => (/* binding */ ADD_TARGET),\n/* harmony export */   REMOVE_SOURCE: () => (/* binding */ REMOVE_SOURCE),\n/* harmony export */   REMOVE_TARGET: () => (/* binding */ REMOVE_TARGET),\n/* harmony export */   addSource: () => (/* binding */ addSource),\n/* harmony export */   addTarget: () => (/* binding */ addTarget),\n/* harmony export */   removeSource: () => (/* binding */ removeSource),\n/* harmony export */   removeTarget: () => (/* binding */ removeTarget)\n/* harmony export */ });\nvar ADD_SOURCE = \"dnd-core/ADD_SOURCE\";\nvar ADD_TARGET = \"dnd-core/ADD_TARGET\";\nvar REMOVE_SOURCE = \"dnd-core/REMOVE_SOURCE\";\nvar REMOVE_TARGET = \"dnd-core/REMOVE_TARGET\";\nfunction addSource(sourceId) {\n    return {\n        type: ADD_SOURCE,\n        payload: {\n            sourceId: sourceId\n        }\n    };\n}\nfunction addTarget(targetId) {\n    return {\n        type: ADD_TARGET,\n        payload: {\n            targetId: targetId\n        }\n    };\n}\nfunction removeSource(sourceId) {\n    return {\n        type: REMOVE_SOURCE,\n        payload: {\n            sourceId: sourceId\n        }\n    };\n}\nfunction removeTarget(targetId) {\n    return {\n        type: REMOVE_TARGET,\n        payload: {\n            targetId: targetId\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/actions/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/classes/DragDropManagerImpl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/classes/DragDropManagerImpl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropManagerImpl: () => (/* binding */ DragDropManagerImpl)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/index.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\nvar DragDropManagerImpl = /*#__PURE__*/ function() {\n    function DragDropManagerImpl(store, monitor) {\n        var _this = this;\n        _classCallCheck(this, DragDropManagerImpl);\n        _defineProperty(this, \"store\", void 0);\n        _defineProperty(this, \"monitor\", void 0);\n        _defineProperty(this, \"backend\", void 0);\n        _defineProperty(this, \"isSetUp\", false);\n        _defineProperty(this, \"handleRefCountChange\", function() {\n            var shouldSetUp = _this.store.getState().refCount > 0;\n            if (_this.backend) {\n                if (shouldSetUp && !_this.isSetUp) {\n                    _this.backend.setup();\n                    _this.isSetUp = true;\n                } else if (!shouldSetUp && _this.isSetUp) {\n                    _this.backend.teardown();\n                    _this.isSetUp = false;\n                }\n            }\n        });\n        this.store = store;\n        this.monitor = monitor;\n        store.subscribe(this.handleRefCountChange);\n    }\n    _createClass(DragDropManagerImpl, [\n        {\n            key: \"receiveBackend\",\n            value: function receiveBackend(backend) {\n                this.backend = backend;\n            }\n        },\n        {\n            key: \"getMonitor\",\n            value: function getMonitor() {\n                return this.monitor;\n            }\n        },\n        {\n            key: \"getBackend\",\n            value: function getBackend() {\n                return this.backend;\n            }\n        },\n        {\n            key: \"getRegistry\",\n            value: function getRegistry() {\n                return this.monitor.registry;\n            }\n        },\n        {\n            key: \"getActions\",\n            value: function getActions() {\n                /* eslint-disable-next-line @typescript-eslint/no-this-alias */ var manager = this;\n                var dispatch = this.store.dispatch;\n                function bindActionCreator(actionCreator) {\n                    return function() {\n                        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                            args[_key] = arguments[_key];\n                        }\n                        var action = actionCreator.apply(manager, args);\n                        if (typeof action !== \"undefined\") {\n                            dispatch(action);\n                        }\n                    };\n                }\n                var actions = (0,_actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.createDragDropActions)(this);\n                return Object.keys(actions).reduce(function(boundActions, key) {\n                    var action = actions[key];\n                    boundActions[key] = bindActionCreator(action);\n                    return boundActions;\n                }, {});\n            }\n        },\n        {\n            key: \"dispatch\",\n            value: function dispatch(action) {\n                this.store.dispatch(action);\n            }\n        }\n    ]);\n    return DragDropManagerImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/classes/DragDropManagerImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/classes/DragDropMonitorImpl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/classes/DragDropMonitorImpl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragDropMonitorImpl: () => (/* binding */ DragDropMonitorImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _utils_matchesType__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/matchesType */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/matchesType.js\");\n/* harmony import */ var _utils_coords__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/coords */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/coords.js\");\n/* harmony import */ var _utils_dirtiness__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/dirtiness */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/dirtiness.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\n\nvar DragDropMonitorImpl = /*#__PURE__*/ function() {\n    function DragDropMonitorImpl(store, registry) {\n        _classCallCheck(this, DragDropMonitorImpl);\n        _defineProperty(this, \"store\", void 0);\n        _defineProperty(this, \"registry\", void 0);\n        this.store = store;\n        this.registry = registry;\n    }\n    _createClass(DragDropMonitorImpl, [\n        {\n            key: \"subscribeToStateChange\",\n            value: function subscribeToStateChange(listener) {\n                var _this = this;\n                var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n                    handlerIds: undefined\n                };\n                var handlerIds = options.handlerIds;\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === \"function\", \"listener must be a function.\");\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof handlerIds === \"undefined\" || Array.isArray(handlerIds), \"handlerIds, when specified, must be an array of strings.\");\n                var prevStateId = this.store.getState().stateId;\n                var handleChange = function handleChange() {\n                    var state = _this.store.getState();\n                    var currentStateId = state.stateId;\n                    try {\n                        var canSkipListener = currentStateId === prevStateId || currentStateId === prevStateId + 1 && !(0,_utils_dirtiness__WEBPACK_IMPORTED_MODULE_1__.areDirty)(state.dirtyHandlerIds, handlerIds);\n                        if (!canSkipListener) {\n                            listener();\n                        }\n                    } finally{\n                        prevStateId = currentStateId;\n                    }\n                };\n                return this.store.subscribe(handleChange);\n            }\n        },\n        {\n            key: \"subscribeToOffsetChange\",\n            value: function subscribeToOffsetChange(listener) {\n                var _this2 = this;\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof listener === \"function\", \"listener must be a function.\");\n                var previousState = this.store.getState().dragOffset;\n                var handleChange = function handleChange() {\n                    var nextState = _this2.store.getState().dragOffset;\n                    if (nextState === previousState) {\n                        return;\n                    }\n                    previousState = nextState;\n                    listener();\n                };\n                return this.store.subscribe(handleChange);\n            }\n        },\n        {\n            key: \"canDragSource\",\n            value: function canDragSource(sourceId) {\n                if (!sourceId) {\n                    return false;\n                }\n                var source = this.registry.getSource(sourceId);\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, \"Expected to find a valid source. sourceId=\".concat(sourceId));\n                if (this.isDragging()) {\n                    return false;\n                }\n                return source.canDrag(this, sourceId);\n            }\n        },\n        {\n            key: \"canDropOnTarget\",\n            value: function canDropOnTarget(targetId) {\n                // undefined on initial render\n                if (!targetId) {\n                    return false;\n                }\n                var target = this.registry.getTarget(targetId);\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(target, \"Expected to find a valid target. targetId=\".concat(targetId));\n                if (!this.isDragging() || this.didDrop()) {\n                    return false;\n                }\n                var targetType = this.registry.getTargetType(targetId);\n                var draggedItemType = this.getItemType();\n                return (0,_utils_matchesType__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType) && target.canDrop(this, targetId);\n            }\n        },\n        {\n            key: \"isDragging\",\n            value: function isDragging() {\n                return Boolean(this.getItemType());\n            }\n        },\n        {\n            key: \"isDraggingSource\",\n            value: function isDraggingSource(sourceId) {\n                // undefined on initial render\n                if (!sourceId) {\n                    return false;\n                }\n                var source = this.registry.getSource(sourceId, true);\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, \"Expected to find a valid source. sourceId=\".concat(sourceId));\n                if (!this.isDragging() || !this.isSourcePublic()) {\n                    return false;\n                }\n                var sourceType = this.registry.getSourceType(sourceId);\n                var draggedItemType = this.getItemType();\n                if (sourceType !== draggedItemType) {\n                    return false;\n                }\n                return source.isDragging(this, sourceId);\n            }\n        },\n        {\n            key: \"isOverTarget\",\n            value: function isOverTarget(targetId) {\n                var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n                    shallow: false\n                };\n                // undefined on initial render\n                if (!targetId) {\n                    return false;\n                }\n                var shallow = options.shallow;\n                if (!this.isDragging()) {\n                    return false;\n                }\n                var targetType = this.registry.getTargetType(targetId);\n                var draggedItemType = this.getItemType();\n                if (draggedItemType && !(0,_utils_matchesType__WEBPACK_IMPORTED_MODULE_2__.matchesType)(targetType, draggedItemType)) {\n                    return false;\n                }\n                var targetIds = this.getTargetIds();\n                if (!targetIds.length) {\n                    return false;\n                }\n                var index = targetIds.indexOf(targetId);\n                if (shallow) {\n                    return index === targetIds.length - 1;\n                } else {\n                    return index > -1;\n                }\n            }\n        },\n        {\n            key: \"getItemType\",\n            value: function getItemType() {\n                return this.store.getState().dragOperation.itemType;\n            }\n        },\n        {\n            key: \"getItem\",\n            value: function getItem() {\n                return this.store.getState().dragOperation.item;\n            }\n        },\n        {\n            key: \"getSourceId\",\n            value: function getSourceId() {\n                return this.store.getState().dragOperation.sourceId;\n            }\n        },\n        {\n            key: \"getTargetIds\",\n            value: function getTargetIds() {\n                return this.store.getState().dragOperation.targetIds;\n            }\n        },\n        {\n            key: \"getDropResult\",\n            value: function getDropResult() {\n                return this.store.getState().dragOperation.dropResult;\n            }\n        },\n        {\n            key: \"didDrop\",\n            value: function didDrop() {\n                return this.store.getState().dragOperation.didDrop;\n            }\n        },\n        {\n            key: \"isSourcePublic\",\n            value: function isSourcePublic() {\n                return Boolean(this.store.getState().dragOperation.isSourcePublic);\n            }\n        },\n        {\n            key: \"getInitialClientOffset\",\n            value: function getInitialClientOffset() {\n                return this.store.getState().dragOffset.initialClientOffset;\n            }\n        },\n        {\n            key: \"getInitialSourceClientOffset\",\n            value: function getInitialSourceClientOffset() {\n                return this.store.getState().dragOffset.initialSourceClientOffset;\n            }\n        },\n        {\n            key: \"getClientOffset\",\n            value: function getClientOffset() {\n                return this.store.getState().dragOffset.clientOffset;\n            }\n        },\n        {\n            key: \"getSourceClientOffset\",\n            value: function getSourceClientOffset() {\n                return (0,_utils_coords__WEBPACK_IMPORTED_MODULE_3__.getSourceClientOffset)(this.store.getState().dragOffset);\n            }\n        },\n        {\n            key: \"getDifferenceFromInitialOffset\",\n            value: function getDifferenceFromInitialOffset() {\n                return (0,_utils_coords__WEBPACK_IMPORTED_MODULE_3__.getDifferenceFromInitialOffset)(this.store.getState().dragOffset);\n            }\n        }\n    ]);\n    return DragDropMonitorImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/classes/DragDropMonitorImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/classes/HandlerRegistryImpl.js":
/*!***********************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/classes/HandlerRegistryImpl.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRegistryImpl: () => (/* binding */ HandlerRegistryImpl)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\n/* harmony import */ var _actions_registry__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../actions/registry */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/registry.js\");\n/* harmony import */ var _utils_getNextUniqueId__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getNextUniqueId */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/getNextUniqueId.js\");\n/* harmony import */ var _interfaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../interfaces */ \"(ssr)/./node_modules/dnd-core/dist/esm/interfaces.js\");\n/* harmony import */ var _contracts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts */ \"(ssr)/./node_modules/dnd-core/dist/esm/contracts.js\");\n/* harmony import */ var _react_dnd_asap__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-dnd/asap */ \"(ssr)/./node_modules/@react-dnd/asap/dist/esm/index.mjs\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\n\n\n\n\n\nfunction getNextHandlerId(role) {\n    var id = (0,_utils_getNextUniqueId__WEBPACK_IMPORTED_MODULE_2__.getNextUniqueId)().toString();\n    switch(role){\n        case _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE:\n            return \"S\".concat(id);\n        case _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET:\n            return \"T\".concat(id);\n        default:\n            throw new Error(\"Unknown Handler Role: \".concat(role));\n    }\n}\nfunction parseRoleFromHandlerId(handlerId) {\n    switch(handlerId[0]){\n        case \"S\":\n            return _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n        case \"T\":\n            return _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n        default:\n            (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(false, \"Cannot parse handler ID: \".concat(handlerId));\n    }\n}\nfunction mapContainsValue(map, searchValue) {\n    var entries = map.entries();\n    var isDone = false;\n    do {\n        var _entries$next = entries.next(), done = _entries$next.done, _entries$next$value = _slicedToArray(_entries$next.value, 2), value = _entries$next$value[1];\n        if (value === searchValue) {\n            return true;\n        }\n        isDone = !!done;\n    }while (!isDone);\n    return false;\n}\nvar HandlerRegistryImpl = /*#__PURE__*/ function() {\n    function HandlerRegistryImpl(store) {\n        _classCallCheck(this, HandlerRegistryImpl);\n        _defineProperty(this, \"types\", new Map());\n        _defineProperty(this, \"dragSources\", new Map());\n        _defineProperty(this, \"dropTargets\", new Map());\n        _defineProperty(this, \"pinnedSourceId\", null);\n        _defineProperty(this, \"pinnedSource\", null);\n        _defineProperty(this, \"store\", void 0);\n        this.store = store;\n    }\n    _createClass(HandlerRegistryImpl, [\n        {\n            key: \"addSource\",\n            value: function addSource(type, source) {\n                (0,_contracts__WEBPACK_IMPORTED_MODULE_4__.validateType)(type);\n                (0,_contracts__WEBPACK_IMPORTED_MODULE_4__.validateSourceContract)(source);\n                var sourceId = this.addHandler(_interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE, type, source);\n                this.store.dispatch((0,_actions_registry__WEBPACK_IMPORTED_MODULE_5__.addSource)(sourceId));\n                return sourceId;\n            }\n        },\n        {\n            key: \"addTarget\",\n            value: function addTarget(type, target) {\n                (0,_contracts__WEBPACK_IMPORTED_MODULE_4__.validateType)(type, true);\n                (0,_contracts__WEBPACK_IMPORTED_MODULE_4__.validateTargetContract)(target);\n                var targetId = this.addHandler(_interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET, type, target);\n                this.store.dispatch((0,_actions_registry__WEBPACK_IMPORTED_MODULE_5__.addTarget)(targetId));\n                return targetId;\n            }\n        },\n        {\n            key: \"containsHandler\",\n            value: function containsHandler(handler) {\n                return mapContainsValue(this.dragSources, handler) || mapContainsValue(this.dropTargets, handler);\n            }\n        },\n        {\n            key: \"getSource\",\n            value: function getSource(sourceId) {\n                var includePinned = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.isSourceId(sourceId), \"Expected a valid source ID.\");\n                var isPinned = includePinned && sourceId === this.pinnedSourceId;\n                var source = isPinned ? this.pinnedSource : this.dragSources.get(sourceId);\n                return source;\n            }\n        },\n        {\n            key: \"getTarget\",\n            value: function getTarget(targetId) {\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.isTargetId(targetId), \"Expected a valid target ID.\");\n                return this.dropTargets.get(targetId);\n            }\n        },\n        {\n            key: \"getSourceType\",\n            value: function getSourceType(sourceId) {\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.isSourceId(sourceId), \"Expected a valid source ID.\");\n                return this.types.get(sourceId);\n            }\n        },\n        {\n            key: \"getTargetType\",\n            value: function getTargetType(targetId) {\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.isTargetId(targetId), \"Expected a valid target ID.\");\n                return this.types.get(targetId);\n            }\n        },\n        {\n            key: \"isSourceId\",\n            value: function isSourceId(handlerId) {\n                var role = parseRoleFromHandlerId(handlerId);\n                return role === _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE;\n            }\n        },\n        {\n            key: \"isTargetId\",\n            value: function isTargetId(handlerId) {\n                var role = parseRoleFromHandlerId(handlerId);\n                return role === _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET;\n            }\n        },\n        {\n            key: \"removeSource\",\n            value: function removeSource(sourceId) {\n                var _this = this;\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.getSource(sourceId), \"Expected an existing source.\");\n                this.store.dispatch((0,_actions_registry__WEBPACK_IMPORTED_MODULE_5__.removeSource)(sourceId));\n                (0,_react_dnd_asap__WEBPACK_IMPORTED_MODULE_1__.asap)(function() {\n                    _this.dragSources.delete(sourceId);\n                    _this.types.delete(sourceId);\n                });\n            }\n        },\n        {\n            key: \"removeTarget\",\n            value: function removeTarget(targetId) {\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.getTarget(targetId), \"Expected an existing target.\");\n                this.store.dispatch((0,_actions_registry__WEBPACK_IMPORTED_MODULE_5__.removeTarget)(targetId));\n                this.dropTargets.delete(targetId);\n                this.types.delete(targetId);\n            }\n        },\n        {\n            key: \"pinSource\",\n            value: function pinSource(sourceId) {\n                var source = this.getSource(sourceId);\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(source, \"Expected an existing source.\");\n                this.pinnedSourceId = sourceId;\n                this.pinnedSource = source;\n            }\n        },\n        {\n            key: \"unpinSource\",\n            value: function unpinSource() {\n                (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(this.pinnedSource, \"No source is pinned at the time.\");\n                this.pinnedSourceId = null;\n                this.pinnedSource = null;\n            }\n        },\n        {\n            key: \"addHandler\",\n            value: function addHandler(role, type, handler) {\n                var id = getNextHandlerId(role);\n                this.types.set(id, type);\n                if (role === _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.SOURCE) {\n                    this.dragSources.set(id, handler);\n                } else if (role === _interfaces__WEBPACK_IMPORTED_MODULE_3__.HandlerRole.TARGET) {\n                    this.dropTargets.set(id, handler);\n                }\n                return id;\n            }\n        }\n    ]);\n    return HandlerRegistryImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/classes/HandlerRegistryImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/contracts.js":
/*!*****************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/contracts.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSourceContract: () => (/* binding */ validateSourceContract),\n/* harmony export */   validateTargetContract: () => (/* binding */ validateTargetContract),\n/* harmony export */   validateType: () => (/* binding */ validateType)\n/* harmony export */ });\n/* harmony import */ var _react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-dnd/invariant */ \"(ssr)/./node_modules/@react-dnd/invariant/dist/invariant.esm.js\");\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n        _typeof = function _typeof(obj) {\n            return typeof obj;\n        };\n    } else {\n        _typeof = function _typeof(obj) {\n            return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n        };\n    }\n    return _typeof(obj);\n}\n\nfunction validateSourceContract(source) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.canDrag === \"function\", \"Expected canDrag to be a function.\");\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.beginDrag === \"function\", \"Expected beginDrag to be a function.\");\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof source.endDrag === \"function\", \"Expected endDrag to be a function.\");\n}\nfunction validateTargetContract(target) {\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.canDrop === \"function\", \"Expected canDrop to be a function.\");\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.hover === \"function\", \"Expected hover to be a function.\");\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof target.drop === \"function\", \"Expected beginDrag to be a function.\");\n}\nfunction validateType(type, allowArray) {\n    if (allowArray && Array.isArray(type)) {\n        type.forEach(function(t) {\n            return validateType(t, false);\n        });\n        return;\n    }\n    (0,_react_dnd_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant)(typeof type === \"string\" || _typeof(type) === \"symbol\", allowArray ? \"Type can only be a string, a symbol, or an array of either.\" : \"Type can only be a string or a symbol.\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/contracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/createDragDropManager.js":
/*!*****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/createDragDropManager.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDragDropManager: () => (/* binding */ createDragDropManager)\n/* harmony export */ });\n/* harmony import */ var _classes_DragDropManagerImpl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./classes/DragDropManagerImpl */ \"(ssr)/./node_modules/dnd-core/dist/esm/classes/DragDropManagerImpl.js\");\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/dnd-core/node_modules/redux/es/redux.js\");\n/* harmony import */ var _reducers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./reducers */ \"(ssr)/./node_modules/dnd-core/dist/esm/reducers/index.js\");\n/* harmony import */ var _classes_DragDropMonitorImpl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/DragDropMonitorImpl */ \"(ssr)/./node_modules/dnd-core/dist/esm/classes/DragDropMonitorImpl.js\");\n/* harmony import */ var _classes_HandlerRegistryImpl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/HandlerRegistryImpl */ \"(ssr)/./node_modules/dnd-core/dist/esm/classes/HandlerRegistryImpl.js\");\n\n\n\n\n\nfunction createDragDropManager(backendFactory) {\n    var globalContext = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n    var backendOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var debugMode = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var store = makeStoreInstance(debugMode);\n    var monitor = new _classes_DragDropMonitorImpl__WEBPACK_IMPORTED_MODULE_0__.DragDropMonitorImpl(store, new _classes_HandlerRegistryImpl__WEBPACK_IMPORTED_MODULE_1__.HandlerRegistryImpl(store));\n    var manager = new _classes_DragDropManagerImpl__WEBPACK_IMPORTED_MODULE_2__.DragDropManagerImpl(store, monitor);\n    var backend = backendFactory(manager, globalContext, backendOptions);\n    manager.receiveBackend(backend);\n    return manager;\n}\nfunction makeStoreInstance(debugMode) {\n    // TODO: if we ever make a react-native version of this,\n    // we'll need to consider how to pull off dev-tooling\n    var reduxDevTools =  false && 0;\n    return (0,redux__WEBPACK_IMPORTED_MODULE_3__.createStore)(_reducers__WEBPACK_IMPORTED_MODULE_4__.reduce, debugMode && reduxDevTools && reduxDevTools({\n        name: \"dnd-core\",\n        instanceId: \"dnd-core\"\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/createDragDropManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/interfaces.js":
/*!******************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/interfaces.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandlerRole: () => (/* binding */ HandlerRole)\n/* harmony export */ });\nvar HandlerRole;\n(function(HandlerRole) {\n    HandlerRole[\"SOURCE\"] = \"SOURCE\";\n    HandlerRole[\"TARGET\"] = \"TARGET\";\n})(HandlerRole || (HandlerRole = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vaW50ZXJmYWNlcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sSUFBSUEsWUFBWTtBQUV0QixVQUFVQSxXQUFXO0lBQ3BCQSxXQUFXLENBQUMsU0FBUyxHQUFHO0lBQ3hCQSxXQUFXLENBQUMsU0FBUyxHQUFHO0FBQzFCLEdBQUdBLGVBQWdCQSxDQUFBQSxjQUFjLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vaW50ZXJmYWNlcy5qcz9jNDg1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgSGFuZGxlclJvbGU7XG5cbihmdW5jdGlvbiAoSGFuZGxlclJvbGUpIHtcbiAgSGFuZGxlclJvbGVbXCJTT1VSQ0VcIl0gPSBcIlNPVVJDRVwiO1xuICBIYW5kbGVyUm9sZVtcIlRBUkdFVFwiXSA9IFwiVEFSR0VUXCI7XG59KShIYW5kbGVyUm9sZSB8fCAoSGFuZGxlclJvbGUgPSB7fSkpOyJdLCJuYW1lcyI6WyJIYW5kbGVyUm9sZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/interfaces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/reducers/dirtyHandlerIds.js":
/*!********************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/reducers/dirtyHandlerIds.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/dragDrop */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../actions/registry */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/registry.js\");\n/* harmony import */ var _utils_equality__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/equality */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/equality.js\");\n/* harmony import */ var _utils_dirtiness__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/dirtiness */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/dirtiness.js\");\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/js_utils */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\");\n\n\n\n\n\nfunction reduce() {\n    var _state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _utils_dirtiness__WEBPACK_IMPORTED_MODULE_0__.NONE;\n    var action = arguments.length > 1 ? arguments[1] : undefined;\n    switch(action.type){\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_1__.HOVER:\n            break;\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_2__.ADD_SOURCE:\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_2__.ADD_TARGET:\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_2__.REMOVE_TARGET:\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_2__.REMOVE_SOURCE:\n            return _utils_dirtiness__WEBPACK_IMPORTED_MODULE_0__.NONE;\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_1__.BEGIN_DRAG:\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_1__.PUBLISH_DRAG_SOURCE:\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_1__.END_DRAG:\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_1__.DROP:\n        default:\n            return _utils_dirtiness__WEBPACK_IMPORTED_MODULE_0__.ALL;\n    }\n    var _action$payload = action.payload, _action$payload$targe = _action$payload.targetIds, targetIds = _action$payload$targe === void 0 ? [] : _action$payload$targe, _action$payload$prevT = _action$payload.prevTargetIds, prevTargetIds = _action$payload$prevT === void 0 ? [] : _action$payload$prevT;\n    var result = (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_3__.xor)(targetIds, prevTargetIds);\n    var didChange = result.length > 0 || !(0,_utils_equality__WEBPACK_IMPORTED_MODULE_4__.areArraysEqual)(targetIds, prevTargetIds);\n    if (!didChange) {\n        return _utils_dirtiness__WEBPACK_IMPORTED_MODULE_0__.NONE;\n    } // Check the target ids at the innermost position. If they are valid, add them\n    // to the result\n    var prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1];\n    var innermostTargetId = targetIds[targetIds.length - 1];\n    if (prevInnermostTargetId !== innermostTargetId) {\n        if (prevInnermostTargetId) {\n            result.push(prevInnermostTargetId);\n        }\n        if (innermostTargetId) {\n            result.push(innermostTargetId);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/reducers/dirtyHandlerIds.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/reducers/dragOffset.js":
/*!***************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/reducers/dragOffset.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n/* harmony import */ var _utils_equality__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/equality */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/equality.js\");\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) {\n            symbols = symbols.filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n            });\n        }\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\nvar initialState = {\n    initialSourceClientOffset: null,\n    initialClientOffset: null,\n    clientOffset: null\n};\nfunction reduce() {\n    var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n    var action = arguments.length > 1 ? arguments[1] : undefined;\n    var payload = action.payload;\n    switch(action.type){\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.INIT_COORDS:\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return {\n                initialSourceClientOffset: payload.sourceClientOffset,\n                initialClientOffset: payload.clientOffset,\n                clientOffset: payload.clientOffset\n            };\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            if ((0,_utils_equality__WEBPACK_IMPORTED_MODULE_1__.areCoordsEqual)(state.clientOffset, payload.clientOffset)) {\n                return state;\n            }\n            return _objectSpread(_objectSpread({}, state), {}, {\n                clientOffset: payload.clientOffset\n            });\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return initialState;\n        default:\n            return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/reducers/dragOffset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/reducers/dragOperation.js":
/*!******************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/reducers/dragOperation.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/dragDrop */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/dragDrop/types.js\");\n/* harmony import */ var _actions_registry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../actions/registry */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/registry.js\");\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/js_utils */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\");\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) {\n            symbols = symbols.filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n            });\n        }\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\nvar initialState = {\n    itemType: null,\n    item: null,\n    sourceId: null,\n    targetIds: [],\n    dropResult: null,\n    didDrop: false,\n    isSourcePublic: null\n};\nfunction reduce() {\n    var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n    var action = arguments.length > 1 ? arguments[1] : undefined;\n    var payload = action.payload;\n    switch(action.type){\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.BEGIN_DRAG:\n            return _objectSpread(_objectSpread({}, state), {}, {\n                itemType: payload.itemType,\n                item: payload.item,\n                sourceId: payload.sourceId,\n                isSourcePublic: payload.isSourcePublic,\n                dropResult: null,\n                didDrop: false\n            });\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.PUBLISH_DRAG_SOURCE:\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isSourcePublic: true\n            });\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.HOVER:\n            return _objectSpread(_objectSpread({}, state), {}, {\n                targetIds: payload.targetIds\n            });\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_1__.REMOVE_TARGET:\n            if (state.targetIds.indexOf(payload.targetId) === -1) {\n                return state;\n            }\n            return _objectSpread(_objectSpread({}, state), {}, {\n                targetIds: (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_2__.without)(state.targetIds, payload.targetId)\n            });\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.DROP:\n            return _objectSpread(_objectSpread({}, state), {}, {\n                dropResult: payload.dropResult,\n                didDrop: true,\n                targetIds: []\n            });\n        case _actions_dragDrop__WEBPACK_IMPORTED_MODULE_0__.END_DRAG:\n            return _objectSpread(_objectSpread({}, state), {}, {\n                itemType: null,\n                item: null,\n                sourceId: null,\n                dropResult: null,\n                didDrop: false,\n                isSourcePublic: null,\n                targetIds: []\n            });\n        default:\n            return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/reducers/dragOperation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/reducers/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/reducers/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _dragOffset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dragOffset */ \"(ssr)/./node_modules/dnd-core/dist/esm/reducers/dragOffset.js\");\n/* harmony import */ var _dragOperation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dragOperation */ \"(ssr)/./node_modules/dnd-core/dist/esm/reducers/dragOperation.js\");\n/* harmony import */ var _refCount__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./refCount */ \"(ssr)/./node_modules/dnd-core/dist/esm/reducers/refCount.js\");\n/* harmony import */ var _dirtyHandlerIds__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dirtyHandlerIds */ \"(ssr)/./node_modules/dnd-core/dist/esm/reducers/dirtyHandlerIds.js\");\n/* harmony import */ var _stateId__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./stateId */ \"(ssr)/./node_modules/dnd-core/dist/esm/reducers/stateId.js\");\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/js_utils */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\");\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) {\n            symbols = symbols.filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n            });\n        }\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\n\n\n\nfunction reduce() {\n    var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var action = arguments.length > 1 ? arguments[1] : undefined;\n    return {\n        dirtyHandlerIds: (0,_dirtyHandlerIds__WEBPACK_IMPORTED_MODULE_0__.reduce)(state.dirtyHandlerIds, {\n            type: action.type,\n            payload: _objectSpread(_objectSpread({}, action.payload), {}, {\n                prevTargetIds: (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_1__.get)(state, \"dragOperation.targetIds\", [])\n            })\n        }),\n        dragOffset: (0,_dragOffset__WEBPACK_IMPORTED_MODULE_2__.reduce)(state.dragOffset, action),\n        refCount: (0,_refCount__WEBPACK_IMPORTED_MODULE_3__.reduce)(state.refCount, action),\n        dragOperation: (0,_dragOperation__WEBPACK_IMPORTED_MODULE_4__.reduce)(state.dragOperation, action),\n        stateId: (0,_stateId__WEBPACK_IMPORTED_MODULE_5__.reduce)(state.stateId)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/reducers/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/reducers/refCount.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/reducers/refCount.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\n/* harmony import */ var _actions_registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/registry */ \"(ssr)/./node_modules/dnd-core/dist/esm/actions/registry.js\");\n\nfunction reduce() {\n    var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    var action = arguments.length > 1 ? arguments[1] : undefined;\n    switch(action.type){\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_0__.ADD_SOURCE:\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_0__.ADD_TARGET:\n            return state + 1;\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_0__.REMOVE_SOURCE:\n        case _actions_registry__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TARGET:\n            return state - 1;\n        default:\n            return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vcmVkdWNlcnMvcmVmQ291bnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkY7QUFDcEYsU0FBU0k7SUFDZCxJQUFJQyxRQUFRQyxVQUFVQyxNQUFNLEdBQUcsS0FBS0QsU0FBUyxDQUFDLEVBQUUsS0FBS0UsWUFBWUYsU0FBUyxDQUFDLEVBQUUsR0FBRztJQUNoRixJQUFJRyxTQUFTSCxVQUFVQyxNQUFNLEdBQUcsSUFBSUQsU0FBUyxDQUFDLEVBQUUsR0FBR0U7SUFFbkQsT0FBUUMsT0FBT0MsSUFBSTtRQUNqQixLQUFLVix5REFBVUE7UUFDZixLQUFLQyx5REFBVUE7WUFDYixPQUFPSSxRQUFRO1FBRWpCLEtBQUtILDREQUFhQTtRQUNsQixLQUFLQyw0REFBYUE7WUFDaEIsT0FBT0UsUUFBUTtRQUVqQjtZQUNFLE9BQU9BO0lBQ1g7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2VzbS9yZWR1Y2Vycy9yZWZDb3VudC5qcz8yMGFmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFERF9TT1VSQ0UsIEFERF9UQVJHRVQsIFJFTU9WRV9TT1VSQ0UsIFJFTU9WRV9UQVJHRVQgfSBmcm9tICcuLi9hY3Rpb25zL3JlZ2lzdHJ5JztcbmV4cG9ydCBmdW5jdGlvbiByZWR1Y2UoKSB7XG4gIHZhciBzdGF0ZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogMDtcbiAgdmFyIGFjdGlvbiA9IGFyZ3VtZW50cy5sZW5ndGggPiAxID8gYXJndW1lbnRzWzFdIDogdW5kZWZpbmVkO1xuXG4gIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICBjYXNlIEFERF9TT1VSQ0U6XG4gICAgY2FzZSBBRERfVEFSR0VUOlxuICAgICAgcmV0dXJuIHN0YXRlICsgMTtcblxuICAgIGNhc2UgUkVNT1ZFX1NPVVJDRTpcbiAgICBjYXNlIFJFTU9WRV9UQVJHRVQ6XG4gICAgICByZXR1cm4gc3RhdGUgLSAxO1xuXG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiBzdGF0ZTtcbiAgfVxufSJdLCJuYW1lcyI6WyJBRERfU09VUkNFIiwiQUREX1RBUkdFVCIsIlJFTU9WRV9TT1VSQ0UiLCJSRU1PVkVfVEFSR0VUIiwicmVkdWNlIiwic3RhdGUiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJhY3Rpb24iLCJ0eXBlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/reducers/refCount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/reducers/stateId.js":
/*!************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/reducers/stateId.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reduce: () => (/* binding */ reduce)\n/* harmony export */ });\nfunction reduce() {\n    var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    return state + 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vcmVkdWNlcnMvc3RhdGVJZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sU0FBU0E7SUFDZCxJQUFJQyxRQUFRQyxVQUFVQyxNQUFNLEdBQUcsS0FBS0QsU0FBUyxDQUFDLEVBQUUsS0FBS0UsWUFBWUYsU0FBUyxDQUFDLEVBQUUsR0FBRztJQUNoRixPQUFPRCxRQUFRO0FBQ2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL3JlZHVjZXJzL3N0YXRlSWQuanM/YmZjMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gcmVkdWNlKCkge1xuICB2YXIgc3RhdGUgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IDA7XG4gIHJldHVybiBzdGF0ZSArIDE7XG59Il0sIm5hbWVzIjpbInJlZHVjZSIsInN0YXRlIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/reducers/stateId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/utils/coords.js":
/*!********************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/utils/coords.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   getDifferenceFromInitialOffset: () => (/* binding */ getDifferenceFromInitialOffset),\n/* harmony export */   getSourceClientOffset: () => (/* binding */ getSourceClientOffset),\n/* harmony export */   subtract: () => (/* binding */ subtract)\n/* harmony export */ });\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */ function add(a, b) {\n    return {\n        x: a.x + b.x,\n        y: a.y + b.y\n    };\n}\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */ function subtract(a, b) {\n    return {\n        x: a.x - b.x,\n        y: a.y - b.y\n    };\n}\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */ function getSourceClientOffset(state) {\n    var clientOffset = state.clientOffset, initialClientOffset = state.initialClientOffset, initialSourceClientOffset = state.initialSourceClientOffset;\n    if (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n        return null;\n    }\n    return subtract(add(clientOffset, initialSourceClientOffset), initialClientOffset);\n}\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */ function getDifferenceFromInitialOffset(state) {\n    var clientOffset = state.clientOffset, initialClientOffset = state.initialClientOffset;\n    if (!clientOffset || !initialClientOffset) {\n        return null;\n    }\n    return subtract(clientOffset, initialClientOffset);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vdXRpbHMvY29vcmRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7OztDQUlDLEdBQ00sU0FBU0EsSUFBSUMsQ0FBQyxFQUFFQyxDQUFDO0lBQ3RCLE9BQU87UUFDTEMsR0FBR0YsRUFBRUUsQ0FBQyxHQUFHRCxFQUFFQyxDQUFDO1FBQ1pDLEdBQUdILEVBQUVHLENBQUMsR0FBR0YsRUFBRUUsQ0FBQztJQUNkO0FBQ0Y7QUFDQTs7OztDQUlDLEdBRU0sU0FBU0MsU0FBU0osQ0FBQyxFQUFFQyxDQUFDO0lBQzNCLE9BQU87UUFDTEMsR0FBR0YsRUFBRUUsQ0FBQyxHQUFHRCxFQUFFQyxDQUFDO1FBQ1pDLEdBQUdILEVBQUVHLENBQUMsR0FBR0YsRUFBRUUsQ0FBQztJQUNkO0FBQ0Y7QUFDQTs7Ozs7OztDQU9DLEdBRU0sU0FBU0Usc0JBQXNCQyxLQUFLO0lBQ3pDLElBQUlDLGVBQWVELE1BQU1DLFlBQVksRUFDakNDLHNCQUFzQkYsTUFBTUUsbUJBQW1CLEVBQy9DQyw0QkFBNEJILE1BQU1HLHlCQUF5QjtJQUUvRCxJQUFJLENBQUNGLGdCQUFnQixDQUFDQyx1QkFBdUIsQ0FBQ0MsMkJBQTJCO1FBQ3ZFLE9BQU87SUFDVDtJQUVBLE9BQU9MLFNBQVNMLElBQUlRLGNBQWNFLDRCQUE0QkQ7QUFDaEU7QUFDQTs7OztDQUlDLEdBRU0sU0FBU0UsK0JBQStCSixLQUFLO0lBQ2xELElBQUlDLGVBQWVELE1BQU1DLFlBQVksRUFDakNDLHNCQUFzQkYsTUFBTUUsbUJBQW1CO0lBRW5ELElBQUksQ0FBQ0QsZ0JBQWdCLENBQUNDLHFCQUFxQjtRQUN6QyxPQUFPO0lBQ1Q7SUFFQSxPQUFPSixTQUFTRyxjQUFjQztBQUNoQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9kbmQtY29yZS9kaXN0L2VzbS91dGlscy9jb29yZHMuanM/OGEzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvb3JkaW5hdGUgYWRkaXRpb25cbiAqIEBwYXJhbSBhIFRoZSBmaXJzdCBjb29yZGluYXRlXG4gKiBAcGFyYW0gYiBUaGUgc2Vjb25kIGNvb3JkaW5hdGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGFkZChhLCBiKSB7XG4gIHJldHVybiB7XG4gICAgeDogYS54ICsgYi54LFxuICAgIHk6IGEueSArIGIueVxuICB9O1xufVxuLyoqXG4gKiBDb29yZGluYXRlIHN1YnRyYWN0aW9uXG4gKiBAcGFyYW0gYSBUaGUgZmlyc3QgY29vcmRpbmF0ZVxuICogQHBhcmFtIGIgVGhlIHNlY29uZCBjb29yZGluYXRlXG4gKi9cblxuZXhwb3J0IGZ1bmN0aW9uIHN1YnRyYWN0KGEsIGIpIHtcbiAgcmV0dXJuIHtcbiAgICB4OiBhLnggLSBiLngsXG4gICAgeTogYS55IC0gYi55XG4gIH07XG59XG4vKipcbiAqIFJldHVybnMgdGhlIGNhcnRlc2lhbiBkaXN0YW5jZSBvZiB0aGUgZHJhZyBzb3VyY2UgY29tcG9uZW50J3MgcG9zaXRpb24sIGJhc2VkIG9uIGl0cyBwb3NpdGlvblxuICogYXQgdGhlIHRpbWUgd2hlbiB0aGUgY3VycmVudCBkcmFnIG9wZXJhdGlvbiBoYXMgc3RhcnRlZCwgYW5kIHRoZSBtb3ZlbWVudCBkaWZmZXJlbmNlLlxuICpcbiAqIFJldHVybnMgbnVsbCBpZiBubyBpdGVtIGlzIGJlaW5nIGRyYWdnZWQuXG4gKlxuICogQHBhcmFtIHN0YXRlIFRoZSBvZmZzZXQgc3RhdGUgdG8gY29tcHV0ZSBmcm9tXG4gKi9cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFNvdXJjZUNsaWVudE9mZnNldChzdGF0ZSkge1xuICB2YXIgY2xpZW50T2Zmc2V0ID0gc3RhdGUuY2xpZW50T2Zmc2V0LFxuICAgICAgaW5pdGlhbENsaWVudE9mZnNldCA9IHN0YXRlLmluaXRpYWxDbGllbnRPZmZzZXQsXG4gICAgICBpbml0aWFsU291cmNlQ2xpZW50T2Zmc2V0ID0gc3RhdGUuaW5pdGlhbFNvdXJjZUNsaWVudE9mZnNldDtcblxuICBpZiAoIWNsaWVudE9mZnNldCB8fCAhaW5pdGlhbENsaWVudE9mZnNldCB8fCAhaW5pdGlhbFNvdXJjZUNsaWVudE9mZnNldCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIHN1YnRyYWN0KGFkZChjbGllbnRPZmZzZXQsIGluaXRpYWxTb3VyY2VDbGllbnRPZmZzZXQpLCBpbml0aWFsQ2xpZW50T2Zmc2V0KTtcbn1cbi8qKlxuICogRGV0ZXJtaW5lcyB0aGUgeCx5IG9mZnNldCBiZXR3ZWVuIHRoZSBjbGllbnQgb2Zmc2V0IGFuZCB0aGUgaW5pdGlhbCBjbGllbnQgb2Zmc2V0XG4gKlxuICogQHBhcmFtIHN0YXRlIFRoZSBvZmZzZXQgc3RhdGUgdG8gY29tcHV0ZSBmcm9tXG4gKi9cblxuZXhwb3J0IGZ1bmN0aW9uIGdldERpZmZlcmVuY2VGcm9tSW5pdGlhbE9mZnNldChzdGF0ZSkge1xuICB2YXIgY2xpZW50T2Zmc2V0ID0gc3RhdGUuY2xpZW50T2Zmc2V0LFxuICAgICAgaW5pdGlhbENsaWVudE9mZnNldCA9IHN0YXRlLmluaXRpYWxDbGllbnRPZmZzZXQ7XG5cbiAgaWYgKCFjbGllbnRPZmZzZXQgfHwgIWluaXRpYWxDbGllbnRPZmZzZXQpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHJldHVybiBzdWJ0cmFjdChjbGllbnRPZmZzZXQsIGluaXRpYWxDbGllbnRPZmZzZXQpO1xufSJdLCJuYW1lcyI6WyJhZGQiLCJhIiwiYiIsIngiLCJ5Iiwic3VidHJhY3QiLCJnZXRTb3VyY2VDbGllbnRPZmZzZXQiLCJzdGF0ZSIsImNsaWVudE9mZnNldCIsImluaXRpYWxDbGllbnRPZmZzZXQiLCJpbml0aWFsU291cmNlQ2xpZW50T2Zmc2V0IiwiZ2V0RGlmZmVyZW5jZUZyb21Jbml0aWFsT2Zmc2V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/utils/coords.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/utils/dirtiness.js":
/*!***********************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/utils/dirtiness.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL: () => (/* binding */ ALL),\n/* harmony export */   NONE: () => (/* binding */ NONE),\n/* harmony export */   areDirty: () => (/* binding */ areDirty)\n/* harmony export */ });\n/* harmony import */ var _js_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./js_utils */ \"(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\");\n\nvar NONE = [];\nvar ALL = [];\nNONE.__IS_NONE__ = true;\nALL.__IS_ALL__ = true;\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */ function areDirty(dirtyIds, handlerIds) {\n    if (dirtyIds === NONE) {\n        return false;\n    }\n    if (dirtyIds === ALL || typeof handlerIds === \"undefined\") {\n        return true;\n    }\n    var commonIds = (0,_js_utils__WEBPACK_IMPORTED_MODULE_0__.intersection)(handlerIds, dirtyIds);\n    return commonIds.length > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vdXRpbHMvZGlydGluZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEM7QUFDbkMsSUFBSUMsT0FBTyxFQUFFLENBQUM7QUFDZCxJQUFJQyxNQUFNLEVBQUUsQ0FBQztBQUNwQkQsS0FBS0UsV0FBVyxHQUFHO0FBQ25CRCxJQUFJRSxVQUFVLEdBQUc7QUFDakI7Ozs7O0NBS0MsR0FFTSxTQUFTQyxTQUFTQyxRQUFRLEVBQUVDLFVBQVU7SUFDM0MsSUFBSUQsYUFBYUwsTUFBTTtRQUNyQixPQUFPO0lBQ1Q7SUFFQSxJQUFJSyxhQUFhSixPQUFPLE9BQU9LLGVBQWUsYUFBYTtRQUN6RCxPQUFPO0lBQ1Q7SUFFQSxJQUFJQyxZQUFZUix1REFBWUEsQ0FBQ08sWUFBWUQ7SUFDekMsT0FBT0UsVUFBVUMsTUFBTSxHQUFHO0FBQzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL3V0aWxzL2RpcnRpbmVzcy5qcz8zNDVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludGVyc2VjdGlvbiB9IGZyb20gJy4vanNfdXRpbHMnO1xuZXhwb3J0IHZhciBOT05FID0gW107XG5leHBvcnQgdmFyIEFMTCA9IFtdO1xuTk9ORS5fX0lTX05PTkVfXyA9IHRydWU7XG5BTEwuX19JU19BTExfXyA9IHRydWU7XG4vKipcbiAqIERldGVybWluZXMgaWYgdGhlIGdpdmVuIGhhbmRsZXIgSURzIGFyZSBkaXJ0eSBvciBub3QuXG4gKlxuICogQHBhcmFtIGRpcnR5SWRzIFRoZSBzZXQgb2YgZGlydHkgaGFuZGxlciBpZHNcbiAqIEBwYXJhbSBoYW5kbGVySWRzIFRoZSBzZXQgb2YgaGFuZGxlciBpZHMgdG8gY2hlY2tcbiAqL1xuXG5leHBvcnQgZnVuY3Rpb24gYXJlRGlydHkoZGlydHlJZHMsIGhhbmRsZXJJZHMpIHtcbiAgaWYgKGRpcnR5SWRzID09PSBOT05FKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgaWYgKGRpcnR5SWRzID09PSBBTEwgfHwgdHlwZW9mIGhhbmRsZXJJZHMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cblxuICB2YXIgY29tbW9uSWRzID0gaW50ZXJzZWN0aW9uKGhhbmRsZXJJZHMsIGRpcnR5SWRzKTtcbiAgcmV0dXJuIGNvbW1vbklkcy5sZW5ndGggPiAwO1xufSJdLCJuYW1lcyI6WyJpbnRlcnNlY3Rpb24iLCJOT05FIiwiQUxMIiwiX19JU19OT05FX18iLCJfX0lTX0FMTF9fIiwiYXJlRGlydHkiLCJkaXJ0eUlkcyIsImhhbmRsZXJJZHMiLCJjb21tb25JZHMiLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/utils/dirtiness.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/utils/equality.js":
/*!**********************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/utils/equality.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areArraysEqual: () => (/* binding */ areArraysEqual),\n/* harmony export */   areCoordsEqual: () => (/* binding */ areCoordsEqual),\n/* harmony export */   strictEquality: () => (/* binding */ strictEquality)\n/* harmony export */ });\nvar strictEquality = function strictEquality(a, b) {\n    return a === b;\n};\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */ function areCoordsEqual(offsetA, offsetB) {\n    if (!offsetA && !offsetB) {\n        return true;\n    } else if (!offsetA || !offsetB) {\n        return false;\n    } else {\n        return offsetA.x === offsetB.x && offsetA.y === offsetB.y;\n    }\n}\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */ function areArraysEqual(a, b) {\n    var isEqual = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : strictEquality;\n    if (a.length !== b.length) {\n        return false;\n    }\n    for(var i = 0; i < a.length; ++i){\n        if (!isEqual(a[i], b[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/utils/equality.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/utils/getNextUniqueId.js":
/*!*****************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/utils/getNextUniqueId.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNextUniqueId: () => (/* binding */ getNextUniqueId)\n/* harmony export */ });\nvar nextUniqueId = 0;\nfunction getNextUniqueId() {\n    return nextUniqueId++;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vdXRpbHMvZ2V0TmV4dFVuaXF1ZUlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxlQUFlO0FBQ1osU0FBU0M7SUFDZCxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldE5leHRVbmlxdWVJZC5qcz8yMzhmIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBuZXh0VW5pcXVlSWQgPSAwO1xuZXhwb3J0IGZ1bmN0aW9uIGdldE5leHRVbmlxdWVJZCgpIHtcbiAgcmV0dXJuIG5leHRVbmlxdWVJZCsrO1xufSJdLCJuYW1lcyI6WyJuZXh0VW5pcXVlSWQiLCJnZXROZXh0VW5pcXVlSWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/utils/getNextUniqueId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/utils/js_utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   intersection: () => (/* binding */ intersection),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   without: () => (/* binding */ without),\n/* harmony export */   xor: () => (/* binding */ xor)\n/* harmony export */ });\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n        _typeof = function _typeof(obj) {\n            return typeof obj;\n        };\n    } else {\n        _typeof = function _typeof(obj) {\n            return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n        };\n    }\n    return _typeof(obj);\n}\n// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */ function get(obj, path, defaultValue) {\n    return path.split(\".\").reduce(function(a, c) {\n        return a && a[c] ? a[c] : defaultValue || null;\n    }, obj);\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter(function(i) {\n        return i !== item;\n    });\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isString(input) {\n    return typeof input === \"string\";\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */ function isObject(input) {\n    return _typeof(input) === \"object\";\n}\n/**\n * repalcement for _.xor\n * @param itemsA\n * @param itemsB\n */ function xor(itemsA, itemsB) {\n    var map = new Map();\n    var insertItem = function insertItem(item) {\n        map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n    };\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    var result = [];\n    map.forEach(function(count, key) {\n        if (count === 1) {\n            result.push(key);\n        }\n    });\n    return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */ function intersection(itemsA, itemsB) {\n    return itemsA.filter(function(t) {\n        return itemsB.indexOf(t) > -1;\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/utils/js_utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/dist/esm/utils/matchesType.js":
/*!*************************************************************!*\
  !*** ./node_modules/dnd-core/dist/esm/utils/matchesType.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchesType: () => (/* binding */ matchesType)\n/* harmony export */ });\nfunction matchesType(targetType, draggedItemType) {\n    if (draggedItemType === null) {\n        return targetType === null;\n    }\n    return Array.isArray(targetType) ? targetType.some(function(t) {\n        return t === draggedItemType;\n    }) : targetType === draggedItemType;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG5kLWNvcmUvZGlzdC9lc20vdXRpbHMvbWF0Y2hlc1R5cGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLFlBQVlDLFVBQVUsRUFBRUMsZUFBZTtJQUNyRCxJQUFJQSxvQkFBb0IsTUFBTTtRQUM1QixPQUFPRCxlQUFlO0lBQ3hCO0lBRUEsT0FBT0UsTUFBTUMsT0FBTyxDQUFDSCxjQUFjQSxXQUFXSSxJQUFJLENBQUMsU0FBVUMsQ0FBQztRQUM1RCxPQUFPQSxNQUFNSjtJQUNmLEtBQUtELGVBQWVDO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2RuZC1jb3JlL2Rpc3QvZXNtL3V0aWxzL21hdGNoZXNUeXBlLmpzP2Y1ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIG1hdGNoZXNUeXBlKHRhcmdldFR5cGUsIGRyYWdnZWRJdGVtVHlwZSkge1xuICBpZiAoZHJhZ2dlZEl0ZW1UeXBlID09PSBudWxsKSB7XG4gICAgcmV0dXJuIHRhcmdldFR5cGUgPT09IG51bGw7XG4gIH1cblxuICByZXR1cm4gQXJyYXkuaXNBcnJheSh0YXJnZXRUeXBlKSA/IHRhcmdldFR5cGUuc29tZShmdW5jdGlvbiAodCkge1xuICAgIHJldHVybiB0ID09PSBkcmFnZ2VkSXRlbVR5cGU7XG4gIH0pIDogdGFyZ2V0VHlwZSA9PT0gZHJhZ2dlZEl0ZW1UeXBlO1xufSJdLCJuYW1lcyI6WyJtYXRjaGVzVHlwZSIsInRhcmdldFR5cGUiLCJkcmFnZ2VkSXRlbVR5cGUiLCJBcnJheSIsImlzQXJyYXkiLCJzb21lIiwidCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/dist/esm/utils/matchesType.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dnd-core/node_modules/redux/es/redux.js":
/*!**************************************************************!*\
  !*** ./node_modules/dnd-core/node_modules/redux/es/redux.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* binding */ ActionTypes),\n/* harmony export */   applyMiddleware: () => (/* binding */ applyMiddleware),\n/* harmony export */   bindActionCreators: () => (/* binding */ bindActionCreators),\n/* harmony export */   combineReducers: () => (/* binding */ combineReducers),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   legacy_createStore: () => (/* binding */ legacy_createStore)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */ function formatProdErrorMessage(code) {\n    return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + \"use the non-minified dev environment for full errors. \";\n}\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = function() {\n    return typeof Symbol === \"function\" && Symbol.observable || \"@@observable\";\n}();\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */ var randomString = function randomString() {\n    return Math.random().toString(36).substring(7).split(\"\").join(\".\");\n};\nvar ActionTypes = {\n    INIT: \"@@redux/INIT\" + randomString(),\n    REPLACE: \"@@redux/REPLACE\" + randomString(),\n    PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n        return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n    }\n};\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */ function isPlainObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) return false;\n    var proto = obj;\n    while(Object.getPrototypeOf(proto) !== null){\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(obj) === proto;\n}\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n    if (val === void 0) return \"undefined\";\n    if (val === null) return \"null\";\n    var type = typeof val;\n    switch(type){\n        case \"boolean\":\n        case \"string\":\n        case \"number\":\n        case \"symbol\":\n        case \"function\":\n            {\n                return type;\n            }\n    }\n    if (Array.isArray(val)) return \"array\";\n    if (isDate(val)) return \"date\";\n    if (isError(val)) return \"error\";\n    var constructorName = ctorName(val);\n    switch(constructorName){\n        case \"Symbol\":\n        case \"Promise\":\n        case \"WeakMap\":\n        case \"WeakSet\":\n        case \"Map\":\n        case \"Set\":\n            return constructorName;\n    } // other\n    return type.slice(8, -1).toLowerCase().replace(/\\s/g, \"\");\n}\nfunction ctorName(val) {\n    return typeof val.constructor === \"function\" ? val.constructor.name : null;\n}\nfunction isError(val) {\n    return val instanceof Error || typeof val.message === \"string\" && val.constructor && typeof val.constructor.stackTraceLimit === \"number\";\n}\nfunction isDate(val) {\n    if (val instanceof Date) return true;\n    return typeof val.toDateString === \"function\" && typeof val.getDate === \"function\" && typeof val.setDate === \"function\";\n}\nfunction kindOf(val) {\n    var typeOfVal = typeof val;\n    if (true) {\n        typeOfVal = miniKindOf(val);\n    }\n    return typeOfVal;\n}\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */ function createStore(reducer, preloadedState, enhancer) {\n    var _ref2;\n    if (typeof preloadedState === \"function\" && typeof enhancer === \"function\" || typeof enhancer === \"function\" && typeof arguments[3] === \"function\") {\n        throw new Error( false ? 0 : \"It looks like you are passing several store enhancers to \" + \"createStore(). This is not supported. Instead, compose them \" + \"together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.\");\n    }\n    if (typeof preloadedState === \"function\" && typeof enhancer === \"undefined\") {\n        enhancer = preloadedState;\n        preloadedState = undefined;\n    }\n    if (typeof enhancer !== \"undefined\") {\n        if (typeof enhancer !== \"function\") {\n            throw new Error( false ? 0 : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n        }\n        return enhancer(createStore)(reducer, preloadedState);\n    }\n    if (typeof reducer !== \"function\") {\n        throw new Error( false ? 0 : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n    }\n    var currentReducer = reducer;\n    var currentState = preloadedState;\n    var currentListeners = [];\n    var nextListeners = currentListeners;\n    var isDispatching = false;\n    /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */ function ensureCanMutateNextListeners() {\n        if (nextListeners === currentListeners) {\n            nextListeners = currentListeners.slice();\n        }\n    }\n    /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */ function getState() {\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"You may not call store.getState() while the reducer is executing. \" + \"The reducer has already received the state as an argument. \" + \"Pass it down from the top reducer instead of reading it from the store.\");\n        }\n        return currentState;\n    }\n    /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */ function subscribe(listener) {\n        if (typeof listener !== \"function\") {\n            throw new Error( false ? 0 : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n        }\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"You may not call store.subscribe() while the reducer is executing. \" + \"If you would like to be notified after the store has been updated, subscribe from a \" + \"component and invoke store.getState() in the callback to access the latest state. \" + \"See https://redux.js.org/api/store#subscribelistener for more details.\");\n        }\n        var isSubscribed = true;\n        ensureCanMutateNextListeners();\n        nextListeners.push(listener);\n        return function unsubscribe() {\n            if (!isSubscribed) {\n                return;\n            }\n            if (isDispatching) {\n                throw new Error( false ? 0 : \"You may not unsubscribe from a store listener while the reducer is executing. \" + \"See https://redux.js.org/api/store#subscribelistener for more details.\");\n            }\n            isSubscribed = false;\n            ensureCanMutateNextListeners();\n            var index = nextListeners.indexOf(listener);\n            nextListeners.splice(index, 1);\n            currentListeners = null;\n        };\n    }\n    /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */ function dispatch(action) {\n        if (!isPlainObject(action)) {\n            throw new Error( false ? 0 : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n        }\n        if (typeof action.type === \"undefined\") {\n            throw new Error( false ? 0 : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n        }\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"Reducers may not dispatch actions.\");\n        }\n        try {\n            isDispatching = true;\n            currentState = currentReducer(currentState, action);\n        } finally{\n            isDispatching = false;\n        }\n        var listeners = currentListeners = nextListeners;\n        for(var i = 0; i < listeners.length; i++){\n            var listener = listeners[i];\n            listener();\n        }\n        return action;\n    }\n    /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */ function replaceReducer(nextReducer) {\n        if (typeof nextReducer !== \"function\") {\n            throw new Error( false ? 0 : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n        }\n        currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n        // Any reducers that existed in both the new and old rootReducer\n        // will receive the previous state. This effectively populates\n        // the new state tree with any relevant data from the old one.\n        dispatch({\n            type: ActionTypes.REPLACE\n        });\n    }\n    /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */ function observable() {\n        var _ref;\n        var outerSubscribe = subscribe;\n        return _ref = {\n            /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */ subscribe: function subscribe(observer) {\n                if (typeof observer !== \"object\" || observer === null) {\n                    throw new Error( false ? 0 : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n                }\n                function observeState() {\n                    if (observer.next) {\n                        observer.next(getState());\n                    }\n                }\n                observeState();\n                var unsubscribe = outerSubscribe(observeState);\n                return {\n                    unsubscribe: unsubscribe\n                };\n            }\n        }, _ref[$$observable] = function() {\n            return this;\n        }, _ref;\n    } // When a store is created, an \"INIT\" action is dispatched so that every\n    // reducer returns their initial state. This effectively populates\n    // the initial state tree.\n    dispatch({\n        type: ActionTypes.INIT\n    });\n    return _ref2 = {\n        dispatch: dispatch,\n        subscribe: subscribe,\n        getState: getState,\n        replaceReducer: replaceReducer\n    }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */ var legacy_createStore = createStore;\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */ function warning(message) {\n    /* eslint-disable no-console */ if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        console.error(message);\n    }\n    /* eslint-enable no-console */ try {\n        // This error was thrown as a convenience so that if you enable\n        // \"break on all exceptions\" in your console,\n        // it would pause the execution at this line.\n        throw new Error(message);\n    } catch (e) {} // eslint-disable-line no-empty\n}\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n    var reducerKeys = Object.keys(reducers);\n    var argumentName = action && action.type === ActionTypes.INIT ? \"preloadedState argument passed to createStore\" : \"previous state received by the reducer\";\n    if (reducerKeys.length === 0) {\n        return \"Store does not have a valid reducer. Make sure the argument passed \" + \"to combineReducers is an object whose values are reducers.\";\n    }\n    if (!isPlainObject(inputState)) {\n        return \"The \" + argumentName + ' has unexpected type of \"' + kindOf(inputState) + '\". Expected argument to be an object with the following ' + ('keys: \"' + reducerKeys.join('\", \"') + '\"');\n    }\n    var unexpectedKeys = Object.keys(inputState).filter(function(key) {\n        return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n    });\n    unexpectedKeys.forEach(function(key) {\n        unexpectedKeyCache[key] = true;\n    });\n    if (action && action.type === ActionTypes.REPLACE) return;\n    if (unexpectedKeys.length > 0) {\n        return \"Unexpected \" + (unexpectedKeys.length > 1 ? \"keys\" : \"key\") + \" \" + ('\"' + unexpectedKeys.join('\", \"') + '\" found in ' + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + ('\"' + reducerKeys.join('\", \"') + '\". Unexpected keys will be ignored.');\n    }\n}\nfunction assertReducerShape(reducers) {\n    Object.keys(reducers).forEach(function(key) {\n        var reducer = reducers[key];\n        var initialState = reducer(undefined, {\n            type: ActionTypes.INIT\n        });\n        if (typeof initialState === \"undefined\") {\n            throw new Error( false ? 0 : 'The slice reducer for key \"' + key + '\" returned undefined during initialization. ' + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n        }\n        if (typeof reducer(undefined, {\n            type: ActionTypes.PROBE_UNKNOWN_ACTION()\n        }) === \"undefined\") {\n            throw new Error( false ? 0 : 'The slice reducer for key \"' + key + '\" returned undefined when probed with a random type. ' + (\"Don't try to handle '\" + ActionTypes.INIT + '\\' or other actions in \"redux/*\" ') + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n        }\n    });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */ function combineReducers(reducers) {\n    var reducerKeys = Object.keys(reducers);\n    var finalReducers = {};\n    for(var i = 0; i < reducerKeys.length; i++){\n        var key = reducerKeys[i];\n        if (true) {\n            if (typeof reducers[key] === \"undefined\") {\n                warning('No reducer provided for key \"' + key + '\"');\n            }\n        }\n        if (typeof reducers[key] === \"function\") {\n            finalReducers[key] = reducers[key];\n        }\n    }\n    var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n    // keys multiple times.\n    var unexpectedKeyCache;\n    if (true) {\n        unexpectedKeyCache = {};\n    }\n    var shapeAssertionError;\n    try {\n        assertReducerShape(finalReducers);\n    } catch (e) {\n        shapeAssertionError = e;\n    }\n    return function combination(state, action) {\n        if (state === void 0) {\n            state = {};\n        }\n        if (shapeAssertionError) {\n            throw shapeAssertionError;\n        }\n        if (true) {\n            var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n            if (warningMessage) {\n                warning(warningMessage);\n            }\n        }\n        var hasChanged = false;\n        var nextState = {};\n        for(var _i = 0; _i < finalReducerKeys.length; _i++){\n            var _key = finalReducerKeys[_i];\n            var reducer = finalReducers[_key];\n            var previousStateForKey = state[_key];\n            var nextStateForKey = reducer(previousStateForKey, action);\n            if (typeof nextStateForKey === \"undefined\") {\n                var actionType = action && action.type;\n                throw new Error( false ? 0 : \"When called with an action of type \" + (actionType ? '\"' + String(actionType) + '\"' : \"(unknown type)\") + ', the slice reducer for key \"' + _key + '\" returned undefined. ' + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n            }\n            nextState[_key] = nextStateForKey;\n            hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n        }\n        hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n        return hasChanged ? nextState : state;\n    };\n}\nfunction bindActionCreator(actionCreator, dispatch) {\n    return function() {\n        return dispatch(actionCreator.apply(this, arguments));\n    };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */ function bindActionCreators(actionCreators, dispatch) {\n    if (typeof actionCreators === \"function\") {\n        return bindActionCreator(actionCreators, dispatch);\n    }\n    if (typeof actionCreators !== \"object\" || actionCreators === null) {\n        throw new Error( false ? 0 : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + 'Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?');\n    }\n    var boundActionCreators = {};\n    for(var key in actionCreators){\n        var actionCreator = actionCreators[key];\n        if (typeof actionCreator === \"function\") {\n            boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n        }\n    }\n    return boundActionCreators;\n}\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */ function compose() {\n    for(var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++){\n        funcs[_key] = arguments[_key];\n    }\n    if (funcs.length === 0) {\n        return function(arg) {\n            return arg;\n        };\n    }\n    if (funcs.length === 1) {\n        return funcs[0];\n    }\n    return funcs.reduce(function(a, b) {\n        return function() {\n            return a(b.apply(void 0, arguments));\n        };\n    });\n}\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */ function applyMiddleware() {\n    for(var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++){\n        middlewares[_key] = arguments[_key];\n    }\n    return function(createStore) {\n        return function() {\n            var store = createStore.apply(void 0, arguments);\n            var _dispatch = function dispatch() {\n                throw new Error( false ? 0 : \"Dispatching while constructing your middleware is not allowed. \" + \"Other middleware would not be applied to this dispatch.\");\n            };\n            var middlewareAPI = {\n                getState: store.getState,\n                dispatch: function dispatch() {\n                    return _dispatch.apply(void 0, arguments);\n                }\n            };\n            var chain = middlewares.map(function(middleware) {\n                return middleware(middlewareAPI);\n            });\n            _dispatch = compose.apply(void 0, chain)(store.dispatch);\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, store), {}, {\n                dispatch: _dispatch\n            });\n        };\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dnd-core/node_modules/redux/es/redux.js\n");

/***/ })

};
;