"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/markdown-content.tsx":
/*!*****************************************!*\
  !*** ./components/markdown-content.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownContent: function() { return /* binding */ MarkdownContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ MarkdownContent auto */ \n\n\n\nfunction MarkdownContent(param) {\n    let { content, className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"prose prose-invert prose-sm max-w-none \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_1__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_highlight__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            ],\n            components: {\n                // Custom styling for code blocks\n                code: (param)=>{\n                    let { node, inline, className, children, ...props } = param;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-[#1e1e1e] border border-[#333] rounded-lg p-4 overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 15\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-[#3c3c3c] px-1.5 py-0.5 rounded text-[#f8f8f2] font-mono text-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom styling for blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-[#0e639c] pl-4 italic text-[#cccccc] bg-[#2d2d30] py-2 rounded-r\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Custom styling for tables\n                table: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-[#333] rounded-lg\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                th: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"border border-[#333] bg-[#2d2d30] px-3 py-2 text-left font-semibold text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                td: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"border border-[#333] px-3 py-2 text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Custom styling for links\n                a: (param)=>{\n                    let { href, children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        className: \"text-[#0e639c] hover:text-[#1177bb] underline\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Custom styling for headings\n                h1: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-[#cccccc] mb-4 border-b border-[#333] pb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                h2: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-[#cccccc] mb-3 border-b border-[#333] pb-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                h3: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-[#cccccc] mb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Custom styling for lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-1 text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside space-y-1 text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Custom styling for paragraphs\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#cccccc] leading-relaxed mb-3\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Custom styling for horizontal rules\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-[#333] my-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = MarkdownContent;\nvar _c;\n$RefreshReg$(_c, \"MarkdownContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/markdown-content.tsx\n"));

/***/ })

});