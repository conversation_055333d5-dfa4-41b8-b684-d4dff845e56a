'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  ArrowLeft,
  Code,
  Code2,
  Eye,
  FileText,
  MoreVertical,
  Share,
  Settings,
  GitBranch
} from 'lucide-react'
import { type Chat, type Version } from '@/lib/api'

interface ChatHeaderProps {
  chat: Chat
  versions: Version[]
  currentVersion: Version | null
  onVersionChange: (version: Version) => void
  showPreview: boolean
  onTogglePreview: (show: boolean) => void
}

export function ChatHeader({
  chat,
  versions,
  currentVersion,
  onVersionChange,
  showPreview,
  onTogglePreview
}: ChatHeaderProps) {
  const { user } = useAuth()
  const router = useRouter()

  const goBack = () => {
    router.push('/dashboard')
  }

  return (
    <div className="bg-[#1e1e1e] border-b border-[#333]">
      {/* Top Bar - VS Code Style */}
      <div className="flex items-center justify-between px-4 py-2 bg-[#2d2d30] border-b border-[#333]">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          <button
            onClick={goBack}
            className="p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
          </button>

          {/* Sidebar Toggle */}
          <button className="p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </button>

          {/* File Explorer Toggle */}
          <button className="flex items-center space-x-1 px-2 py-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
            </svg>
            <span className="text-xs">Explorer</span>
          </button>

          {/* Preview Toggle */}
          <button
            onClick={() => onTogglePreview(!showPreview)}
            className={`flex items-center space-x-1 px-2 py-1 rounded transition-colors ${
              showPreview
                ? 'bg-[#0e639c] text-white'
                : 'text-[#cccccc] hover:bg-[#3c3c3c]'
            }`}
          >
            <Eye className="w-4 h-4" />
            <span className="text-xs">Preview</span>
          </button>

          {/* Code Toggle */}
          <button
            onClick={() => onTogglePreview(false)}
            className={`flex items-center space-x-1 px-2 py-1 rounded transition-colors ${
              !showPreview
                ? 'bg-[#0e639c] text-white'
                : 'text-[#cccccc] hover:bg-[#3c3c3c]'
            }`}
          >
            <Code2 className="w-4 h-4" />
            <span className="text-xs">Code</span>
          </button>
        </div>

        {/* Center - Project Name */}
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center">
            <Code className="w-3 h-3 text-white" />
          </div>
          <span className="text-sm text-[#cccccc] font-medium">{chat.name}</span>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2">
          {/* Version Selector */}
          {versions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center space-x-1 px-2 py-1 bg-[#3c3c3c] hover:bg-[#4c4c4c] rounded text-[#cccccc] text-xs transition-colors">
                  <span>v{currentVersion?.version_number || 1}</span>
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48 bg-[#252526] border-[#3c3c3c]">
                {versions.map((version) => (
                  <DropdownMenuItem
                    key={version.id}
                    onClick={() => onVersionChange(version)}
                    className={`text-[#cccccc] hover:bg-[#3c3c3c] ${currentVersion?.id === version.id ? "bg-[#3c3c3c]" : ""}`}
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">Version {version.version_number}</span>
                      {version.description && (
                        <span className="text-xs text-[#858585]">
                          {version.description}
                        </span>
                      )}
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Download Button */}
          <button className="p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>

          {/* More Options */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors">
                <MoreVertical className="w-4 h-4" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-[#252526] border-[#3c3c3c]">
              <DropdownMenuItem className="text-[#cccccc] hover:bg-[#3c3c3c]">
                <Share className="w-4 h-4 mr-2" />
                Share Chat
              </DropdownMenuItem>
              <DropdownMenuItem className="text-[#cccccc] hover:bg-[#3c3c3c]">
                <Settings className="w-4 h-4 mr-2" />
                Chat Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Tab Bar - VS Code Style */}
      <div className="flex items-center bg-[#252526]">
        <div className="flex items-center">
          <div
            className={`flex items-center space-x-2 px-4 py-2 text-sm border-r border-[#333] transition-colors ${
              !showPreview
                ? 'bg-[#1e1e1e] text-[#cccccc] border-b-2 border-b-[#0e639c]'
                : 'text-[#858585] hover:text-[#cccccc] hover:bg-[#2d2d30] cursor-pointer'
            }`}
            onClick={() => onTogglePreview(false)}
          >
            <Code2 className="w-4 h-4" />
            <span>globals.css</span>
            <span className="text-xs text-[#4ec9b0]">+5 / -5</span>
          </div>
          <div
            className={`flex items-center space-x-2 px-4 py-2 text-sm border-r border-[#333] transition-colors ${
              showPreview
                ? 'bg-[#1e1e1e] text-[#cccccc] border-b-2 border-b-[#0e639c]'
                : 'text-[#858585] hover:text-[#cccccc] hover:bg-[#2d2d30] cursor-pointer'
            }`}
            onClick={() => onTogglePreview(true)}
          >
            <Eye className="w-4 h-4" />
            <span>Preview</span>
          </div>
        </div>
      </div>
    </div>
  )
}
