"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-footnote";
exports.ids = ["vendor-chunks/mdast-util-gfm-footnote"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-gfm-footnote/lib/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnoteFromMarkdown: () => (/* binding */ gfmFootnoteFromMarkdown),\n/* harmony export */   gfmFootnoteToMarkdown: () => (/* binding */ gfmFootnoteToMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/**\n * @import {\n *   CompileContext,\n *   Extension as FromMarkdownExtension,\n *   Handle as FromMarkdownHandle\n * } from 'mdast-util-from-markdown'\n * @import {ToMarkdownOptions} from 'mdast-util-gfm-footnote'\n * @import {\n *   Handle as ToMarkdownHandle,\n *   Map,\n *   Options as ToMarkdownExtension\n * } from 'mdast-util-to-markdown'\n * @import {FootnoteDefinition, FootnoteReference} from 'mdast'\n */ \n\nfootnoteReference.peek = footnoteReferencePeek;\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteCallString() {\n    this.buffer();\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteCall(token) {\n    this.enter({\n        type: \"footnoteReference\",\n        identifier: \"\",\n        label: \"\"\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteDefinitionLabelString() {\n    this.buffer();\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function enterFootnoteDefinition(token) {\n    this.enter({\n        type: \"footnoteDefinition\",\n        identifier: \"\",\n        label: \"\",\n        children: []\n    }, token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteCallString(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"footnoteReference\");\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n    node.label = label;\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteCall(token) {\n    this.exit(token);\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteDefinitionLabelString(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"footnoteDefinition\");\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n    node.label = label;\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitFootnoteDefinition(token) {\n    this.exit(token);\n}\n/** @type {ToMarkdownHandle} */ function footnoteReferencePeek() {\n    return \"[\";\n}\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteReference} node\n */ function footnoteReference(node, _, state, info) {\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[^\");\n    const exit = state.enter(\"footnoteReference\");\n    const subexit = state.enter(\"reference\");\n    value += tracker.move(state.safe(state.associationId(node), {\n        after: \"]\",\n        before: value\n    }));\n    subexit();\n    exit();\n    value += tracker.move(\"]\");\n    return value;\n}\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */ function gfmFootnoteFromMarkdown() {\n    return {\n        enter: {\n            gfmFootnoteCallString: enterFootnoteCallString,\n            gfmFootnoteCall: enterFootnoteCall,\n            gfmFootnoteDefinitionLabelString: enterFootnoteDefinitionLabelString,\n            gfmFootnoteDefinition: enterFootnoteDefinition\n        },\n        exit: {\n            gfmFootnoteCallString: exitFootnoteCallString,\n            gfmFootnoteCall: exitFootnoteCall,\n            gfmFootnoteDefinitionLabelString: exitFootnoteDefinitionLabelString,\n            gfmFootnoteDefinition: exitFootnoteDefinition\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @param {ToMarkdownOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */ function gfmFootnoteToMarkdown(options) {\n    // To do: next major: change default.\n    let firstLineBlank = false;\n    if (options && options.firstLineBlank) {\n        firstLineBlank = true;\n    }\n    return {\n        handlers: {\n            footnoteDefinition,\n            footnoteReference\n        },\n        // This is on by default already.\n        unsafe: [\n            {\n                character: \"[\",\n                inConstruct: [\n                    \"label\",\n                    \"phrasing\",\n                    \"reference\"\n                ]\n            }\n        ]\n    };\n    /**\n   * @type {ToMarkdownHandle}\n   * @param {FootnoteDefinition} node\n   */ function footnoteDefinition(node, _, state, info) {\n        const tracker = state.createTracker(info);\n        let value = tracker.move(\"[^\");\n        const exit = state.enter(\"footnoteDefinition\");\n        const subexit = state.enter(\"label\");\n        value += tracker.move(state.safe(state.associationId(node), {\n            before: value,\n            after: \"]\"\n        }));\n        subexit();\n        value += tracker.move(\"]:\");\n        if (node.children && node.children.length > 0) {\n            tracker.shift(4);\n            value += tracker.move((firstLineBlank ? \"\\n\" : \" \") + state.indentLines(state.containerFlow(node, tracker.current()), firstLineBlank ? mapAll : mapExceptFirst));\n        }\n        exit();\n        return value;\n    }\n}\n/** @type {Map} */ function mapExceptFirst(line, index, blank) {\n    return index === 0 ? line : mapAll(line, index, blank);\n}\n/** @type {Map} */ function mapAll(line, index, blank) {\n    return (blank ? \"\" : \"    \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js\n");

/***/ })

};
;