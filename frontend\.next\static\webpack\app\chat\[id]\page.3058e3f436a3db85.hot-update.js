"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(app-pages-browser)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(app-pages-browser)/./components/website-preview.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage(param) {\n    let { params } = param;\n    var _currentVersion_id;\n    _s();\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return \"javascript\";\n            case \"ts\":\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    if (authLoading || !user && !authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: chat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (currentVersion === null || currentVersion === void 0 ? void 0 : (_currentVersion_id = currentVersion.id) === null || _currentVersion_id === void 0 ? void 0 : _currentVersion_id.toString()) || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id.toString() === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id.toString(),\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 bg-[#0d1117] border-r border-[#21262d]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                    version: currentVersion,\n                                    onFileSelect: handleFileSelect\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-[#0d1117]\",\n                                children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                    version: currentVersion\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center px-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-[#7d8590]\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#f0f6fc] text-sm font-medium\",\n                                                        children: selectedFile ? selectedFile.path.split(\"/\").pop() : \"No file selected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                height: \"100%\",\n                                                language: getFileLanguage(selectedFile.path),\n                                                value: selectedFile.content || \"// No content available\",\n                                                theme: \"vs-dark\",\n                                                options: {\n                                                    readOnly: true,\n                                                    minimap: {\n                                                        enabled: false\n                                                    },\n                                                    scrollBeyondLastLine: false,\n                                                    fontSize: 14,\n                                                    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n                                                    lineNumbers: \"on\",\n                                                    glyphMargin: false,\n                                                    folding: true,\n                                                    lineDecorationsWidth: 0,\n                                                    lineNumbersMinChars: 3,\n                                                    renderLineHighlight: \"line\",\n                                                    selectOnLineNumbers: true,\n                                                    wordWrap: \"on\",\n                                                    automaticLayout: true\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 mx-auto mb-4 text-[#7d8590]\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-[#f0f6fc] mb-2\",\n                                                            children: \"Code Editor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[#7d8590]\",\n                                                            children: \"Select a file from the explorer to view its content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"851n8SFiz3SRoLlPT4KA3In4dKg=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chat/[id]/page.tsx\n"));

/***/ })

});