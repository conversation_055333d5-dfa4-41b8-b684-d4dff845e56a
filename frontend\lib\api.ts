import axios from 'axios'

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Auth API
export const authAPI = {
  register: async (userData: {
    full_name: string
    username: string
    email: string
    password: string
  }) => {
    const response = await api.post('/auth/register', userData)
    return response.data
  },

  login: async (credentials: {
    username_or_email: string
    password: string
  }) => {
    const response = await api.post('/auth/login', credentials)
    return response.data
  },
}

// Chat API
export const chatAPI = {
  getChats: async () => {
    const response = await api.get('/chats/')
    return response.data
  },

  createChat: async (chatData: {
    name: string
    description?: string
  }) => {
    const response = await api.post('/chats/', chatData)
    return response.data
  },

  getChat: async (chatId: number) => {
    const response = await api.get(`/chats/${chatId}`)
    return response.data
  },

  getChatHistory: async (chatId: number) => {
    const response = await api.get(`/chat/${chatId}/history`)
    return response.data
  },

  sendMessage: async (chatId: number, content: string) => {
    // This returns a stream, handle differently
    return api.post(`/chat/${chatId}/message`, { content }, {
      responseType: 'stream'
    })
  },

  getVersionPreview: async (chatId: number, versionId: number) => {
    const response = await api.get(`/chat/${chatId}/versions/${versionId}/preview`)
    return response.data
  },

  getChatVersions: async (chatId: number) => {
    const response = await api.get(`/chats/${chatId}/versions`)
    return response.data
  },
}

// Types
export interface User {
  id: number
  username: string
  email: string
  full_name: string
  created_at: string
}

export interface Chat {
  id: number
  user_id: number
  name: string
  description?: string
  is_public: boolean
  share_token: string
  created_at: string
  updated_at: string
}

export interface Message {
  id: number
  chat_id: number
  version_id?: number
  content: string
  role: 'user' | 'assistant'
  parent_message_id?: number
  created_at: string
}

export interface Version {
  id: number
  chat_id: number
  version_number: number
  trigger_message_id?: number
  description?: string
  created_at: string
  files: File[]
}

export interface File {
  id: number
  version_id: number
  path: string
  content?: string
  file_type?: string
  created_at: string
}
