"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dnd-html5-backend";
exports.ids = ["vendor-chunks/react-dnd-html5-backend"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/BrowserDetector.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/BrowserDetector.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFirefox: () => (/* binding */ isFirefox),\n/* harmony export */   isSafari: () => (/* binding */ isSafari)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/js_utils */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/utils/js_utils.js\");\n\nvar isFirefox = (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n    return /firefox/i.test(navigator.userAgent);\n});\nvar isSafari = (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n    return Boolean(window.safari);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9lc20vQnJvd3NlckRldGVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQztBQUNwQyxJQUFJQyxZQUFZRCx3REFBT0EsQ0FBQztJQUM3QixPQUFPLFdBQVdFLElBQUksQ0FBQ0MsVUFBVUMsU0FBUztBQUM1QyxHQUFHO0FBQ0ksSUFBSUMsV0FBV0wsd0RBQU9BLENBQUM7SUFDNUIsT0FBT00sUUFBUUMsT0FBT0MsTUFBTTtBQUM5QixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvZXNtL0Jyb3dzZXJEZXRlY3Rvci5qcz80OTEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW9pemUgfSBmcm9tICcuL3V0aWxzL2pzX3V0aWxzJztcbmV4cG9ydCB2YXIgaXNGaXJlZm94ID0gbWVtb2l6ZShmdW5jdGlvbiAoKSB7XG4gIHJldHVybiAvZmlyZWZveC9pLnRlc3QobmF2aWdhdG9yLnVzZXJBZ2VudCk7XG59KTtcbmV4cG9ydCB2YXIgaXNTYWZhcmkgPSBtZW1vaXplKGZ1bmN0aW9uICgpIHtcbiAgcmV0dXJuIEJvb2xlYW4od2luZG93LnNhZmFyaSk7XG59KTsiXSwibmFtZXMiOlsibWVtb2l6ZSIsImlzRmlyZWZveCIsInRlc3QiLCJuYXZpZ2F0b3IiLCJ1c2VyQWdlbnQiLCJpc1NhZmFyaSIsIkJvb2xlYW4iLCJ3aW5kb3ciLCJzYWZhcmkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/BrowserDetector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/EnterLeaveCounter.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/EnterLeaveCounter.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnterLeaveCounter: () => (/* binding */ EnterLeaveCounter)\n/* harmony export */ });\n/* harmony import */ var _utils_js_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/js_utils */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/utils/js_utils.js\");\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\nvar EnterLeaveCounter = /*#__PURE__*/ function() {\n    function EnterLeaveCounter(isNodeInDocument) {\n        _classCallCheck(this, EnterLeaveCounter);\n        _defineProperty(this, \"entered\", []);\n        _defineProperty(this, \"isNodeInDocument\", void 0);\n        this.isNodeInDocument = isNodeInDocument;\n    }\n    _createClass(EnterLeaveCounter, [\n        {\n            key: \"enter\",\n            value: function enter(enteringNode) {\n                var _this = this;\n                var previousLength = this.entered.length;\n                var isNodeEntered = function isNodeEntered(node) {\n                    return _this.isNodeInDocument(node) && (!node.contains || node.contains(enteringNode));\n                };\n                this.entered = (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_0__.union)(this.entered.filter(isNodeEntered), [\n                    enteringNode\n                ]);\n                return previousLength === 0 && this.entered.length > 0;\n            }\n        },\n        {\n            key: \"leave\",\n            value: function leave(leavingNode) {\n                var previousLength = this.entered.length;\n                this.entered = (0,_utils_js_utils__WEBPACK_IMPORTED_MODULE_0__.without)(this.entered.filter(this.isNodeInDocument), leavingNode);\n                return previousLength > 0 && this.entered.length === 0;\n            }\n        },\n        {\n            key: \"reset\",\n            value: function reset() {\n                this.entered = [];\n            }\n        }\n    ]);\n    return EnterLeaveCounter;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/EnterLeaveCounter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/HTML5BackendImpl.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/HTML5BackendImpl.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTML5BackendImpl: () => (/* binding */ HTML5BackendImpl)\n/* harmony export */ });\n/* harmony import */ var _EnterLeaveCounter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EnterLeaveCounter */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/EnterLeaveCounter.js\");\n/* harmony import */ var _OffsetUtils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./OffsetUtils */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/OffsetUtils.js\");\n/* harmony import */ var _NativeDragSources__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeDragSources */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/index.js\");\n/* harmony import */ var _NativeTypes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NativeTypes */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js\");\n/* harmony import */ var _OptionsReader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OptionsReader */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/OptionsReader.js\");\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) {\n            symbols = symbols.filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n            });\n        }\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\n\n\n\nvar HTML5BackendImpl = /*#__PURE__*/ function() {\n    // React-Dnd Components\n    // Internal State\n    function HTML5BackendImpl(manager, globalContext, options) {\n        var _this = this;\n        _classCallCheck(this, HTML5BackendImpl);\n        _defineProperty(this, \"options\", void 0);\n        _defineProperty(this, \"actions\", void 0);\n        _defineProperty(this, \"monitor\", void 0);\n        _defineProperty(this, \"registry\", void 0);\n        _defineProperty(this, \"enterLeaveCounter\", void 0);\n        _defineProperty(this, \"sourcePreviewNodes\", new Map());\n        _defineProperty(this, \"sourcePreviewNodeOptions\", new Map());\n        _defineProperty(this, \"sourceNodes\", new Map());\n        _defineProperty(this, \"sourceNodeOptions\", new Map());\n        _defineProperty(this, \"dragStartSourceIds\", null);\n        _defineProperty(this, \"dropTargetIds\", []);\n        _defineProperty(this, \"dragEnterTargetIds\", []);\n        _defineProperty(this, \"currentNativeSource\", null);\n        _defineProperty(this, \"currentNativeHandle\", null);\n        _defineProperty(this, \"currentDragSourceNode\", null);\n        _defineProperty(this, \"altKeyPressed\", false);\n        _defineProperty(this, \"mouseMoveTimeoutTimer\", null);\n        _defineProperty(this, \"asyncEndDragFrameId\", null);\n        _defineProperty(this, \"dragOverTargetIds\", null);\n        _defineProperty(this, \"lastClientOffset\", null);\n        _defineProperty(this, \"hoverRafId\", null);\n        _defineProperty(this, \"getSourceClientOffset\", function(sourceId) {\n            var source = _this.sourceNodes.get(sourceId);\n            return source && (0,_OffsetUtils__WEBPACK_IMPORTED_MODULE_0__.getNodeClientOffset)(source) || null;\n        });\n        _defineProperty(this, \"endDragNativeItem\", function() {\n            if (!_this.isDraggingNativeItem()) {\n                return;\n            }\n            _this.actions.endDrag();\n            if (_this.currentNativeHandle) {\n                _this.registry.removeSource(_this.currentNativeHandle);\n            }\n            _this.currentNativeHandle = null;\n            _this.currentNativeSource = null;\n        });\n        _defineProperty(this, \"isNodeInDocument\", function(node) {\n            // Check the node either in the main document or in the current context\n            return Boolean(node && _this.document && _this.document.body && _this.document.body.contains(node));\n        });\n        _defineProperty(this, \"endDragIfSourceWasRemovedFromDOM\", function() {\n            var node = _this.currentDragSourceNode;\n            if (node == null || _this.isNodeInDocument(node)) {\n                return;\n            }\n            if (_this.clearCurrentDragSourceNode() && _this.monitor.isDragging()) {\n                _this.actions.endDrag();\n            }\n        });\n        _defineProperty(this, \"handleTopDragStartCapture\", function() {\n            _this.clearCurrentDragSourceNode();\n            _this.dragStartSourceIds = [];\n        });\n        _defineProperty(this, \"handleTopDragStart\", function(e) {\n            if (e.defaultPrevented) {\n                return;\n            }\n            var dragStartSourceIds = _this.dragStartSourceIds;\n            _this.dragStartSourceIds = null;\n            var clientOffset = (0,_OffsetUtils__WEBPACK_IMPORTED_MODULE_0__.getEventClientOffset)(e); // Avoid crashing if we missed a drop event or our previous drag died\n            if (_this.monitor.isDragging()) {\n                _this.actions.endDrag();\n            } // Don't publish the source just yet (see why below)\n            _this.actions.beginDrag(dragStartSourceIds || [], {\n                publishSource: false,\n                getSourceClientOffset: _this.getSourceClientOffset,\n                clientOffset: clientOffset\n            });\n            var dataTransfer = e.dataTransfer;\n            var nativeType = (0,_NativeDragSources__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(dataTransfer);\n            if (_this.monitor.isDragging()) {\n                if (dataTransfer && typeof dataTransfer.setDragImage === \"function\") {\n                    // Use custom drag image if user specifies it.\n                    // If child drag source refuses drag but parent agrees,\n                    // use parent's node as drag image. Neither works in IE though.\n                    var sourceId = _this.monitor.getSourceId();\n                    var sourceNode = _this.sourceNodes.get(sourceId);\n                    var dragPreview = _this.sourcePreviewNodes.get(sourceId) || sourceNode;\n                    if (dragPreview) {\n                        var _this$getCurrentSourc = _this.getCurrentSourcePreviewNodeOptions(), anchorX = _this$getCurrentSourc.anchorX, anchorY = _this$getCurrentSourc.anchorY, offsetX = _this$getCurrentSourc.offsetX, offsetY = _this$getCurrentSourc.offsetY;\n                        var anchorPoint = {\n                            anchorX: anchorX,\n                            anchorY: anchorY\n                        };\n                        var offsetPoint = {\n                            offsetX: offsetX,\n                            offsetY: offsetY\n                        };\n                        var dragPreviewOffset = (0,_OffsetUtils__WEBPACK_IMPORTED_MODULE_0__.getDragPreviewOffset)(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n                        dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n                    }\n                }\n                try {\n                    // Firefox won't drag without setting data\n                    dataTransfer === null || dataTransfer === void 0 ? void 0 : dataTransfer.setData(\"application/json\", {});\n                } catch (err) {} // Store drag source node so we can check whether\n                // it is removed from DOM and trigger endDrag manually.\n                _this.setCurrentDragSourceNode(e.target); // Now we are ready to publish the drag source.. or are we not?\n                var _this$getCurrentSourc2 = _this.getCurrentSourcePreviewNodeOptions(), captureDraggingState = _this$getCurrentSourc2.captureDraggingState;\n                if (!captureDraggingState) {\n                    // Usually we want to publish it in the next tick so that browser\n                    // is able to screenshot the current (not yet dragging) state.\n                    //\n                    // It also neatly avoids a situation where render() returns null\n                    // in the same tick for the source element, and browser freaks out.\n                    setTimeout(function() {\n                        return _this.actions.publishDragSource();\n                    }, 0);\n                } else {\n                    // In some cases the user may want to override this behavior, e.g.\n                    // to work around IE not supporting custom drag previews.\n                    //\n                    // When using a custom drag layer, the only way to prevent\n                    // the default drag preview from drawing in IE is to screenshot\n                    // the dragging state in which the node itself has zero opacity\n                    // and height. In this case, though, returning null from render()\n                    // will abruptly end the dragging, which is not obvious.\n                    //\n                    // This is the reason such behavior is strictly opt-in.\n                    _this.actions.publishDragSource();\n                }\n            } else if (nativeType) {\n                // A native item (such as URL) dragged from inside the document\n                _this.beginDragNativeItem(nativeType);\n            } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute(\"draggable\"))) {\n                // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n                // Just let it drag. It's a native type (URL or text) and will be picked up in\n                // dragenter handler.\n                return;\n            } else {\n                // If by this time no drag source reacted, tell browser not to drag.\n                e.preventDefault();\n            }\n        });\n        _defineProperty(this, \"handleTopDragEndCapture\", function() {\n            if (_this.clearCurrentDragSourceNode() && _this.monitor.isDragging()) {\n                // Firefox can dispatch this event in an infinite loop\n                // if dragend handler does something like showing an alert.\n                // Only proceed if we have not handled it already.\n                _this.actions.endDrag();\n            }\n        });\n        _defineProperty(this, \"handleTopDragEnterCapture\", function(e) {\n            _this.dragEnterTargetIds = [];\n            var isFirstEnter = _this.enterLeaveCounter.enter(e.target);\n            if (!isFirstEnter || _this.monitor.isDragging()) {\n                return;\n            }\n            var dataTransfer = e.dataTransfer;\n            var nativeType = (0,_NativeDragSources__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(dataTransfer);\n            if (nativeType) {\n                // A native item (such as file or URL) dragged from outside the document\n                _this.beginDragNativeItem(nativeType, dataTransfer);\n            }\n        });\n        _defineProperty(this, \"handleTopDragEnter\", function(e) {\n            var dragEnterTargetIds = _this.dragEnterTargetIds;\n            _this.dragEnterTargetIds = [];\n            if (!_this.monitor.isDragging()) {\n                // This is probably a native item type we don't understand.\n                return;\n            }\n            _this.altKeyPressed = e.altKey; // If the target changes position as the result of `dragenter`, `dragover` might still\n            // get dispatched despite target being no longer there. The easy solution is to check\n            // whether there actually is a target before firing `hover`.\n            if (dragEnterTargetIds.length > 0) {\n                _this.actions.hover(dragEnterTargetIds, {\n                    clientOffset: (0,_OffsetUtils__WEBPACK_IMPORTED_MODULE_0__.getEventClientOffset)(e)\n                });\n            }\n            var canDrop = dragEnterTargetIds.some(function(targetId) {\n                return _this.monitor.canDropOnTarget(targetId);\n            });\n            if (canDrop) {\n                // IE requires this to fire dragover events\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n                }\n            }\n        });\n        _defineProperty(this, \"handleTopDragOverCapture\", function() {\n            _this.dragOverTargetIds = [];\n        });\n        _defineProperty(this, \"handleTopDragOver\", function(e) {\n            var dragOverTargetIds = _this.dragOverTargetIds;\n            _this.dragOverTargetIds = [];\n            if (!_this.monitor.isDragging()) {\n                // This is probably a native item type we don't understand.\n                // Prevent default \"drop and blow away the whole document\" action.\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = \"none\";\n                }\n                return;\n            }\n            _this.altKeyPressed = e.altKey;\n            _this.lastClientOffset = (0,_OffsetUtils__WEBPACK_IMPORTED_MODULE_0__.getEventClientOffset)(e);\n            if (_this.hoverRafId === null && typeof requestAnimationFrame !== \"undefined\") {\n                _this.hoverRafId = requestAnimationFrame(function() {\n                    if (_this.monitor.isDragging()) {\n                        _this.actions.hover(dragOverTargetIds || [], {\n                            clientOffset: _this.lastClientOffset\n                        });\n                    }\n                    _this.hoverRafId = null;\n                });\n            }\n            var canDrop = (dragOverTargetIds || []).some(function(targetId) {\n                return _this.monitor.canDropOnTarget(targetId);\n            });\n            if (canDrop) {\n                // Show user-specified drop effect.\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n                }\n            } else if (_this.isDraggingNativeItem()) {\n                // Don't show a nice cursor but still prevent default\n                // \"drop and blow away the whole document\" action.\n                e.preventDefault();\n            } else {\n                e.preventDefault();\n                if (e.dataTransfer) {\n                    e.dataTransfer.dropEffect = \"none\";\n                }\n            }\n        });\n        _defineProperty(this, \"handleTopDragLeaveCapture\", function(e) {\n            if (_this.isDraggingNativeItem()) {\n                e.preventDefault();\n            }\n            var isLastLeave = _this.enterLeaveCounter.leave(e.target);\n            if (!isLastLeave) {\n                return;\n            }\n            if (_this.isDraggingNativeItem()) {\n                setTimeout(function() {\n                    return _this.endDragNativeItem();\n                }, 0);\n            }\n        });\n        _defineProperty(this, \"handleTopDropCapture\", function(e) {\n            _this.dropTargetIds = [];\n            if (_this.isDraggingNativeItem()) {\n                var _this$currentNativeSo;\n                e.preventDefault();\n                (_this$currentNativeSo = _this.currentNativeSource) === null || _this$currentNativeSo === void 0 ? void 0 : _this$currentNativeSo.loadDataTransfer(e.dataTransfer);\n            } else if ((0,_NativeDragSources__WEBPACK_IMPORTED_MODULE_1__.matchNativeItemType)(e.dataTransfer)) {\n                // Dragging some elements, like <a> and <img> may still behave like a native drag event,\n                // even if the current drag event matches a user-defined type.\n                // Stop the default behavior when we're not expecting a native item to be dropped.\n                e.preventDefault();\n            }\n            _this.enterLeaveCounter.reset();\n        });\n        _defineProperty(this, \"handleTopDrop\", function(e) {\n            var dropTargetIds = _this.dropTargetIds;\n            _this.dropTargetIds = [];\n            _this.actions.hover(dropTargetIds, {\n                clientOffset: (0,_OffsetUtils__WEBPACK_IMPORTED_MODULE_0__.getEventClientOffset)(e)\n            });\n            _this.actions.drop({\n                dropEffect: _this.getCurrentDropEffect()\n            });\n            if (_this.isDraggingNativeItem()) {\n                _this.endDragNativeItem();\n            } else if (_this.monitor.isDragging()) {\n                _this.actions.endDrag();\n            }\n        });\n        _defineProperty(this, \"handleSelectStart\", function(e) {\n            var target = e.target; // Only IE requires us to explicitly say\n            // we want drag drop operation to start\n            if (typeof target.dragDrop !== \"function\") {\n                return;\n            } // Inputs and textareas should be selectable\n            if (target.tagName === \"INPUT\" || target.tagName === \"SELECT\" || target.tagName === \"TEXTAREA\" || target.isContentEditable) {\n                return;\n            } // For other targets, ask IE\n            // to enable drag and drop\n            e.preventDefault();\n            target.dragDrop();\n        });\n        this.options = new _OptionsReader__WEBPACK_IMPORTED_MODULE_2__.OptionsReader(globalContext, options);\n        this.actions = manager.getActions();\n        this.monitor = manager.getMonitor();\n        this.registry = manager.getRegistry();\n        this.enterLeaveCounter = new _EnterLeaveCounter__WEBPACK_IMPORTED_MODULE_3__.EnterLeaveCounter(this.isNodeInDocument);\n    }\n    /**\n   * Generate profiling statistics for the HTML5Backend.\n   */ _createClass(HTML5BackendImpl, [\n        {\n            key: \"profile\",\n            value: function profile() {\n                var _this$dragStartSource, _this$dragOverTargetI;\n                return {\n                    sourcePreviewNodes: this.sourcePreviewNodes.size,\n                    sourcePreviewNodeOptions: this.sourcePreviewNodeOptions.size,\n                    sourceNodeOptions: this.sourceNodeOptions.size,\n                    sourceNodes: this.sourceNodes.size,\n                    dragStartSourceIds: ((_this$dragStartSource = this.dragStartSourceIds) === null || _this$dragStartSource === void 0 ? void 0 : _this$dragStartSource.length) || 0,\n                    dropTargetIds: this.dropTargetIds.length,\n                    dragEnterTargetIds: this.dragEnterTargetIds.length,\n                    dragOverTargetIds: ((_this$dragOverTargetI = this.dragOverTargetIds) === null || _this$dragOverTargetI === void 0 ? void 0 : _this$dragOverTargetI.length) || 0\n                };\n            } // public for test\n        },\n        {\n            key: \"window\",\n            get: function get() {\n                return this.options.window;\n            }\n        },\n        {\n            key: \"document\",\n            get: function get() {\n                return this.options.document;\n            }\n        },\n        {\n            key: \"rootElement\",\n            get: function get() {\n                return this.options.rootElement;\n            }\n        },\n        {\n            key: \"setup\",\n            value: function setup() {\n                var root = this.rootElement;\n                if (root === undefined) {\n                    return;\n                }\n                if (root.__isReactDndBackendSetUp) {\n                    throw new Error(\"Cannot have two HTML5 backends at the same time.\");\n                }\n                root.__isReactDndBackendSetUp = true;\n                this.addEventListeners(root);\n            }\n        },\n        {\n            key: \"teardown\",\n            value: function teardown() {\n                var root = this.rootElement;\n                if (root === undefined) {\n                    return;\n                }\n                root.__isReactDndBackendSetUp = false;\n                this.removeEventListeners(this.rootElement);\n                this.clearCurrentDragSourceNode();\n                if (this.asyncEndDragFrameId) {\n                    var _this$window;\n                    (_this$window = this.window) === null || _this$window === void 0 ? void 0 : _this$window.cancelAnimationFrame(this.asyncEndDragFrameId);\n                }\n            }\n        },\n        {\n            key: \"connectDragPreview\",\n            value: function connectDragPreview(sourceId, node, options) {\n                var _this2 = this;\n                this.sourcePreviewNodeOptions.set(sourceId, options);\n                this.sourcePreviewNodes.set(sourceId, node);\n                return function() {\n                    _this2.sourcePreviewNodes.delete(sourceId);\n                    _this2.sourcePreviewNodeOptions.delete(sourceId);\n                };\n            }\n        },\n        {\n            key: \"connectDragSource\",\n            value: function connectDragSource(sourceId, node, options) {\n                var _this3 = this;\n                this.sourceNodes.set(sourceId, node);\n                this.sourceNodeOptions.set(sourceId, options);\n                var handleDragStart = function handleDragStart(e) {\n                    return _this3.handleDragStart(e, sourceId);\n                };\n                var handleSelectStart = function handleSelectStart(e) {\n                    return _this3.handleSelectStart(e);\n                };\n                node.setAttribute(\"draggable\", \"true\");\n                node.addEventListener(\"dragstart\", handleDragStart);\n                node.addEventListener(\"selectstart\", handleSelectStart);\n                return function() {\n                    _this3.sourceNodes.delete(sourceId);\n                    _this3.sourceNodeOptions.delete(sourceId);\n                    node.removeEventListener(\"dragstart\", handleDragStart);\n                    node.removeEventListener(\"selectstart\", handleSelectStart);\n                    node.setAttribute(\"draggable\", \"false\");\n                };\n            }\n        },\n        {\n            key: \"connectDropTarget\",\n            value: function connectDropTarget(targetId, node) {\n                var _this4 = this;\n                var handleDragEnter = function handleDragEnter(e) {\n                    return _this4.handleDragEnter(e, targetId);\n                };\n                var handleDragOver = function handleDragOver(e) {\n                    return _this4.handleDragOver(e, targetId);\n                };\n                var handleDrop = function handleDrop(e) {\n                    return _this4.handleDrop(e, targetId);\n                };\n                node.addEventListener(\"dragenter\", handleDragEnter);\n                node.addEventListener(\"dragover\", handleDragOver);\n                node.addEventListener(\"drop\", handleDrop);\n                return function() {\n                    node.removeEventListener(\"dragenter\", handleDragEnter);\n                    node.removeEventListener(\"dragover\", handleDragOver);\n                    node.removeEventListener(\"drop\", handleDrop);\n                };\n            }\n        },\n        {\n            key: \"addEventListeners\",\n            value: function addEventListeners(target) {\n                // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n                if (!target.addEventListener) {\n                    return;\n                }\n                target.addEventListener(\"dragstart\", this.handleTopDragStart);\n                target.addEventListener(\"dragstart\", this.handleTopDragStartCapture, true);\n                target.addEventListener(\"dragend\", this.handleTopDragEndCapture, true);\n                target.addEventListener(\"dragenter\", this.handleTopDragEnter);\n                target.addEventListener(\"dragenter\", this.handleTopDragEnterCapture, true);\n                target.addEventListener(\"dragleave\", this.handleTopDragLeaveCapture, true);\n                target.addEventListener(\"dragover\", this.handleTopDragOver);\n                target.addEventListener(\"dragover\", this.handleTopDragOverCapture, true);\n                target.addEventListener(\"drop\", this.handleTopDrop);\n                target.addEventListener(\"drop\", this.handleTopDropCapture, true);\n            }\n        },\n        {\n            key: \"removeEventListeners\",\n            value: function removeEventListeners(target) {\n                // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n                if (!target.removeEventListener) {\n                    return;\n                }\n                target.removeEventListener(\"dragstart\", this.handleTopDragStart);\n                target.removeEventListener(\"dragstart\", this.handleTopDragStartCapture, true);\n                target.removeEventListener(\"dragend\", this.handleTopDragEndCapture, true);\n                target.removeEventListener(\"dragenter\", this.handleTopDragEnter);\n                target.removeEventListener(\"dragenter\", this.handleTopDragEnterCapture, true);\n                target.removeEventListener(\"dragleave\", this.handleTopDragLeaveCapture, true);\n                target.removeEventListener(\"dragover\", this.handleTopDragOver);\n                target.removeEventListener(\"dragover\", this.handleTopDragOverCapture, true);\n                target.removeEventListener(\"drop\", this.handleTopDrop);\n                target.removeEventListener(\"drop\", this.handleTopDropCapture, true);\n            }\n        },\n        {\n            key: \"getCurrentSourceNodeOptions\",\n            value: function getCurrentSourceNodeOptions() {\n                var sourceId = this.monitor.getSourceId();\n                var sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n                return _objectSpread({\n                    dropEffect: this.altKeyPressed ? \"copy\" : \"move\"\n                }, sourceNodeOptions || {});\n            }\n        },\n        {\n            key: \"getCurrentDropEffect\",\n            value: function getCurrentDropEffect() {\n                if (this.isDraggingNativeItem()) {\n                    // It makes more sense to default to 'copy' for native resources\n                    return \"copy\";\n                }\n                return this.getCurrentSourceNodeOptions().dropEffect;\n            }\n        },\n        {\n            key: \"getCurrentSourcePreviewNodeOptions\",\n            value: function getCurrentSourcePreviewNodeOptions() {\n                var sourceId = this.monitor.getSourceId();\n                var sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n                return _objectSpread({\n                    anchorX: 0.5,\n                    anchorY: 0.5,\n                    captureDraggingState: false\n                }, sourcePreviewNodeOptions || {});\n            }\n        },\n        {\n            key: \"isDraggingNativeItem\",\n            value: function isDraggingNativeItem() {\n                var itemType = this.monitor.getItemType();\n                return Object.keys(_NativeTypes__WEBPACK_IMPORTED_MODULE_4__).some(function(key) {\n                    return _NativeTypes__WEBPACK_IMPORTED_MODULE_4__[key] === itemType;\n                });\n            }\n        },\n        {\n            key: \"beginDragNativeItem\",\n            value: function beginDragNativeItem(type, dataTransfer) {\n                this.clearCurrentDragSourceNode();\n                this.currentNativeSource = (0,_NativeDragSources__WEBPACK_IMPORTED_MODULE_1__.createNativeDragSource)(type, dataTransfer);\n                this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n                this.actions.beginDrag([\n                    this.currentNativeHandle\n                ]);\n            }\n        },\n        {\n            key: \"setCurrentDragSourceNode\",\n            value: function setCurrentDragSourceNode(node) {\n                var _this5 = this;\n                this.clearCurrentDragSourceNode();\n                this.currentDragSourceNode = node; // A timeout of > 0 is necessary to resolve Firefox issue referenced\n                // See:\n                //   * https://github.com/react-dnd/react-dnd/pull/928\n                //   * https://github.com/react-dnd/react-dnd/issues/869\n                var MOUSE_MOVE_TIMEOUT = 1000; // Receiving a mouse event in the middle of a dragging operation\n                // means it has ended and the drag source node disappeared from DOM,\n                // so the browser didn't dispatch the dragend event.\n                //\n                // We need to wait before we start listening for mousemove events.\n                // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n                // immediately in some browsers.\n                //\n                // See:\n                //   * https://github.com/react-dnd/react-dnd/pull/928\n                //   * https://github.com/react-dnd/react-dnd/issues/869\n                //\n                this.mouseMoveTimeoutTimer = setTimeout(function() {\n                    var _this5$rootElement;\n                    return (_this5$rootElement = _this5.rootElement) === null || _this5$rootElement === void 0 ? void 0 : _this5$rootElement.addEventListener(\"mousemove\", _this5.endDragIfSourceWasRemovedFromDOM, true);\n                }, MOUSE_MOVE_TIMEOUT);\n            }\n        },\n        {\n            key: \"clearCurrentDragSourceNode\",\n            value: function clearCurrentDragSourceNode() {\n                if (this.currentDragSourceNode) {\n                    this.currentDragSourceNode = null;\n                    if (this.rootElement) {\n                        var _this$window2;\n                        (_this$window2 = this.window) === null || _this$window2 === void 0 ? void 0 : _this$window2.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n                        this.rootElement.removeEventListener(\"mousemove\", this.endDragIfSourceWasRemovedFromDOM, true);\n                    }\n                    this.mouseMoveTimeoutTimer = null;\n                    return true;\n                }\n                return false;\n            }\n        },\n        {\n            key: \"handleDragStart\",\n            value: function handleDragStart(e, sourceId) {\n                if (e.defaultPrevented) {\n                    return;\n                }\n                if (!this.dragStartSourceIds) {\n                    this.dragStartSourceIds = [];\n                }\n                this.dragStartSourceIds.unshift(sourceId);\n            }\n        },\n        {\n            key: \"handleDragEnter\",\n            value: function handleDragEnter(e, targetId) {\n                this.dragEnterTargetIds.unshift(targetId);\n            }\n        },\n        {\n            key: \"handleDragOver\",\n            value: function handleDragOver(e, targetId) {\n                if (this.dragOverTargetIds === null) {\n                    this.dragOverTargetIds = [];\n                }\n                this.dragOverTargetIds.unshift(targetId);\n            }\n        },\n        {\n            key: \"handleDrop\",\n            value: function handleDrop(e, targetId) {\n                this.dropTargetIds.unshift(targetId);\n            }\n        }\n    ]);\n    return HTML5BackendImpl;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/HTML5BackendImpl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/MonotonicInterpolant.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/MonotonicInterpolant.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonotonicInterpolant: () => (/* binding */ MonotonicInterpolant)\n/* harmony export */ });\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nvar MonotonicInterpolant = /*#__PURE__*/ function() {\n    function MonotonicInterpolant(xs, ys) {\n        _classCallCheck(this, MonotonicInterpolant);\n        _defineProperty(this, \"xs\", void 0);\n        _defineProperty(this, \"ys\", void 0);\n        _defineProperty(this, \"c1s\", void 0);\n        _defineProperty(this, \"c2s\", void 0);\n        _defineProperty(this, \"c3s\", void 0);\n        var length = xs.length; // Rearrange xs and ys so that xs is sorted\n        var indexes = [];\n        for(var i = 0; i < length; i++){\n            indexes.push(i);\n        }\n        indexes.sort(function(a, b) {\n            return xs[a] < xs[b] ? -1 : 1;\n        }); // Get consecutive differences and slopes\n        var dys = [];\n        var dxs = [];\n        var ms = [];\n        var dx;\n        var dy;\n        for(var _i = 0; _i < length - 1; _i++){\n            dx = xs[_i + 1] - xs[_i];\n            dy = ys[_i + 1] - ys[_i];\n            dxs.push(dx);\n            dys.push(dy);\n            ms.push(dy / dx);\n        } // Get degree-1 coefficients\n        var c1s = [\n            ms[0]\n        ];\n        for(var _i2 = 0; _i2 < dxs.length - 1; _i2++){\n            var m2 = ms[_i2];\n            var mNext = ms[_i2 + 1];\n            if (m2 * mNext <= 0) {\n                c1s.push(0);\n            } else {\n                dx = dxs[_i2];\n                var dxNext = dxs[_i2 + 1];\n                var common = dx + dxNext;\n                c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n            }\n        }\n        c1s.push(ms[ms.length - 1]); // Get degree-2 and degree-3 coefficients\n        var c2s = [];\n        var c3s = [];\n        var m;\n        for(var _i3 = 0; _i3 < c1s.length - 1; _i3++){\n            m = ms[_i3];\n            var c1 = c1s[_i3];\n            var invDx = 1 / dxs[_i3];\n            var _common = c1 + c1s[_i3 + 1] - m - m;\n            c2s.push((m - c1 - _common) * invDx);\n            c3s.push(_common * invDx * invDx);\n        }\n        this.xs = xs;\n        this.ys = ys;\n        this.c1s = c1s;\n        this.c2s = c2s;\n        this.c3s = c3s;\n    }\n    _createClass(MonotonicInterpolant, [\n        {\n            key: \"interpolate\",\n            value: function interpolate(x) {\n                var xs = this.xs, ys = this.ys, c1s = this.c1s, c2s = this.c2s, c3s = this.c3s; // The rightmost point in the dataset should give an exact result\n                var i = xs.length - 1;\n                if (x === xs[i]) {\n                    return ys[i];\n                } // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n                var low = 0;\n                var high = c3s.length - 1;\n                var mid;\n                while(low <= high){\n                    mid = Math.floor(0.5 * (low + high));\n                    var xHere = xs[mid];\n                    if (xHere < x) {\n                        low = mid + 1;\n                    } else if (xHere > x) {\n                        high = mid - 1;\n                    } else {\n                        return ys[mid];\n                    }\n                }\n                i = Math.max(0, high); // Interpolate\n                var diff = x - xs[i];\n                var diffSq = diff * diff;\n                return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n            }\n        }\n    ]);\n    return MonotonicInterpolant;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9lc20vTW9ub3RvbmljSW50ZXJwb2xhbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBLGdCQUFnQkMsUUFBUSxFQUFFQyxXQUFXO0lBQUksSUFBSSxDQUFFRCxDQUFBQSxvQkFBb0JDLFdBQVUsR0FBSTtRQUFFLE1BQU0sSUFBSUMsVUFBVTtJQUFzQztBQUFFO0FBRXhKLFNBQVNDLGtCQUFrQkMsTUFBTSxFQUFFQyxLQUFLO0lBQUksSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlELE1BQU1FLE1BQU0sRUFBRUQsSUFBSztRQUFFLElBQUlFLGFBQWFILEtBQUssQ0FBQ0MsRUFBRTtRQUFFRSxXQUFXQyxVQUFVLEdBQUdELFdBQVdDLFVBQVUsSUFBSTtRQUFPRCxXQUFXRSxZQUFZLEdBQUc7UUFBTSxJQUFJLFdBQVdGLFlBQVlBLFdBQVdHLFFBQVEsR0FBRztRQUFNQyxPQUFPQyxjQUFjLENBQUNULFFBQVFJLFdBQVdNLEdBQUcsRUFBRU47SUFBYTtBQUFFO0FBRTVULFNBQVNPLGFBQWFkLFdBQVcsRUFBRWUsVUFBVSxFQUFFQyxXQUFXO0lBQUksSUFBSUQsWUFBWWIsa0JBQWtCRixZQUFZaUIsU0FBUyxFQUFFRjtJQUFhLElBQUlDLGFBQWFkLGtCQUFrQkYsYUFBYWdCO0lBQWMsT0FBT2hCO0FBQWE7QUFFdE4sU0FBU2tCLGdCQUFnQkMsR0FBRyxFQUFFTixHQUFHLEVBQUVPLEtBQUs7SUFBSSxJQUFJUCxPQUFPTSxLQUFLO1FBQUVSLE9BQU9DLGNBQWMsQ0FBQ08sS0FBS04sS0FBSztZQUFFTyxPQUFPQTtZQUFPWixZQUFZO1lBQU1DLGNBQWM7WUFBTUMsVUFBVTtRQUFLO0lBQUksT0FBTztRQUFFUyxHQUFHLENBQUNOLElBQUksR0FBR087SUFBTztJQUFFLE9BQU9EO0FBQUs7QUFFek0sSUFBSUUsdUJBQXVCLFdBQVcsR0FBRTtJQUM3QyxTQUFTQSxxQkFBcUJDLEVBQUUsRUFBRUMsRUFBRTtRQUNsQ3pCLGdCQUFnQixJQUFJLEVBQUV1QjtRQUV0QkgsZ0JBQWdCLElBQUksRUFBRSxNQUFNLEtBQUs7UUFFakNBLGdCQUFnQixJQUFJLEVBQUUsTUFBTSxLQUFLO1FBRWpDQSxnQkFBZ0IsSUFBSSxFQUFFLE9BQU8sS0FBSztRQUVsQ0EsZ0JBQWdCLElBQUksRUFBRSxPQUFPLEtBQUs7UUFFbENBLGdCQUFnQixJQUFJLEVBQUUsT0FBTyxLQUFLO1FBRWxDLElBQUlaLFNBQVNnQixHQUFHaEIsTUFBTSxFQUFFLDJDQUEyQztRQUVuRSxJQUFJa0IsVUFBVSxFQUFFO1FBRWhCLElBQUssSUFBSW5CLElBQUksR0FBR0EsSUFBSUMsUUFBUUQsSUFBSztZQUMvQm1CLFFBQVFDLElBQUksQ0FBQ3BCO1FBQ2Y7UUFFQW1CLFFBQVFFLElBQUksQ0FBQyxTQUFVQyxDQUFDLEVBQUVDLENBQUM7WUFDekIsT0FBT04sRUFBRSxDQUFDSyxFQUFFLEdBQUdMLEVBQUUsQ0FBQ00sRUFBRSxHQUFHLENBQUMsSUFBSTtRQUM5QixJQUFJLHlDQUF5QztRQUU3QyxJQUFJQyxNQUFNLEVBQUU7UUFDWixJQUFJQyxNQUFNLEVBQUU7UUFDWixJQUFJQyxLQUFLLEVBQUU7UUFDWCxJQUFJQztRQUNKLElBQUlDO1FBRUosSUFBSyxJQUFJQyxLQUFLLEdBQUdBLEtBQUs1QixTQUFTLEdBQUc0QixLQUFNO1lBQ3RDRixLQUFLVixFQUFFLENBQUNZLEtBQUssRUFBRSxHQUFHWixFQUFFLENBQUNZLEdBQUc7WUFDeEJELEtBQUtWLEVBQUUsQ0FBQ1csS0FBSyxFQUFFLEdBQUdYLEVBQUUsQ0FBQ1csR0FBRztZQUN4QkosSUFBSUwsSUFBSSxDQUFDTztZQUNUSCxJQUFJSixJQUFJLENBQUNRO1lBQ1RGLEdBQUdOLElBQUksQ0FBQ1EsS0FBS0Q7UUFDZixFQUFFLDRCQUE0QjtRQUc5QixJQUFJRyxNQUFNO1lBQUNKLEVBQUUsQ0FBQyxFQUFFO1NBQUM7UUFFakIsSUFBSyxJQUFJSyxNQUFNLEdBQUdBLE1BQU1OLElBQUl4QixNQUFNLEdBQUcsR0FBRzhCLE1BQU87WUFDN0MsSUFBSUMsS0FBS04sRUFBRSxDQUFDSyxJQUFJO1lBQ2hCLElBQUlFLFFBQVFQLEVBQUUsQ0FBQ0ssTUFBTSxFQUFFO1lBRXZCLElBQUlDLEtBQUtDLFNBQVMsR0FBRztnQkFDbkJILElBQUlWLElBQUksQ0FBQztZQUNYLE9BQU87Z0JBQ0xPLEtBQUtGLEdBQUcsQ0FBQ00sSUFBSTtnQkFDYixJQUFJRyxTQUFTVCxHQUFHLENBQUNNLE1BQU0sRUFBRTtnQkFDekIsSUFBSUksU0FBU1IsS0FBS087Z0JBQ2xCSixJQUFJVixJQUFJLENBQUMsSUFBSWUsU0FBVSxFQUFDQSxTQUFTRCxNQUFLLElBQUtGLEtBQUssQ0FBQ0csU0FBU1IsRUFBQyxJQUFLTSxLQUFJO1lBQ3RFO1FBQ0Y7UUFFQUgsSUFBSVYsSUFBSSxDQUFDTSxFQUFFLENBQUNBLEdBQUd6QixNQUFNLEdBQUcsRUFBRSxHQUFHLHlDQUF5QztRQUV0RSxJQUFJbUMsTUFBTSxFQUFFO1FBQ1osSUFBSUMsTUFBTSxFQUFFO1FBQ1osSUFBSUM7UUFFSixJQUFLLElBQUlDLE1BQU0sR0FBR0EsTUFBTVQsSUFBSTdCLE1BQU0sR0FBRyxHQUFHc0MsTUFBTztZQUM3Q0QsSUFBSVosRUFBRSxDQUFDYSxJQUFJO1lBQ1gsSUFBSUMsS0FBS1YsR0FBRyxDQUFDUyxJQUFJO1lBQ2pCLElBQUlFLFFBQVEsSUFBSWhCLEdBQUcsQ0FBQ2MsSUFBSTtZQUV4QixJQUFJRyxVQUFVRixLQUFLVixHQUFHLENBQUNTLE1BQU0sRUFBRSxHQUFHRCxJQUFJQTtZQUV0Q0YsSUFBSWhCLElBQUksQ0FBQyxDQUFDa0IsSUFBSUUsS0FBS0UsT0FBTSxJQUFLRDtZQUM5QkosSUFBSWpCLElBQUksQ0FBQ3NCLFVBQVVELFFBQVFBO1FBQzdCO1FBRUEsSUFBSSxDQUFDeEIsRUFBRSxHQUFHQTtRQUNWLElBQUksQ0FBQ0MsRUFBRSxHQUFHQTtRQUNWLElBQUksQ0FBQ1ksR0FBRyxHQUFHQTtRQUNYLElBQUksQ0FBQ00sR0FBRyxHQUFHQTtRQUNYLElBQUksQ0FBQ0MsR0FBRyxHQUFHQTtJQUNiO0lBRUE1QixhQUFhTyxzQkFBc0I7UUFBQztZQUNsQ1IsS0FBSztZQUNMTyxPQUFPLFNBQVM0QixZQUFZQyxDQUFDO2dCQUMzQixJQUFJM0IsS0FBSyxJQUFJLENBQUNBLEVBQUUsRUFDWkMsS0FBSyxJQUFJLENBQUNBLEVBQUUsRUFDWlksTUFBTSxJQUFJLENBQUNBLEdBQUcsRUFDZE0sTUFBTSxJQUFJLENBQUNBLEdBQUcsRUFDZEMsTUFBTSxJQUFJLENBQUNBLEdBQUcsRUFBRSxpRUFBaUU7Z0JBRXJGLElBQUlyQyxJQUFJaUIsR0FBR2hCLE1BQU0sR0FBRztnQkFFcEIsSUFBSTJDLE1BQU0zQixFQUFFLENBQUNqQixFQUFFLEVBQUU7b0JBQ2YsT0FBT2tCLEVBQUUsQ0FBQ2xCLEVBQUU7Z0JBQ2QsRUFBRSxnR0FBZ0c7Z0JBR2xHLElBQUk2QyxNQUFNO2dCQUNWLElBQUlDLE9BQU9ULElBQUlwQyxNQUFNLEdBQUc7Z0JBQ3hCLElBQUk4QztnQkFFSixNQUFPRixPQUFPQyxLQUFNO29CQUNsQkMsTUFBTUMsS0FBS0MsS0FBSyxDQUFDLE1BQU9KLENBQUFBLE1BQU1DLElBQUc7b0JBQ2pDLElBQUlJLFFBQVFqQyxFQUFFLENBQUM4QixJQUFJO29CQUVuQixJQUFJRyxRQUFRTixHQUFHO3dCQUNiQyxNQUFNRSxNQUFNO29CQUNkLE9BQU8sSUFBSUcsUUFBUU4sR0FBRzt3QkFDcEJFLE9BQU9DLE1BQU07b0JBQ2YsT0FBTzt3QkFDTCxPQUFPN0IsRUFBRSxDQUFDNkIsSUFBSTtvQkFDaEI7Z0JBQ0Y7Z0JBRUEvQyxJQUFJZ0QsS0FBS0csR0FBRyxDQUFDLEdBQUdMLE9BQU8sY0FBYztnQkFFckMsSUFBSU0sT0FBT1IsSUFBSTNCLEVBQUUsQ0FBQ2pCLEVBQUU7Z0JBQ3BCLElBQUlxRCxTQUFTRCxPQUFPQTtnQkFDcEIsT0FBT2xDLEVBQUUsQ0FBQ2xCLEVBQUUsR0FBRzhCLEdBQUcsQ0FBQzlCLEVBQUUsR0FBR29ELE9BQU9oQixHQUFHLENBQUNwQyxFQUFFLEdBQUdxRCxTQUFTaEIsR0FBRyxDQUFDckMsRUFBRSxHQUFHb0QsT0FBT0M7WUFDbkU7UUFDRjtLQUFFO0lBRUYsT0FBT3JDO0FBQ1QsSUFBSSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kbmQtaHRtbDUtYmFja2VuZC9kaXN0L2VzbS9Nb25vdG9uaWNJbnRlcnBvbGFudC5qcz82NTY5Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF9jbGFzc0NhbGxDaGVjayhpbnN0YW5jZSwgQ29uc3RydWN0b3IpIHsgaWYgKCEoaW5zdGFuY2UgaW5zdGFuY2VvZiBDb25zdHJ1Y3RvcikpIHsgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCBjYWxsIGEgY2xhc3MgYXMgYSBmdW5jdGlvblwiKTsgfSB9XG5cbmZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0aWVzKHRhcmdldCwgcHJvcHMpIHsgZm9yICh2YXIgaSA9IDA7IGkgPCBwcm9wcy5sZW5ndGg7IGkrKykgeyB2YXIgZGVzY3JpcHRvciA9IHByb3BzW2ldOyBkZXNjcmlwdG9yLmVudW1lcmFibGUgPSBkZXNjcmlwdG9yLmVudW1lcmFibGUgfHwgZmFsc2U7IGRlc2NyaXB0b3IuY29uZmlndXJhYmxlID0gdHJ1ZTsgaWYgKFwidmFsdWVcIiBpbiBkZXNjcmlwdG9yKSBkZXNjcmlwdG9yLndyaXRhYmxlID0gdHJ1ZTsgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgZGVzY3JpcHRvci5rZXksIGRlc2NyaXB0b3IpOyB9IH1cblxuZnVuY3Rpb24gX2NyZWF0ZUNsYXNzKENvbnN0cnVjdG9yLCBwcm90b1Byb3BzLCBzdGF0aWNQcm9wcykgeyBpZiAocHJvdG9Qcm9wcykgX2RlZmluZVByb3BlcnRpZXMoQ29uc3RydWN0b3IucHJvdG90eXBlLCBwcm90b1Byb3BzKTsgaWYgKHN0YXRpY1Byb3BzKSBfZGVmaW5lUHJvcGVydGllcyhDb25zdHJ1Y3Rvciwgc3RhdGljUHJvcHMpOyByZXR1cm4gQ29uc3RydWN0b3I7IH1cblxuZnVuY3Rpb24gX2RlZmluZVByb3BlcnR5KG9iaiwga2V5LCB2YWx1ZSkgeyBpZiAoa2V5IGluIG9iaikgeyBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHsgdmFsdWU6IHZhbHVlLCBlbnVtZXJhYmxlOiB0cnVlLCBjb25maWd1cmFibGU6IHRydWUsIHdyaXRhYmxlOiB0cnVlIH0pOyB9IGVsc2UgeyBvYmpba2V5XSA9IHZhbHVlOyB9IHJldHVybiBvYmo7IH1cblxuZXhwb3J0IHZhciBNb25vdG9uaWNJbnRlcnBvbGFudCA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIE1vbm90b25pY0ludGVycG9sYW50KHhzLCB5cykge1xuICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBNb25vdG9uaWNJbnRlcnBvbGFudCk7XG5cbiAgICBfZGVmaW5lUHJvcGVydHkodGhpcywgXCJ4c1wiLCB2b2lkIDApO1xuXG4gICAgX2RlZmluZVByb3BlcnR5KHRoaXMsIFwieXNcIiwgdm9pZCAwKTtcblxuICAgIF9kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImMxc1wiLCB2b2lkIDApO1xuXG4gICAgX2RlZmluZVByb3BlcnR5KHRoaXMsIFwiYzJzXCIsIHZvaWQgMCk7XG5cbiAgICBfZGVmaW5lUHJvcGVydHkodGhpcywgXCJjM3NcIiwgdm9pZCAwKTtcblxuICAgIHZhciBsZW5ndGggPSB4cy5sZW5ndGg7IC8vIFJlYXJyYW5nZSB4cyBhbmQgeXMgc28gdGhhdCB4cyBpcyBzb3J0ZWRcblxuICAgIHZhciBpbmRleGVzID0gW107XG5cbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICBpbmRleGVzLnB1c2goaSk7XG4gICAgfVxuXG4gICAgaW5kZXhlcy5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7XG4gICAgICByZXR1cm4geHNbYV0gPCB4c1tiXSA/IC0xIDogMTtcbiAgICB9KTsgLy8gR2V0IGNvbnNlY3V0aXZlIGRpZmZlcmVuY2VzIGFuZCBzbG9wZXNcblxuICAgIHZhciBkeXMgPSBbXTtcbiAgICB2YXIgZHhzID0gW107XG4gICAgdmFyIG1zID0gW107XG4gICAgdmFyIGR4O1xuICAgIHZhciBkeTtcblxuICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBsZW5ndGggLSAxOyBfaSsrKSB7XG4gICAgICBkeCA9IHhzW19pICsgMV0gLSB4c1tfaV07XG4gICAgICBkeSA9IHlzW19pICsgMV0gLSB5c1tfaV07XG4gICAgICBkeHMucHVzaChkeCk7XG4gICAgICBkeXMucHVzaChkeSk7XG4gICAgICBtcy5wdXNoKGR5IC8gZHgpO1xuICAgIH0gLy8gR2V0IGRlZ3JlZS0xIGNvZWZmaWNpZW50c1xuXG5cbiAgICB2YXIgYzFzID0gW21zWzBdXTtcblxuICAgIGZvciAodmFyIF9pMiA9IDA7IF9pMiA8IGR4cy5sZW5ndGggLSAxOyBfaTIrKykge1xuICAgICAgdmFyIG0yID0gbXNbX2kyXTtcbiAgICAgIHZhciBtTmV4dCA9IG1zW19pMiArIDFdO1xuXG4gICAgICBpZiAobTIgKiBtTmV4dCA8PSAwKSB7XG4gICAgICAgIGMxcy5wdXNoKDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZHggPSBkeHNbX2kyXTtcbiAgICAgICAgdmFyIGR4TmV4dCA9IGR4c1tfaTIgKyAxXTtcbiAgICAgICAgdmFyIGNvbW1vbiA9IGR4ICsgZHhOZXh0O1xuICAgICAgICBjMXMucHVzaCgzICogY29tbW9uIC8gKChjb21tb24gKyBkeE5leHQpIC8gbTIgKyAoY29tbW9uICsgZHgpIC8gbU5leHQpKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjMXMucHVzaChtc1ttcy5sZW5ndGggLSAxXSk7IC8vIEdldCBkZWdyZWUtMiBhbmQgZGVncmVlLTMgY29lZmZpY2llbnRzXG5cbiAgICB2YXIgYzJzID0gW107XG4gICAgdmFyIGMzcyA9IFtdO1xuICAgIHZhciBtO1xuXG4gICAgZm9yICh2YXIgX2kzID0gMDsgX2kzIDwgYzFzLmxlbmd0aCAtIDE7IF9pMysrKSB7XG4gICAgICBtID0gbXNbX2kzXTtcbiAgICAgIHZhciBjMSA9IGMxc1tfaTNdO1xuICAgICAgdmFyIGludkR4ID0gMSAvIGR4c1tfaTNdO1xuXG4gICAgICB2YXIgX2NvbW1vbiA9IGMxICsgYzFzW19pMyArIDFdIC0gbSAtIG07XG5cbiAgICAgIGMycy5wdXNoKChtIC0gYzEgLSBfY29tbW9uKSAqIGludkR4KTtcbiAgICAgIGMzcy5wdXNoKF9jb21tb24gKiBpbnZEeCAqIGludkR4KTtcbiAgICB9XG5cbiAgICB0aGlzLnhzID0geHM7XG4gICAgdGhpcy55cyA9IHlzO1xuICAgIHRoaXMuYzFzID0gYzFzO1xuICAgIHRoaXMuYzJzID0gYzJzO1xuICAgIHRoaXMuYzNzID0gYzNzO1xuICB9XG5cbiAgX2NyZWF0ZUNsYXNzKE1vbm90b25pY0ludGVycG9sYW50LCBbe1xuICAgIGtleTogXCJpbnRlcnBvbGF0ZVwiLFxuICAgIHZhbHVlOiBmdW5jdGlvbiBpbnRlcnBvbGF0ZSh4KSB7XG4gICAgICB2YXIgeHMgPSB0aGlzLnhzLFxuICAgICAgICAgIHlzID0gdGhpcy55cyxcbiAgICAgICAgICBjMXMgPSB0aGlzLmMxcyxcbiAgICAgICAgICBjMnMgPSB0aGlzLmMycyxcbiAgICAgICAgICBjM3MgPSB0aGlzLmMzczsgLy8gVGhlIHJpZ2h0bW9zdCBwb2ludCBpbiB0aGUgZGF0YXNldCBzaG91bGQgZ2l2ZSBhbiBleGFjdCByZXN1bHRcblxuICAgICAgdmFyIGkgPSB4cy5sZW5ndGggLSAxO1xuXG4gICAgICBpZiAoeCA9PT0geHNbaV0pIHtcbiAgICAgICAgcmV0dXJuIHlzW2ldO1xuICAgICAgfSAvLyBTZWFyY2ggZm9yIHRoZSBpbnRlcnZhbCB4IGlzIGluLCByZXR1cm5pbmcgdGhlIGNvcnJlc3BvbmRpbmcgeSBpZiB4IGlzIG9uZSBvZiB0aGUgb3JpZ2luYWwgeHNcblxuXG4gICAgICB2YXIgbG93ID0gMDtcbiAgICAgIHZhciBoaWdoID0gYzNzLmxlbmd0aCAtIDE7XG4gICAgICB2YXIgbWlkO1xuXG4gICAgICB3aGlsZSAobG93IDw9IGhpZ2gpIHtcbiAgICAgICAgbWlkID0gTWF0aC5mbG9vcigwLjUgKiAobG93ICsgaGlnaCkpO1xuICAgICAgICB2YXIgeEhlcmUgPSB4c1ttaWRdO1xuXG4gICAgICAgIGlmICh4SGVyZSA8IHgpIHtcbiAgICAgICAgICBsb3cgPSBtaWQgKyAxO1xuICAgICAgICB9IGVsc2UgaWYgKHhIZXJlID4geCkge1xuICAgICAgICAgIGhpZ2ggPSBtaWQgLSAxO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiB5c1ttaWRdO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGkgPSBNYXRoLm1heCgwLCBoaWdoKTsgLy8gSW50ZXJwb2xhdGVcblxuICAgICAgdmFyIGRpZmYgPSB4IC0geHNbaV07XG4gICAgICB2YXIgZGlmZlNxID0gZGlmZiAqIGRpZmY7XG4gICAgICByZXR1cm4geXNbaV0gKyBjMXNbaV0gKiBkaWZmICsgYzJzW2ldICogZGlmZlNxICsgYzNzW2ldICogZGlmZiAqIGRpZmZTcTtcbiAgICB9XG4gIH1dKTtcblxuICByZXR1cm4gTW9ub3RvbmljSW50ZXJwb2xhbnQ7XG59KCk7Il0sIm5hbWVzIjpbIl9jbGFzc0NhbGxDaGVjayIsImluc3RhbmNlIiwiQ29uc3RydWN0b3IiLCJUeXBlRXJyb3IiLCJfZGVmaW5lUHJvcGVydGllcyIsInRhcmdldCIsInByb3BzIiwiaSIsImxlbmd0aCIsImRlc2NyaXB0b3IiLCJlbnVtZXJhYmxlIiwiY29uZmlndXJhYmxlIiwid3JpdGFibGUiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImtleSIsIl9jcmVhdGVDbGFzcyIsInByb3RvUHJvcHMiLCJzdGF0aWNQcm9wcyIsInByb3RvdHlwZSIsIl9kZWZpbmVQcm9wZXJ0eSIsIm9iaiIsInZhbHVlIiwiTW9ub3RvbmljSW50ZXJwb2xhbnQiLCJ4cyIsInlzIiwiaW5kZXhlcyIsInB1c2giLCJzb3J0IiwiYSIsImIiLCJkeXMiLCJkeHMiLCJtcyIsImR4IiwiZHkiLCJfaSIsImMxcyIsIl9pMiIsIm0yIiwibU5leHQiLCJkeE5leHQiLCJjb21tb24iLCJjMnMiLCJjM3MiLCJtIiwiX2kzIiwiYzEiLCJpbnZEeCIsIl9jb21tb24iLCJpbnRlcnBvbGF0ZSIsIngiLCJsb3ciLCJoaWdoIiwibWlkIiwiTWF0aCIsImZsb29yIiwieEhlcmUiLCJtYXgiLCJkaWZmIiwiZGlmZlNxIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/MonotonicInterpolant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/NativeDragSource.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/NativeDragSource.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeDragSource: () => (/* binding */ NativeDragSource)\n/* harmony export */ });\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nvar NativeDragSource = /*#__PURE__*/ function() {\n    function NativeDragSource(config) {\n        _classCallCheck(this, NativeDragSource);\n        _defineProperty(this, \"item\", void 0);\n        _defineProperty(this, \"config\", void 0);\n        this.config = config;\n        this.item = {};\n        this.initializeExposedProperties();\n    }\n    _createClass(NativeDragSource, [\n        {\n            key: \"initializeExposedProperties\",\n            value: function initializeExposedProperties() {\n                var _this = this;\n                Object.keys(this.config.exposeProperties).forEach(function(property) {\n                    Object.defineProperty(_this.item, property, {\n                        configurable: true,\n                        enumerable: true,\n                        get: function get() {\n                            // eslint-disable-next-line no-console\n                            console.warn(\"Browser doesn't allow reading \\\"\".concat(property, '\" until the drop event.'));\n                            return null;\n                        }\n                    });\n                });\n            }\n        },\n        {\n            key: \"loadDataTransfer\",\n            value: function loadDataTransfer(dataTransfer) {\n                var _this2 = this;\n                if (dataTransfer) {\n                    var newProperties = {};\n                    Object.keys(this.config.exposeProperties).forEach(function(property) {\n                        newProperties[property] = {\n                            value: _this2.config.exposeProperties[property](dataTransfer, _this2.config.matchesTypes),\n                            configurable: true,\n                            enumerable: true\n                        };\n                    });\n                    Object.defineProperties(this.item, newProperties);\n                }\n            }\n        },\n        {\n            key: \"canDrag\",\n            value: function canDrag() {\n                return true;\n            }\n        },\n        {\n            key: \"beginDrag\",\n            value: function beginDrag() {\n                return this.item;\n            }\n        },\n        {\n            key: \"isDragging\",\n            value: function isDragging(monitor, handle) {\n                return handle === monitor.getSourceId();\n            }\n        },\n        {\n            key: \"endDrag\",\n            value: function endDrag() {}\n        }\n    ]);\n    return NativeDragSource;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/NativeDragSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/getDataFromDataTransfer.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/getDataFromDataTransfer.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataFromDataTransfer: () => (/* binding */ getDataFromDataTransfer)\n/* harmony export */ });\nfunction getDataFromDataTransfer(dataTransfer, typesToTry, defaultValue) {\n    var result = typesToTry.reduce(function(resultSoFar, typeToTry) {\n        return resultSoFar || dataTransfer.getData(typeToTry);\n    }, \"\");\n    return result != null ? result : defaultValue;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9lc20vTmF0aXZlRHJhZ1NvdXJjZXMvZ2V0RGF0YUZyb21EYXRhVHJhbnNmZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLHdCQUF3QkMsWUFBWSxFQUFFQyxVQUFVLEVBQUVDLFlBQVk7SUFDNUUsSUFBSUMsU0FBU0YsV0FBV0csTUFBTSxDQUFDLFNBQVVDLFdBQVcsRUFBRUMsU0FBUztRQUM3RCxPQUFPRCxlQUFlTCxhQUFhTyxPQUFPLENBQUNEO0lBQzdDLEdBQUc7SUFDSCxPQUFPSCxVQUFVLE9BQU9BLFNBQVNEO0FBQ25DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvZXNtL05hdGl2ZURyYWdTb3VyY2VzL2dldERhdGFGcm9tRGF0YVRyYW5zZmVyLmpzP2YwM2IiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGdldERhdGFGcm9tRGF0YVRyYW5zZmVyKGRhdGFUcmFuc2ZlciwgdHlwZXNUb1RyeSwgZGVmYXVsdFZhbHVlKSB7XG4gIHZhciByZXN1bHQgPSB0eXBlc1RvVHJ5LnJlZHVjZShmdW5jdGlvbiAocmVzdWx0U29GYXIsIHR5cGVUb1RyeSkge1xuICAgIHJldHVybiByZXN1bHRTb0ZhciB8fCBkYXRhVHJhbnNmZXIuZ2V0RGF0YSh0eXBlVG9UcnkpO1xuICB9LCAnJyk7XG4gIHJldHVybiByZXN1bHQgIT0gbnVsbCA/IHJlc3VsdCA6IGRlZmF1bHRWYWx1ZTtcbn0iXSwibmFtZXMiOlsiZ2V0RGF0YUZyb21EYXRhVHJhbnNmZXIiLCJkYXRhVHJhbnNmZXIiLCJ0eXBlc1RvVHJ5IiwiZGVmYXVsdFZhbHVlIiwicmVzdWx0IiwicmVkdWNlIiwicmVzdWx0U29GYXIiLCJ0eXBlVG9UcnkiLCJnZXREYXRhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/getDataFromDataTransfer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNativeDragSource: () => (/* binding */ createNativeDragSource),\n/* harmony export */   matchNativeItemType: () => (/* binding */ matchNativeItemType)\n/* harmony export */ });\n/* harmony import */ var _nativeTypesConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nativeTypesConfig */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/nativeTypesConfig.js\");\n/* harmony import */ var _NativeDragSource__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NativeDragSource */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/NativeDragSource.js\");\n\n\nfunction createNativeDragSource(type, dataTransfer) {\n    var result = new _NativeDragSource__WEBPACK_IMPORTED_MODULE_0__.NativeDragSource(_nativeTypesConfig__WEBPACK_IMPORTED_MODULE_1__.nativeTypesConfig[type]);\n    result.loadDataTransfer(dataTransfer);\n    return result;\n}\nfunction matchNativeItemType(dataTransfer) {\n    if (!dataTransfer) {\n        return null;\n    }\n    var dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || []);\n    return Object.keys(_nativeTypesConfig__WEBPACK_IMPORTED_MODULE_1__.nativeTypesConfig).filter(function(nativeItemType) {\n        var matchesTypes = _nativeTypesConfig__WEBPACK_IMPORTED_MODULE_1__.nativeTypesConfig[nativeItemType].matchesTypes;\n        return matchesTypes.some(function(t) {\n            return dataTransferTypes.indexOf(t) > -1;\n        });\n    })[0] || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/nativeTypesConfig.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/nativeTypesConfig.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nativeTypesConfig: () => (/* binding */ nativeTypesConfig)\n/* harmony export */ });\n/* harmony import */ var _NativeTypes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../NativeTypes */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js\");\n/* harmony import */ var _getDataFromDataTransfer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDataFromDataTransfer */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/getDataFromDataTransfer.js\");\nvar _nativeTypesConfig;\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\n\n\nvar nativeTypesConfig = (_nativeTypesConfig = {}, _defineProperty(_nativeTypesConfig, _NativeTypes__WEBPACK_IMPORTED_MODULE_0__.FILE, {\n    exposeProperties: {\n        files: function files(dataTransfer) {\n            return Array.prototype.slice.call(dataTransfer.files);\n        },\n        items: function items(dataTransfer) {\n            return dataTransfer.items;\n        },\n        dataTransfer: function dataTransfer(_dataTransfer) {\n            return _dataTransfer;\n        }\n    },\n    matchesTypes: [\n        \"Files\"\n    ]\n}), _defineProperty(_nativeTypesConfig, _NativeTypes__WEBPACK_IMPORTED_MODULE_0__.HTML, {\n    exposeProperties: {\n        html: function html(dataTransfer, matchesTypes) {\n            return (0,_getDataFromDataTransfer__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, \"\");\n        },\n        dataTransfer: function dataTransfer(_dataTransfer2) {\n            return _dataTransfer2;\n        }\n    },\n    matchesTypes: [\n        \"Html\",\n        \"text/html\"\n    ]\n}), _defineProperty(_nativeTypesConfig, _NativeTypes__WEBPACK_IMPORTED_MODULE_0__.URL, {\n    exposeProperties: {\n        urls: function urls(dataTransfer, matchesTypes) {\n            return (0,_getDataFromDataTransfer__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, \"\").split(\"\\n\");\n        },\n        dataTransfer: function dataTransfer(_dataTransfer3) {\n            return _dataTransfer3;\n        }\n    },\n    matchesTypes: [\n        \"Url\",\n        \"text/uri-list\"\n    ]\n}), _defineProperty(_nativeTypesConfig, _NativeTypes__WEBPACK_IMPORTED_MODULE_0__.TEXT, {\n    exposeProperties: {\n        text: function text(dataTransfer, matchesTypes) {\n            return (0,_getDataFromDataTransfer__WEBPACK_IMPORTED_MODULE_1__.getDataFromDataTransfer)(dataTransfer, matchesTypes, \"\");\n        },\n        dataTransfer: function dataTransfer(_dataTransfer4) {\n            return _dataTransfer4;\n        }\n    },\n    matchesTypes: [\n        \"Text\",\n        \"text/plain\"\n    ]\n}), _nativeTypesConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/nativeTypesConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FILE: () => (/* binding */ FILE),\n/* harmony export */   HTML: () => (/* binding */ HTML),\n/* harmony export */   TEXT: () => (/* binding */ TEXT),\n/* harmony export */   URL: () => (/* binding */ URL)\n/* harmony export */ });\nvar FILE = \"__NATIVE_FILE__\";\nvar URL = \"__NATIVE_URL__\";\nvar TEXT = \"__NATIVE_TEXT__\";\nvar HTML = \"__NATIVE_HTML__\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9lc20vTmF0aXZlVHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLElBQUlBLE9BQU8sa0JBQWtCO0FBQzdCLElBQUlDLE1BQU0saUJBQWlCO0FBQzNCLElBQUlDLE9BQU8sa0JBQWtCO0FBQzdCLElBQUlDLE9BQU8sa0JBQWtCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvZXNtL05hdGl2ZVR5cGVzLmpzP2U5MTAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBGSUxFID0gJ19fTkFUSVZFX0ZJTEVfXyc7XG5leHBvcnQgdmFyIFVSTCA9ICdfX05BVElWRV9VUkxfXyc7XG5leHBvcnQgdmFyIFRFWFQgPSAnX19OQVRJVkVfVEVYVF9fJztcbmV4cG9ydCB2YXIgSFRNTCA9ICdfX05BVElWRV9IVE1MX18nOyJdLCJuYW1lcyI6WyJGSUxFIiwiVVJMIiwiVEVYVCIsIkhUTUwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/OffsetUtils.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/OffsetUtils.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDragPreviewOffset: () => (/* binding */ getDragPreviewOffset),\n/* harmony export */   getEventClientOffset: () => (/* binding */ getEventClientOffset),\n/* harmony export */   getNodeClientOffset: () => (/* binding */ getNodeClientOffset)\n/* harmony export */ });\n/* harmony import */ var _BrowserDetector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BrowserDetector */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/BrowserDetector.js\");\n/* harmony import */ var _MonotonicInterpolant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MonotonicInterpolant */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/MonotonicInterpolant.js\");\n\n\nvar ELEMENT_NODE = 1;\nfunction getNodeClientOffset(node) {\n    var el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n    if (!el) {\n        return null;\n    }\n    var _el$getBoundingClient = el.getBoundingClientRect(), top = _el$getBoundingClient.top, left = _el$getBoundingClient.left;\n    return {\n        x: left,\n        y: top\n    };\n}\nfunction getEventClientOffset(e) {\n    return {\n        x: e.clientX,\n        y: e.clientY\n    };\n}\nfunction isImageNode(node) {\n    var _document$documentEle;\n    return node.nodeName === \"IMG\" && ((0,_BrowserDetector__WEBPACK_IMPORTED_MODULE_0__.isFirefox)() || !((_document$documentEle = document.documentElement) !== null && _document$documentEle !== void 0 && _document$documentEle.contains(node)));\n}\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n    var dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n    var dragPreviewHeight = isImage ? dragPreview.height : sourceHeight; // Work around @2x coordinate discrepancies in browsers\n    if ((0,_BrowserDetector__WEBPACK_IMPORTED_MODULE_0__.isSafari)() && isImage) {\n        dragPreviewHeight /= window.devicePixelRatio;\n        dragPreviewWidth /= window.devicePixelRatio;\n    }\n    return {\n        dragPreviewWidth: dragPreviewWidth,\n        dragPreviewHeight: dragPreviewHeight\n    };\n}\nfunction getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n    // The browsers will use the image intrinsic size under different conditions.\n    // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n    var isImage = isImageNode(dragPreview);\n    var dragPreviewNode = isImage ? sourceNode : dragPreview;\n    var dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n    var offsetFromDragPreview = {\n        x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n        y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n    };\n    var sourceWidth = sourceNode.offsetWidth, sourceHeight = sourceNode.offsetHeight;\n    var anchorX = anchorPoint.anchorX, anchorY = anchorPoint.anchorY;\n    var _getDragPreviewSize = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight), dragPreviewWidth = _getDragPreviewSize.dragPreviewWidth, dragPreviewHeight = _getDragPreviewSize.dragPreviewHeight;\n    var calculateYOffset = function calculateYOffset() {\n        var interpolantY = new _MonotonicInterpolant__WEBPACK_IMPORTED_MODULE_1__.MonotonicInterpolant([\n            0,\n            0.5,\n            1\n        ], [\n            offsetFromDragPreview.y,\n            offsetFromDragPreview.y / sourceHeight * dragPreviewHeight,\n            offsetFromDragPreview.y + dragPreviewHeight - sourceHeight\n        ]);\n        var y = interpolantY.interpolate(anchorY); // Work around Safari 8 positioning bug\n        if ((0,_BrowserDetector__WEBPACK_IMPORTED_MODULE_0__.isSafari)() && isImage) {\n            // We'll have to wait for @3x to see if this is entirely correct\n            y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n        }\n        return y;\n    };\n    var calculateXOffset = function calculateXOffset() {\n        // Interpolate coordinates depending on anchor point\n        // If you know a simpler way to do this, let me know\n        var interpolantX = new _MonotonicInterpolant__WEBPACK_IMPORTED_MODULE_1__.MonotonicInterpolant([\n            0,\n            0.5,\n            1\n        ], [\n            offsetFromDragPreview.x,\n            offsetFromDragPreview.x / sourceWidth * dragPreviewWidth,\n            offsetFromDragPreview.x + dragPreviewWidth - sourceWidth\n        ]);\n        return interpolantX.interpolate(anchorX);\n    }; // Force offsets if specified in the options.\n    var offsetX = offsetPoint.offsetX, offsetY = offsetPoint.offsetY;\n    var isManualOffsetX = offsetX === 0 || offsetX;\n    var isManualOffsetY = offsetY === 0 || offsetY;\n    return {\n        x: isManualOffsetX ? offsetX : calculateXOffset(),\n        y: isManualOffsetY ? offsetY : calculateYOffset()\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/OffsetUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/OptionsReader.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/OptionsReader.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OptionsReader: () => (/* binding */ OptionsReader)\n/* harmony export */ });\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nfunction _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nvar OptionsReader = /*#__PURE__*/ function() {\n    function OptionsReader(globalContext, options) {\n        _classCallCheck(this, OptionsReader);\n        _defineProperty(this, \"ownerDocument\", null);\n        _defineProperty(this, \"globalContext\", void 0);\n        _defineProperty(this, \"optionsArgs\", void 0);\n        this.globalContext = globalContext;\n        this.optionsArgs = options;\n    }\n    _createClass(OptionsReader, [\n        {\n            key: \"window\",\n            get: function get() {\n                if (this.globalContext) {\n                    return this.globalContext;\n                } else if (false) {}\n                return undefined;\n            }\n        },\n        {\n            key: \"document\",\n            get: function get() {\n                var _this$globalContext;\n                if ((_this$globalContext = this.globalContext) !== null && _this$globalContext !== void 0 && _this$globalContext.document) {\n                    return this.globalContext.document;\n                } else if (this.window) {\n                    return this.window.document;\n                } else {\n                    return undefined;\n                }\n            }\n        },\n        {\n            key: \"rootElement\",\n            get: function get() {\n                var _this$optionsArgs;\n                return ((_this$optionsArgs = this.optionsArgs) === null || _this$optionsArgs === void 0 ? void 0 : _this$optionsArgs.rootElement) || this.window;\n            }\n        }\n    ]);\n    return OptionsReader;\n}();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/OptionsReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEmptyImage: () => (/* binding */ getEmptyImage)\n/* harmony export */ });\nvar emptyImage;\nfunction getEmptyImage() {\n    if (!emptyImage) {\n        emptyImage = new Image();\n        emptyImage.src = \"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==\";\n    }\n    return emptyImage;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9lc20vZ2V0RW1wdHlJbWFnZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsSUFBSUE7QUFDRyxTQUFTQztJQUNkLElBQUksQ0FBQ0QsWUFBWTtRQUNmQSxhQUFhLElBQUlFO1FBQ2pCRixXQUFXRyxHQUFHLEdBQUc7SUFDbkI7SUFFQSxPQUFPSDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvZXNtL2dldEVtcHR5SW1hZ2UuanM/NmJmMiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZW1wdHlJbWFnZTtcbmV4cG9ydCBmdW5jdGlvbiBnZXRFbXB0eUltYWdlKCkge1xuICBpZiAoIWVtcHR5SW1hZ2UpIHtcbiAgICBlbXB0eUltYWdlID0gbmV3IEltYWdlKCk7XG4gICAgZW1wdHlJbWFnZS5zcmMgPSAnZGF0YTppbWFnZS9naWY7YmFzZTY0LFIwbEdPRGxoQVFBQkFBQUFBQ0g1QkFFS0FBRUFMQUFBQUFBQkFBRUFBQUlDVEFFQU93PT0nO1xuICB9XG5cbiAgcmV0dXJuIGVtcHR5SW1hZ2U7XG59Il0sIm5hbWVzIjpbImVtcHR5SW1hZ2UiLCJnZXRFbXB0eUltYWdlIiwiSW1hZ2UiLCJzcmMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTML5Backend: () => (/* binding */ HTML5Backend),\n/* harmony export */   NativeTypes: () => (/* reexport module object */ _NativeTypes__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   getEmptyImage: () => (/* reexport safe */ _getEmptyImage__WEBPACK_IMPORTED_MODULE_0__.getEmptyImage)\n/* harmony export */ });\n/* harmony import */ var _HTML5BackendImpl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HTML5BackendImpl */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/HTML5BackendImpl.js\");\n/* harmony import */ var _NativeTypes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NativeTypes */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js\");\n/* harmony import */ var _getEmptyImage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEmptyImage */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js\");\n\n\n\n\nvar HTML5Backend = function createBackend(manager, context, options) {\n    return new _HTML5BackendImpl__WEBPACK_IMPORTED_MODULE_2__.HTML5BackendImpl(manager, context, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZG5kLWh0bWw1LWJhY2tlbmQvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNEO0FBQ1Q7QUFDRztBQUN6QjtBQUNoQixJQUFJRyxlQUFlLFNBQVNDLGNBQWNDLE9BQU8sRUFBRUMsT0FBTyxFQUFFQyxPQUFPO0lBQ3hFLE9BQU8sSUFBSVAsK0RBQWdCQSxDQUFDSyxTQUFTQyxTQUFTQztBQUNoRCxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWRuZC1odG1sNS1iYWNrZW5kL2Rpc3QvZXNtL2luZGV4LmpzPzUwNjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSFRNTDVCYWNrZW5kSW1wbCB9IGZyb20gJy4vSFRNTDVCYWNrZW5kSW1wbCc7XG5pbXBvcnQgKiBhcyBOYXRpdmVUeXBlcyBmcm9tICcuL05hdGl2ZVR5cGVzJztcbmV4cG9ydCB7IGdldEVtcHR5SW1hZ2UgfSBmcm9tICcuL2dldEVtcHR5SW1hZ2UnO1xuZXhwb3J0IHsgTmF0aXZlVHlwZXMgfTtcbmV4cG9ydCB2YXIgSFRNTDVCYWNrZW5kID0gZnVuY3Rpb24gY3JlYXRlQmFja2VuZChtYW5hZ2VyLCBjb250ZXh0LCBvcHRpb25zKSB7XG4gIHJldHVybiBuZXcgSFRNTDVCYWNrZW5kSW1wbChtYW5hZ2VyLCBjb250ZXh0LCBvcHRpb25zKTtcbn07Il0sIm5hbWVzIjpbIkhUTUw1QmFja2VuZEltcGwiLCJOYXRpdmVUeXBlcyIsImdldEVtcHR5SW1hZ2UiLCJIVE1MNUJhY2tlbmQiLCJjcmVhdGVCYWNrZW5kIiwibWFuYWdlciIsImNvbnRleHQiLCJvcHRpb25zIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/utils/js_utils.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-dnd-html5-backend/dist/esm/utils/js_utils.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   without: () => (/* binding */ without)\n/* harmony export */ });\n// cheap lodash replacements\nfunction memoize(fn) {\n    var result = null;\n    var memoized = function memoized() {\n        if (result == null) {\n            result = fn();\n        }\n        return result;\n    };\n    return memoized;\n}\n/**\n * drop-in replacement for _.without\n */ function without(items, item) {\n    return items.filter(function(i) {\n        return i !== item;\n    });\n}\nfunction union(itemsA, itemsB) {\n    var set = new Set();\n    var insertItem = function insertItem(item) {\n        return set.add(item);\n    };\n    itemsA.forEach(insertItem);\n    itemsB.forEach(insertItem);\n    var result = [];\n    set.forEach(function(key) {\n        return result.push(key);\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/utils/js_utils.js\n");

/***/ })

};
;