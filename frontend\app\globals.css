@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Code syntax highlighting */
.prose pre {
  @apply bg-muted border border-border rounded-lg;
}

.prose code {
  @apply bg-muted px-1 py-0.5 rounded text-sm;
}

/* Chat message animations */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* File tree animations */
.file-tree-item {
  transition: all 0.2s ease-in-out;
}

.file-tree-item:hover {
  @apply bg-muted/50;
}

/* Resizable panels */
[data-panel-resize-handle-enabled] {
  transition: background-color 0.2s ease;
}

[data-panel-resize-handle-enabled]:hover {
  background-color: #0e639c !important;
}

/* Monaco Editor */
.monaco-editor {
  --vscode-editor-background: #1e1e1e;
  --vscode-editor-foreground: #cccccc;
}

/* React Folder Tree Styles */
.tree-node {
  color: #cccccc !important;
}

.tree-node:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.tree-node-selected {
  background-color: rgba(14, 99, 156, 0.3) !important;
}

/* Custom scrollbar for VS Code theme */
.vscode-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.vscode-scrollbar::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 5px;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #4f4f4f;
}

/* Override highlight.js styles for VS Code theme */
.hljs {
  background: #1e1e1e !important;
  color: #cccccc !important;
}

.hljs-keyword {
  color: #569cd6 !important;
}

.hljs-string {
  color: #ce9178 !important;
}

.hljs-number {
  color: #b5cea8 !important;
}

.hljs-comment {
  color: #6a9955 !important;
}

.hljs-function {
  color: #dcdcaa !important;
}

.hljs-variable {
  color: #9cdcfe !important;
}

.hljs-type {
  color: #4ec9b0 !important;
}

/* React Arborist custom styles */
.react-arborist-tree {
  background: #252526 !important;
  color: #cccccc !important;
}

.react-arborist-node {
  color: #cccccc !important;
}

.react-arborist-node:hover {
  background: #2a2d2e !important;
}

.react-arborist-node[data-selected="true"] {
  background: #37373d !important;
}
