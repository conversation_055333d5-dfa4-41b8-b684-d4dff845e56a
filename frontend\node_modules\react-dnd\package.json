{"name": "react-dnd", "version": "14.0.5", "description": "Drag and Drop for React", "main": "dist/cjs/index.js", "publishConfig": {"main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/types/index.d.ts"}, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/react-dnd/react-dnd.git"}, "license": "MIT", "scripts": {"clean": "gulp clean", "build": "gulp build", "watch": "gulp watch", "bundle": "gulp bundle"}, "dependencies": {"@react-dnd/invariant": "^2.0.0", "@react-dnd/shallowequal": "^2.0.0", "dnd-core": "14.0.1", "fast-deep-equal": "^3.1.3", "hoist-non-react-statics": "^3.3.2"}, "devDependencies": {"@react-dnd/build": "12.0.0", "@testing-library/react": "^12.1.2", "@types/hoist-non-react-statics": "^3.3.1", "@types/node": "^14.17.19", "@types/react": "^17.0.24", "@types/react-dom": "^17.0.9", "gulp": "^4.0.2", "react": "^17.0.2", "react-dnd-test-backend": "portal:../backend-test", "react-dnd-test-utils": "portal:../test-utils", "react-dom": "^17.0.2"}, "peerDependencies": {"@types/hoist-non-react-statics": ">= 3.3.1", "@types/node": ">= 12", "@types/react": ">= 16", "react": ">= 16.14"}, "peerDependenciesMeta": {"@types/hoist-non-react-statics": {"optional": true}, "@types/node": {"optional": true}, "@types/react": {"optional": true}}, "umd": {"name": "ReactDnD", "input": "dist/esm/index.js", "external": ["react", "react-dom"], "globals": {"react": "React", "react-dom": "ReactDOM"}, "alias": {"dnd-core": "dnd-core/dist/esm/index"}}, "module": "dist/esm/index.js", "types": "dist/types/index.d.ts"}