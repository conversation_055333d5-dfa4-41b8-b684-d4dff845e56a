"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(app-pages-browser)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(app-pages-browser)/./components/website-preview.tsx\");\n/* harmony import */ var react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-resizable-panels */ \"(app-pages-browser)/./node_modules/react-resizable-panels/dist/react-resizable-panels.browser.development.esm.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatPage(param) {\n    let { params } = param;\n    _s();\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    const getFileLanguage = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return \"javascript\";\n            case \"ts\":\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    if (authLoading || !user && !authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: chat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: \"flex items-center space-x-1 px-2 py-1 rounded text-xs \".concat(!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (currentVersion === null || currentVersion === void 0 ? void 0 : currentVersion.id) || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id,\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelGroup, {\n                            direction: \"horizontal\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 35,\n                                    minSize: 25,\n                                    maxSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                            version: currentVersion,\n                                            onFileSelect: handleFileSelect\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.PanelResizeHandle, {\n                                    className: \"w-1 bg-[#21262d] hover:bg-[#0969da] transition-colors cursor-col-resize\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_resizable_panels__WEBPACK_IMPORTED_MODULE_9__.Panel, {\n                                    defaultSize: 65,\n                                    minSize: 50,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full bg-[#0d1117] flex flex-col\",\n                                        children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                            version: currentVersion\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-[#7d8590]\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-[#f0f6fc] text-sm font-medium\",\n                                                                children: selectedFile ? selectedFile.path.split(\"/\").pop() : \"No file selected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        height: \"100%\",\n                                                        language: getFileLanguage(selectedFile.path),\n                                                        value: selectedFile.content || \"// No content available\",\n                                                        theme: \"vs-dark\",\n                                                        options: {\n                                                            readOnly: true,\n                                                            minimap: {\n                                                                enabled: false\n                                                            },\n                                                            scrollBeyondLastLine: false,\n                                                            fontSize: 14,\n                                                            fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n                                                            lineNumbers: \"on\",\n                                                            glyphMargin: false,\n                                                            folding: true,\n                                                            lineDecorationsWidth: 0,\n                                                            lineNumbersMinChars: 3,\n                                                            renderLineHighlight: \"line\",\n                                                            selectOnLineNumbers: true,\n                                                            wordWrap: \"on\",\n                                                            automaticLayout: true\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-16 h-16 mx-auto mb-4 text-[#7d8590]\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-[#f0f6fc] mb-2\",\n                                                                    children: \"Code Editor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-[#7d8590]\",\n                                                                    children: \"Select a file from the explorer to view its content\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"851n8SFiz3SRoLlPT4KA3In4dKg=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/chat/[id]/page.tsx\n"));

/***/ })

});