!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactDnD={},e.<PERSON>act)}(this,(function(e,t){"use strict";var r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(t),n=t.createContext({dragDropManager:void 0});function o(e){var t={exports:{}};return e(t,t.exports),t.exports
/*
  object-assign
  (c) <PERSON><PERSON> Sorhus
  @license MIT
  */}Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable,!function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach((function(e){n[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(e){return!1}}()||Object.assign;
/** @license React v17.0.2
   * react-jsx-runtime.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
var i=o((function(e,t){var n=60103;if(t.Fragment=60107,"function"==typeof Symbol&&Symbol.for){var o=Symbol.for;n=o("react.element"),t.Fragment=o("react.fragment")}var i=r.default.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a=Object.prototype.hasOwnProperty,u={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var o,c={},s=null,l=null;for(o in void 0!==r&&(s=""+r),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(l=t.ref),t)a.call(t,o)&&!u.hasOwnProperty(o)&&(c[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===c[o]&&(c[o]=t[o]);return{$$typeof:n,type:e,key:s,ref:l,props:c,_owner:i.current}}t.jsx=c,t.jsxs=c}));
/** @license React v17.0.2
   * react-jsx-runtime.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */o((function(e,t){}));var a,u=o((function(e){e.exports=i}));function c(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];if(!e){var i;if(void 0===t)i=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var a=0;(i=new Error(t.replace(/%s/g,(function(){return n[a++]})))).name="Invariant Violation"}throw i.framesToPop=1,i}}!function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"}(a||(a={}));var s="dnd-core/INIT_COORDS",l="dnd-core/BEGIN_DRAG",f="dnd-core/PUBLISH_DRAG_SOURCE",d="dnd-core/HOVER",p="dnd-core/DROP",h="dnd-core/END_DRAG";function g(e,t){return{type:s,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function v(e,t,r){return t.split(".").reduce((function(e,t){return e&&e[t]?e[t]:r||null}),e)}function b(e,t){return e.filter((function(e){return e!==t}))}function m(e){return"object"===y(e)}function O(e,t){var r=new Map,n=function(e){r.set(e,r.has(e)?r.get(e)+1:1)};e.forEach(n),t.forEach(n);var o=[];return r.forEach((function(e,t){1===e&&o.push(t)})),o}var S={type:s,payload:{clientOffset:null,sourceClientOffset:null}};function w(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{publishSource:!0},n=r.publishSource,o=void 0===n||n,i=r.clientOffset,a=r.getSourceClientOffset,u=e.getMonitor(),c=e.getRegistry();e.dispatch(g(i)),D(t,u,c);var s=k(t,u);if(null!==s){var f=null;if(i){if(!a)throw new Error("getSourceClientOffset must be defined");I(a),f=a(s)}e.dispatch(g(i,f));var d=c.getSource(s),p=d.beginDrag(u,s);if(null!=p){C(p),c.pinSource(s);var h=c.getSourceType(s);return{type:l,payload:{itemType:h,item:p,sourceId:s,clientOffset:i||null,sourceClientOffset:f||null,isSourcePublic:!!o}}}}else e.dispatch(S)}}function D(e,t,r){c(!t.isDragging(),"Cannot call beginDrag while dragging."),e.forEach((function(e){c(r.getSource(e),"Expected sourceIds to be registered.")}))}function I(e){c("function"==typeof e,"When clientOffset is provided, getSourceClientOffset must be a function.")}function C(e){c(m(e),"Item must be an object.")}function k(e,t){for(var r=null,n=e.length-1;n>=0;n--)if(t.canDragSource(e[n])){r=e[n];break}return r}function T(e){return function(){if(e.getMonitor().isDragging())return{type:f}}}function j(e,t){return null===t?null===e:Array.isArray(e)?e.some((function(e){return e===t})):e===t}function P(e){return function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.clientOffset;E(t);var o=t.slice(0),i=e.getMonitor(),a=e.getRegistry();R(o,i,a);var u=i.getItemType();return M(o,a,u),x(o,i,a),{type:d,payload:{targetIds:o,clientOffset:n||null}}}}function E(e){c(Array.isArray(e),"Expected targetIds to be an array.")}function R(e,t,r){c(t.isDragging(),"Cannot call hover while not dragging."),c(!t.didDrop(),"Cannot call hover after drop.");for(var n=0;n<e.length;n++){var o=e[n];c(e.lastIndexOf(o)===n,"Expected targetIds to be unique in the passed array."),c(r.getTarget(o),"Expected targetIds to be registered.")}}function M(e,t,r){for(var n=e.length-1;n>=0;n--){var o=e[n];j(t.getTargetType(o),r)||e.splice(n,1)}}function x(e,t,r){e.forEach((function(e){r.getTarget(e).hover(t,e)}))}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach((function(t){N(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function N(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function U(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.getMonitor(),n=e.getRegistry();H(r);var o=F(r);o.forEach((function(o,i){var a=$(o,i,n,r),u={type:p,payload:{dropResult:_(_({},t),a)}};e.dispatch(u)}))}}function H(e){c(e.isDragging(),"Cannot call drop while not dragging."),c(!e.didDrop(),"Cannot call drop twice during one drag operation.")}function $(e,t,r,n){var o=r.getTarget(e),i=o?o.drop(n,e):void 0;return function(e){c(void 0===e||m(e),"Drop result must either be an object or undefined.")}(i),void 0===i&&(i=0===t?{}:n.getDropResult()),i}function F(e){var t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}function B(e){return function(){var t=e.getMonitor(),r=e.getRegistry();!function(e){c(e.isDragging(),"Cannot call endDrag while not dragging.")}(t);var n=t.getSourceId();return null!=n&&(r.getSource(n,!0).endDrag(t,n),r.unpinSource()),{type:h}}}function L(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function G(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var q=function(){function e(t,r){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),G(this,"store",void 0),G(this,"monitor",void 0),G(this,"backend",void 0),G(this,"isSetUp",!1),G(this,"handleRefCountChange",(function(){var e=n.store.getState().refCount>0;n.backend&&(e&&!n.isSetUp?(n.backend.setup(),n.isSetUp=!0):!e&&n.isSetUp&&(n.backend.teardown(),n.isSetUp=!1))})),this.store=t,this.monitor=r,t.subscribe(this.handleRefCountChange)}var t,r;return t=e,(r=[{key:"receiveBackend",value:function(e){this.backend=e}},{key:"getMonitor",value:function(){return this.monitor}},{key:"getBackend",value:function(){return this.backend}},{key:"getRegistry",value:function(){return this.monitor.registry}},{key:"getActions",value:function(){var e=this,t=this.store.dispatch,r=function(e){return{beginDrag:w(e),publishDragSource:T(e),hover:P(e),drop:U(e),endDrag:B(e)}}(this);return Object.keys(r).reduce((function(n,o){var i,a=r[o];return n[o]=(i=a,function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];var a=i.apply(e,n);void 0!==a&&t(a)}),n}),{})}},{key:"dispatch",value:function(e){this.store.dispatch(e)}}])&&L(t.prototype,r),e}();function W(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var V="function"==typeof Symbol&&Symbol.observable||"@@observable",Y=function(){return Math.random().toString(36).substring(7).split("").join(".")},z={INIT:"@@redux/INIT"+Y(),REPLACE:"@@redux/REPLACE"+Y(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Y()}};function K(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function X(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(W(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(W(1));return r(X)(e,t)}if("function"!=typeof e)throw new Error(W(2));var o=e,i=t,a=[],u=a,c=!1;function s(){u===a&&(u=a.slice())}function l(){if(c)throw new Error(W(3));return i}function f(e){if("function"!=typeof e)throw new Error(W(4));if(c)throw new Error(W(5));var t=!0;return s(),u.push(e),function(){if(t){if(c)throw new Error(W(6));t=!1,s();var r=u.indexOf(e);u.splice(r,1),a=null}}}function d(e){if(!K(e))throw new Error(W(7));if(void 0===e.type)throw new Error(W(8));if(c)throw new Error(W(9));try{c=!0,i=o(i,e)}finally{c=!1}for(var t=a=u,r=0;r<t.length;r++)(0,t[r])();return e}function p(e){if("function"!=typeof e)throw new Error(W(10));o=e,d({type:z.REPLACE})}function h(){var e,t=f;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(W(11));function r(){e.next&&e.next(l())}return r(),{unsubscribe:t(r)}}})[V]=function(){return this},e}return d({type:z.INIT}),(n={dispatch:d,subscribe:f,getState:l,replaceReducer:p})[V]=h,n}var J=function(e,t){return e===t};function Q(e,t){return!e&&!t||!(!e||!t)&&e.x===t.x&&e.y===t.y}function Z(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:J;if(e.length!==t.length)return!1;for(var n=0;n<e.length;++n)if(!r(e[n],t[n]))return!1;return!0}function ee(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function te(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ee(Object(r),!0).forEach((function(t){re(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function re(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ne={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function oe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ne,t=arguments.length>1?arguments[1]:void 0,r=t.payload;switch(t.type){case s:case l:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case d:return Q(e.clientOffset,r.clientOffset)?e:te(te({},e),{},{clientOffset:r.clientOffset});case h:case p:return ne;default:return e}}var ie="dnd-core/ADD_SOURCE",ae="dnd-core/ADD_TARGET",ue="dnd-core/REMOVE_SOURCE",ce="dnd-core/REMOVE_TARGET";function se(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function le(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?se(Object(r),!0).forEach((function(t){fe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):se(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function fe(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var de={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function pe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:de,t=arguments.length>1?arguments[1]:void 0,r=t.payload;switch(t.type){case l:return le(le({},e),{},{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case f:return le(le({},e),{},{isSourcePublic:!0});case d:return le(le({},e),{},{targetIds:r.targetIds});case ce:return-1===e.targetIds.indexOf(r.targetId)?e:le(le({},e),{},{targetIds:b(e.targetIds,r.targetId)});case p:return le(le({},e),{},{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case h:return le(le({},e),{},{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}function he(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case ie:case ae:return e+1;case ue:case ce:return e-1;default:return e}}var ge=[],ye=[];function ve(e,t){return e!==ge&&(e===ye||void 0===t||(r=e,t.filter((function(e){return r.indexOf(e)>-1}))).length>0);var r}function be(){var e=arguments.length>1?arguments[1]:void 0;switch(e.type){case d:break;case ie:case ae:case ce:case ue:return ge;case l:case f:case h:case p:default:return ye}var t=e.payload,r=t.targetIds,n=void 0===r?[]:r,o=t.prevTargetIds,i=void 0===o?[]:o,a=O(n,i),u=a.length>0||!Z(n,i);if(!u)return ge;var c=i[i.length-1],s=n[n.length-1];return c!==s&&(c&&a.push(c),s&&a.push(s)),a}function me(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e+1}function Oe(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Se(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Oe(Object(r),!0).forEach((function(t){we(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Oe(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function we(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function De(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return{dirtyHandlerIds:be(e.dirtyHandlerIds,{type:t.type,payload:Se(Se({},t.payload),{},{prevTargetIds:v(e,"dragOperation.targetIds",[])})}),dragOffset:oe(e.dragOffset,t),refCount:he(e.refCount,t),dragOperation:pe(e.dragOperation,t),stateId:me(e.stateId)}}function Ie(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ce(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ke(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}ge.__IS_NONE__=!0,ye.__IS_ALL__=!0;var Te=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ke(this,"store",void 0),ke(this,"registry",void 0),this.store=t,this.registry=r}var t,r;return t=e,(r=[{key:"subscribeToStateChange",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{handlerIds:void 0},n=r.handlerIds;c("function"==typeof e,"listener must be a function."),c(void 0===n||Array.isArray(n),"handlerIds, when specified, must be an array of strings.");var o=this.store.getState().stateId,i=function(){var r=t.store.getState(),i=r.stateId;try{i===o||i===o+1&&!ve(r.dirtyHandlerIds,n)||e()}finally{o=i}};return this.store.subscribe(i)}},{key:"subscribeToOffsetChange",value:function(e){var t=this;c("function"==typeof e,"listener must be a function.");var r=this.store.getState().dragOffset;return this.store.subscribe((function(){var n=t.store.getState().dragOffset;n!==r&&(r=n,e())}))}},{key:"canDragSource",value:function(e){if(!e)return!1;var t=this.registry.getSource(e);return c(t,"Expected to find a valid source. sourceId=".concat(e)),!this.isDragging()&&t.canDrag(this,e)}},{key:"canDropOnTarget",value:function(e){if(!e)return!1;var t=this.registry.getTarget(e);return c(t,"Expected to find a valid target. targetId=".concat(e)),!(!this.isDragging()||this.didDrop())&&j(this.registry.getTargetType(e),this.getItemType())&&t.canDrop(this,e)}},{key:"isDragging",value:function(){return Boolean(this.getItemType())}},{key:"isDraggingSource",value:function(e){if(!e)return!1;var t=this.registry.getSource(e,!0);return c(t,"Expected to find a valid source. sourceId=".concat(e)),!(!this.isDragging()||!this.isSourcePublic())&&this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}},{key:"isOverTarget",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shallow:!1};if(!e)return!1;var r=t.shallow;if(!this.isDragging())return!1;var n=this.registry.getTargetType(e),o=this.getItemType();if(o&&!j(n,o))return!1;var i=this.getTargetIds();if(!i.length)return!1;var a=i.indexOf(e);return r?a===i.length-1:a>-1}},{key:"getItemType",value:function(){return this.store.getState().dragOperation.itemType}},{key:"getItem",value:function(){return this.store.getState().dragOperation.item}},{key:"getSourceId",value:function(){return this.store.getState().dragOperation.sourceId}},{key:"getTargetIds",value:function(){return this.store.getState().dragOperation.targetIds}},{key:"getDropResult",value:function(){return this.store.getState().dragOperation.dropResult}},{key:"didDrop",value:function(){return this.store.getState().dragOperation.didDrop}},{key:"isSourcePublic",value:function(){return Boolean(this.store.getState().dragOperation.isSourcePublic)}},{key:"getInitialClientOffset",value:function(){return this.store.getState().dragOffset.initialClientOffset}},{key:"getInitialSourceClientOffset",value:function(){return this.store.getState().dragOffset.initialSourceClientOffset}},{key:"getClientOffset",value:function(){return this.store.getState().dragOffset.clientOffset}},{key:"getSourceClientOffset",value:function(){return t=(e=this.store.getState().dragOffset).clientOffset,r=e.initialClientOffset,n=e.initialSourceClientOffset,t&&r&&n?Ie(function(e,t){return{x:e.x+t.x,y:e.y+t.y}}(t,n),r):null;var e,t,r,n}},{key:"getDifferenceFromInitialOffset",value:function(){return t=(e=this.store.getState().dragOffset).clientOffset,r=e.initialClientOffset,t&&r?Ie(t,r):null;var e,t,r}}])&&Ce(t.prototype,r),e}(),je=0;function Pe(e){return(Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ee(e,t){t&&Array.isArray(e)?e.forEach((function(e){return Ee(e,!1)})):c("string"==typeof e||"symbol"===Pe(e),t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}function Re(e){xe.length||Me(),xe[xe.length]=e}var Me,xe=[],Ae=0;function _e(){for(;Ae<xe.length;){var e=Ae;if(Ae+=1,xe[e].call(),Ae>1024){for(var t=0,r=xe.length-Ae;t<r;t++)xe[t]=xe[t+Ae];xe.length-=Ae,Ae=0}}xe.length=0,Ae=0}var Ne,Ue,He,$e="undefined"!=typeof global?global:self,Fe=$e.MutationObserver||$e.WebKitMutationObserver;function Be(e){return function(){var t=setTimeout(n,0),r=setInterval(n,50);function n(){clearTimeout(t),clearInterval(r),e()}}}"function"==typeof Fe?(Ne=1,Ue=new Fe(_e),He=document.createTextNode(""),Ue.observe(He,{characterData:!0}),Me=function(){Ne=-Ne,He.data=Ne}):Me=Be(_e),Re.requestFlush=Me,Re.makeRequestCallFromTimer=Be;var Le=[],Ge=[],qe=Re.makeRequestCallFromTimer((function(){if(Ge.length)throw Ge.shift()}));function We(e){var t;(t=Le.length?Le.pop():new Ve).task=e,Re(t)}var Ve=function(){function e(){}return e.prototype.call=function(){try{this.task.call()}catch(e){We.onerror?We.onerror(e):(Ge.push(e),qe())}finally{this.task=null,Le[Le.length]=this}},e}();function Ye(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ze(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ke(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Xe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Xe(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Je(e){switch(e[0]){case"S":return a.SOURCE;case"T":return a.TARGET;default:c(!1,"Cannot parse handler ID: ".concat(e))}}function Qe(e,t){var r=e.entries(),n=!1;do{var o=r.next(),i=o.done;if(Ke(o.value,2)[1]===t)return!0;n=!!i}while(!n);return!1}var Ze=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ze(this,"types",new Map),ze(this,"dragSources",new Map),ze(this,"dropTargets",new Map),ze(this,"pinnedSourceId",null),ze(this,"pinnedSource",null),ze(this,"store",void 0),this.store=t}var t,r;return t=e,(r=[{key:"addSource",value:function(e,t){Ee(e),function(e){c("function"==typeof e.canDrag,"Expected canDrag to be a function."),c("function"==typeof e.beginDrag,"Expected beginDrag to be a function."),c("function"==typeof e.endDrag,"Expected endDrag to be a function.")}(t);var r=this.addHandler(a.SOURCE,e,t);return this.store.dispatch(function(e){return{type:ie,payload:{sourceId:e}}}(r)),r}},{key:"addTarget",value:function(e,t){Ee(e,!0),function(e){c("function"==typeof e.canDrop,"Expected canDrop to be a function."),c("function"==typeof e.hover,"Expected hover to be a function."),c("function"==typeof e.drop,"Expected beginDrag to be a function.")}(t);var r=this.addHandler(a.TARGET,e,t);return this.store.dispatch(function(e){return{type:ae,payload:{targetId:e}}}(r)),r}},{key:"containsHandler",value:function(e){return Qe(this.dragSources,e)||Qe(this.dropTargets,e)}},{key:"getSource",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];c(this.isSourceId(e),"Expected a valid source ID.");var r=t&&e===this.pinnedSourceId,n=r?this.pinnedSource:this.dragSources.get(e);return n}},{key:"getTarget",value:function(e){return c(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}},{key:"getSourceType",value:function(e){return c(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}},{key:"getTargetType",value:function(e){return c(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}},{key:"isSourceId",value:function(e){return Je(e)===a.SOURCE}},{key:"isTargetId",value:function(e){return Je(e)===a.TARGET}},{key:"removeSource",value:function(e){var t=this;c(this.getSource(e),"Expected an existing source."),this.store.dispatch(function(e){return{type:ue,payload:{sourceId:e}}}(e)),We((function(){t.dragSources.delete(e),t.types.delete(e)}))}},{key:"removeTarget",value:function(e){c(this.getTarget(e),"Expected an existing target."),this.store.dispatch(function(e){return{type:ce,payload:{targetId:e}}}(e)),this.dropTargets.delete(e),this.types.delete(e)}},{key:"pinSource",value:function(e){var t=this.getSource(e);c(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}},{key:"unpinSource",value:function(){c(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}},{key:"addHandler",value:function(e,t,r){var n=function(e){var t=(je++).toString();switch(e){case a.SOURCE:return"S".concat(t);case a.TARGET:return"T".concat(t);default:throw new Error("Unknown Handler Role: ".concat(e))}}(e);return this.types.set(n,t),e===a.SOURCE?this.dragSources.set(n,r):e===a.TARGET&&this.dropTargets.set(n,r),n}}])&&Ye(t.prototype,r),e}();function et(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=tt(n),i=new Te(o,new Ze(o)),a=new q(o,i),u=e(a,t,r);return a.receiveBackend(u),a}function tt(e){var t="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return X(De,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}var rt=["children"];function nt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ot=0,it=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__"),at=t.memo((function(e){var r=e.children,o=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return nt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?nt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(function(e){return"manager"in e?[{dragDropManager:e.manager},!1]:[function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ut(),r=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,o=t;return o[it]||(o[it]={dragDropManager:et(e,t,r,n)}),o[it]}(e.backend,e.context,e.options,e.debugMode),!e.context]}(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,rt)),2),i=o[0],a=o[1];return t.useEffect((function(){if(a){var e=ut();return++ot,function(){0==--ot&&(e[it]=null)}}}),[]),u.jsx(n.Provider,Object.assign({value:i},{children:r}),void 0)}));function ut(){return"undefined"!=typeof global?global:window}var ct=t.memo((function(e){var r=e.connect,n=e.src;return t.useEffect((function(){if("undefined"!=typeof Image){var e=!1,t=new Image;return t.src=n,t.onload=function(){r(t),e=!0},function(){e&&r(null)}}})),null}));function st(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function lt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ft=!1,dt=!1,pt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),lt(this,"internalMonitor",void 0),lt(this,"sourceId",null),this.internalMonitor=t.getMonitor()}var t,r;return t=e,(r=[{key:"receiveHandlerId",value:function(e){this.sourceId=e}},{key:"getHandlerId",value:function(){return this.sourceId}},{key:"canDrag",value:function(){c(!ft,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return ft=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{ft=!1}}},{key:"isDragging",value:function(){if(!this.sourceId)return!1;c(!dt,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return dt=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{dt=!1}}},{key:"subscribeToStateChange",value:function(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}},{key:"isDraggingSource",value:function(e){return this.internalMonitor.isDraggingSource(e)}},{key:"isOverTarget",value:function(e,t){return this.internalMonitor.isOverTarget(e,t)}},{key:"getTargetIds",value:function(){return this.internalMonitor.getTargetIds()}},{key:"isSourcePublic",value:function(){return this.internalMonitor.isSourcePublic()}},{key:"getSourceId",value:function(){return this.internalMonitor.getSourceId()}},{key:"subscribeToOffsetChange",value:function(e){return this.internalMonitor.subscribeToOffsetChange(e)}},{key:"canDragSource",value:function(e){return this.internalMonitor.canDragSource(e)}},{key:"canDropOnTarget",value:function(e){return this.internalMonitor.canDropOnTarget(e)}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}])&&st(t.prototype,r),e}();function ht(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function gt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var yt=!1,vt=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),gt(this,"internalMonitor",void 0),gt(this,"targetId",null),this.internalMonitor=t.getMonitor()}var t,r;return t=e,(r=[{key:"receiveHandlerId",value:function(e){this.targetId=e}},{key:"getHandlerId",value:function(){return this.targetId}},{key:"subscribeToStateChange",value:function(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}},{key:"canDrop",value:function(){if(!this.targetId)return!1;c(!yt,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return yt=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{yt=!1}}},{key:"isOver",value:function(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}])&&ht(t.prototype,r),e}();function bt(e){if("string"!=typeof e.type){var t=e.type.displayName||e.type.name||"the component";throw new Error("Only native element nodes can now be passed to React DnD connectors."+"You can either wrap ".concat(t," into a <div>, or turn it into a ")+"drag source or a drop target itself.")}}function mt(e){var r={};return Object.keys(e).forEach((function(n){var o=e[n];if(n.endsWith("Ref"))r[n]=e[n];else{var i=function(e){return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!t.isValidElement(r)){var o=r;return e(o,n),o}var i=r;return bt(i),St(i,n?function(t){return e(t,n)}:e)}}(o);r[n]=function(){return i}}})),r}function Ot(e,t){"function"==typeof e?e(t):e.current=t}function St(e,r){var n=e.ref;return c("string"!=typeof n,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),n?t.cloneElement(e,{ref:function(e){Ot(n,e),Ot(r,e)}}):t.cloneElement(e,{ref:r})}function wt(e){return(wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Dt(e){return null!==e&&"object"===wt(e)&&Object.prototype.hasOwnProperty.call(e,"current")}function It(e,t,r,n){var o=r?r.call(n,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var i=Object.keys(e),a=Object.keys(t);if(i.length!==a.length)return!1;for(var u=Object.prototype.hasOwnProperty.bind(t),c=0;c<i.length;c++){var s=i[c];if(!u(s))return!1;var l=e[s],f=t[s];if(!1===(o=r?r.call(n,l,f,s):void 0)||void 0===o&&l!==f)return!1}return!0}function Ct(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function kt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Tt=function(){function e(t){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),kt(this,"hooks",mt({dragSource:function(e,t){r.clearDragSource(),r.dragSourceOptions=t||null,Dt(e)?r.dragSourceRef=e:r.dragSourceNode=e,r.reconnectDragSource()},dragPreview:function(e,t){r.clearDragPreview(),r.dragPreviewOptions=t||null,Dt(e)?r.dragPreviewRef=e:r.dragPreviewNode=e,r.reconnectDragPreview()}})),kt(this,"handlerId",null),kt(this,"dragSourceRef",null),kt(this,"dragSourceNode",void 0),kt(this,"dragSourceOptionsInternal",null),kt(this,"dragSourceUnsubscribe",void 0),kt(this,"dragPreviewRef",null),kt(this,"dragPreviewNode",void 0),kt(this,"dragPreviewOptionsInternal",null),kt(this,"dragPreviewUnsubscribe",void 0),kt(this,"lastConnectedHandlerId",null),kt(this,"lastConnectedDragSource",null),kt(this,"lastConnectedDragSourceOptions",null),kt(this,"lastConnectedDragPreview",null),kt(this,"lastConnectedDragPreviewOptions",null),kt(this,"backend",void 0),this.backend=t}var t,r;return t=e,(r=[{key:"receiveHandlerId",value:function(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}},{key:"connectTarget",get:function(){return this.dragSource}},{key:"dragSourceOptions",get:function(){return this.dragSourceOptionsInternal},set:function(e){this.dragSourceOptionsInternal=e}},{key:"dragPreviewOptions",get:function(){return this.dragPreviewOptionsInternal},set:function(e){this.dragPreviewOptionsInternal=e}},{key:"reconnect",value:function(){this.reconnectDragSource(),this.reconnectDragPreview()}},{key:"reconnectDragSource",value:function(){var e=this.dragSource,t=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();t&&this.disconnectDragSource(),this.handlerId&&(e?t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)):this.lastConnectedDragSource=e)}},{key:"reconnectDragPreview",value:function(){var e=this.dragPreview,t=this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();t&&this.disconnectDragPreview(),this.handlerId&&(e?t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=e,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,e,this.dragPreviewOptions)):this.lastConnectedDragPreview=e)}},{key:"didHandlerIdChange",value:function(){return this.lastConnectedHandlerId!==this.handlerId}},{key:"didConnectedDragSourceChange",value:function(){return this.lastConnectedDragSource!==this.dragSource}},{key:"didConnectedDragPreviewChange",value:function(){return this.lastConnectedDragPreview!==this.dragPreview}},{key:"didDragSourceOptionsChange",value:function(){return!It(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}},{key:"didDragPreviewOptionsChange",value:function(){return!It(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}},{key:"disconnectDragSource",value:function(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}},{key:"disconnectDragPreview",value:function(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}},{key:"dragSource",get:function(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}},{key:"dragPreview",get:function(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}},{key:"clearDragSource",value:function(){this.dragSourceNode=null,this.dragSourceRef=null}},{key:"clearDragPreview",value:function(){this.dragPreviewNode=null,this.dragPreviewRef=null}}])&&Ct(t.prototype,r),e}();function jt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Pt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Et=function(){function e(t){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Pt(this,"hooks",mt({dropTarget:function(e,t){r.clearDropTarget(),r.dropTargetOptions=t,Dt(e)?r.dropTargetRef=e:r.dropTargetNode=e,r.reconnect()}})),Pt(this,"handlerId",null),Pt(this,"dropTargetRef",null),Pt(this,"dropTargetNode",void 0),Pt(this,"dropTargetOptionsInternal",null),Pt(this,"unsubscribeDropTarget",void 0),Pt(this,"lastConnectedHandlerId",null),Pt(this,"lastConnectedDropTarget",null),Pt(this,"lastConnectedDropTargetOptions",null),Pt(this,"backend",void 0),this.backend=t}var t,r;return t=e,(r=[{key:"connectTarget",get:function(){return this.dropTarget}},{key:"reconnect",value:function(){var e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();var t=this.dropTarget;this.handlerId&&(t?e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions)):this.lastConnectedDropTarget=t)}},{key:"receiveHandlerId",value:function(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}},{key:"dropTargetOptions",get:function(){return this.dropTargetOptionsInternal},set:function(e){this.dropTargetOptionsInternal=e}},{key:"didHandlerIdChange",value:function(){return this.lastConnectedHandlerId!==this.handlerId}},{key:"didDropTargetChange",value:function(){return this.lastConnectedDropTarget!==this.dropTarget}},{key:"didOptionsChange",value:function(){return!It(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}},{key:"disconnectDropTarget",value:function(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}},{key:"dropTarget",get:function(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}},{key:"clearDropTarget",value:function(){this.dropTargetRef=null,this.dropTargetNode=null}}])&&jt(t.prototype,r),e}();function Rt(e,t,r){var n=r.getRegistry(),o=n.addTarget(e,t);return[o,function(){return n.removeTarget(o)}]}function Mt(e,t,r){var n=r.getRegistry(),o=n.addSource(e,t);return[o,function(){return n.removeSource(o)}]}function xt(e){return(xt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function At(e){var t=e.current;return null==t?null:t.decoratedRef?t.decoratedRef.current:t}function _t(e){return(t=e)&&t.prototype&&"function"==typeof t.prototype.render||function(e){var t;return"Symbol(react.forward_ref)"===(null==e||null===(t=e.$$typeof)||void 0===t?void 0:t.toString())}(e);var t}function Nt(e){return"function"==typeof e}function Ut(){}function Ht(e){if(!function(e){return"object"===xt(e)&&null!==e}(e))return!1;if(null===Object.getPrototypeOf(e))return!0;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function $t(e,t){return"string"==typeof e||"symbol"===xt(e)||!!t&&Array.isArray(e)&&e.every((function(e){return $t(e,!1)}))}function Ft(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Lt(e,t,r){return t&&Bt(e.prototype,t),r&&Bt(e,r),e}function Gt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var qt=function(){function e(t){Ft(this,e),Gt(this,"isDisposed",!1),Gt(this,"action",void 0),this.action=Nt(t)?t:Ut}return Lt(e,[{key:"dispose",value:function(){this.isDisposed||(this.action(),this.isDisposed=!0)}}],[{key:"isDisposable",value:function(e){return Boolean(e&&Nt(e.dispose))}},{key:"_fixup",value:function(t){return e.isDisposable(t)?t:e.empty}},{key:"create",value:function(t){return new e(t)}}]),e}();Gt(qt,"empty",{dispose:Ut});var Wt=function(){function e(){Ft(this,e),Gt(this,"isDisposed",!1),Gt(this,"disposables",void 0);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];this.disposables=r}return Lt(e,[{key:"add",value:function(e){this.isDisposed?e.dispose():this.disposables.push(e)}},{key:"remove",value:function(e){var t=!1;if(!this.isDisposed){var r=this.disposables.indexOf(e);-1!==r&&(t=!0,this.disposables.splice(r,1),e.dispose())}return t}},{key:"clear",value:function(){if(!this.isDisposed){for(var e=this.disposables.length,t=new Array(e),r=0;r<e;r++)t[r]=this.disposables[r];this.disposables=[];for(var n=0;n<e;n++)t[n].dispose()}}},{key:"dispose",value:function(){if(!this.isDisposed){this.isDisposed=!0;for(var e=this.disposables.length,t=new Array(e),r=0;r<e;r++)t[r]=this.disposables[r];this.disposables=[];for(var n=0;n<e;n++)t[n].dispose()}}}]),e}(),Vt=function(){function e(){Ft(this,e),Gt(this,"isDisposed",!1),Gt(this,"current",void 0)}return Lt(e,[{key:"getDisposable",value:function(){return this.current}},{key:"setDisposable",value:function(e){var t=this.isDisposed;if(!t){var r=this.current;this.current=e,r&&r.dispose()}t&&e&&e.dispose()}},{key:"dispose",value:function(){if(!this.isDisposed){this.isDisposed=!0;var e=this.current;this.current=void 0,e&&e.dispose()}}}]),e}(),Yt="function"==typeof Symbol&&Symbol.for,zt=Yt?Symbol.for("react.element"):60103,Kt=Yt?Symbol.for("react.portal"):60106,Xt=Yt?Symbol.for("react.fragment"):60107,Jt=Yt?Symbol.for("react.strict_mode"):60108,Qt=Yt?Symbol.for("react.profiler"):60114,Zt=Yt?Symbol.for("react.provider"):60109,er=Yt?Symbol.for("react.context"):60110,tr=Yt?Symbol.for("react.async_mode"):60111,rr=Yt?Symbol.for("react.concurrent_mode"):60111,nr=Yt?Symbol.for("react.forward_ref"):60112,or=Yt?Symbol.for("react.suspense"):60113,ir=Yt?Symbol.for("react.suspense_list"):60120,ar=Yt?Symbol.for("react.memo"):60115,ur=Yt?Symbol.for("react.lazy"):60116,cr=Yt?Symbol.for("react.block"):60121,sr=Yt?Symbol.for("react.fundamental"):60117,lr=Yt?Symbol.for("react.responder"):60118,fr=Yt?Symbol.for("react.scope"):60119;function dr(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case zt:switch(e=e.type){case tr:case rr:case Xt:case Qt:case Jt:case or:return e;default:switch(e=e&&e.$$typeof){case er:case nr:case ur:case ar:case Zt:return e;default:return t}}case Kt:return t}}}function pr(e){return dr(e)===rr}var hr={AsyncMode:tr,ConcurrentMode:rr,ContextConsumer:er,ContextProvider:Zt,Element:zt,ForwardRef:nr,Fragment:Xt,Lazy:ur,Memo:ar,Portal:Kt,Profiler:Qt,StrictMode:Jt,Suspense:or,isAsyncMode:function(e){return pr(e)||dr(e)===tr},isConcurrentMode:pr,isContextConsumer:function(e){return dr(e)===er},isContextProvider:function(e){return dr(e)===Zt},isElement:function(e){return"object"==typeof e&&null!==e&&e.$$typeof===zt},isForwardRef:function(e){return dr(e)===nr},isFragment:function(e){return dr(e)===Xt},isLazy:function(e){return dr(e)===ur},isMemo:function(e){return dr(e)===ar},isPortal:function(e){return dr(e)===Kt},isProfiler:function(e){return dr(e)===Qt},isStrictMode:function(e){return dr(e)===Jt},isSuspense:function(e){return dr(e)===or},isValidElementType:function(e){return"string"==typeof e||"function"==typeof e||e===Xt||e===rr||e===Qt||e===Jt||e===or||e===ir||"object"==typeof e&&null!==e&&(e.$$typeof===ur||e.$$typeof===ar||e.$$typeof===Zt||e.$$typeof===er||e.$$typeof===nr||e.$$typeof===sr||e.$$typeof===lr||e.$$typeof===fr||e.$$typeof===cr)},typeOf:dr};
/** @license React v16.13.1
   * react-is.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */o((function(e,t){}));var gr=o((function(e){e.exports=hr})),yr={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},vr={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},br={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},mr={};function Or(e){return gr.isMemo(e)?br:mr[e.$$typeof]||yr}mr[gr.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},mr[gr.Memo]=br;var Sr=Object.defineProperty,wr=Object.getOwnPropertyNames,Dr=Object.getOwnPropertySymbols,Ir=Object.getOwnPropertyDescriptor,Cr=Object.getPrototypeOf,kr=Object.prototype,Tr=function e(t,r,n){if("string"!=typeof r){if(kr){var o=Cr(r);o&&o!==kr&&e(t,o,n)}var i=wr(r);Dr&&(i=i.concat(Dr(r)));for(var a=Or(t),u=Or(r),c=0;c<i.length;++c){var s=i[c];if(!(vr[s]||n&&n[s]||u&&u[s]||a&&a[s])){var l=Ir(r,s);try{Sr(t,s,l)}catch(e){}}}}return t};function jr(e){return(jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Pr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Er(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Rr(e,t){return(Rr=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Mr(e,t){if(t&&("object"===jr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return xr(e)}function xr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ar(e){return(Ar=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _r(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nr(e){var r=e.DecoratedComponent,o=e.createHandler,i=e.createMonitor,a=e.createConnector,s=e.registerHandler,l=e.containerDisplayName,f=e.getType,d=e.collect,p=e.options.arePropsEqual,h=void 0===p?It:p,g=r,y=r.displayName||r.name||"Component",v=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Rr(e,t)}(v,e);var r,l,p=function(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=Ar(e);if(t){var o=Ar(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return Mr(this,r)}}(v);function v(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,v),_r(xr(r=p.call(this,e)),"decoratedRef",t.createRef()),_r(xr(r),"handlerId",void 0),_r(xr(r),"manager",void 0),_r(xr(r),"handlerMonitor",void 0),_r(xr(r),"handlerConnector",void 0),_r(xr(r),"handler",void 0),_r(xr(r),"disposable",void 0),_r(xr(r),"currentType",void 0),_r(xr(r),"handleChange",(function(){var e=r.getCurrentState();It(e,r.state)||r.setState(e)})),r.disposable=new Vt,r.receiveProps(e),r.dispose(),r}return r=v,(l=[{key:"getHandlerId",value:function(){return this.handlerId}},{key:"getDecoratedComponentInstance",value:function(){return c(this.decoratedRef.current,"In order to access an instance of the decorated component, it must either be a class component or use React.forwardRef()"),this.decoratedRef.current}},{key:"shouldComponentUpdate",value:function(e,t){return!h(e,this.props)||!It(t,this.state)}},{key:"componentDidMount",value:function(){this.disposable=new Vt,this.currentType=void 0,this.receiveProps(this.props),this.handleChange()}},{key:"componentDidUpdate",value:function(e){h(this.props,e)||(this.receiveProps(this.props),this.handleChange())}},{key:"componentWillUnmount",value:function(){this.dispose()}},{key:"receiveProps",value:function(e){this.handler&&(this.handler.receiveProps(e),this.receiveType(f(e)))}},{key:"receiveType",value:function(e){if(this.handlerMonitor&&this.manager&&this.handlerConnector&&e!==this.currentType){this.currentType=e;var t=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Pr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Pr(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(s(e,this.handler,this.manager),2),r=t[0],n=t[1];this.handlerId=r,this.handlerMonitor.receiveHandlerId(r),this.handlerConnector.receiveHandlerId(r);var o=this.manager.getMonitor().subscribeToStateChange(this.handleChange,{handlerIds:[r]});this.disposable.setDisposable(new Wt(new qt(o),new qt(n)))}}},{key:"dispose",value:function(){this.disposable.dispose(),this.handlerConnector&&this.handlerConnector.receiveHandlerId(null)}},{key:"getCurrentState",value:function(){return this.handlerConnector?d(this.handlerConnector.hooks,this.handlerMonitor,this.props):{}}},{key:"render",value:function(){var e=this;return u.jsx(n.Consumer,{children:function(t){var r=t.dragDropManager;return e.receiveDragDropManager(r),"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame((function(){var t;return null===(t=e.handlerConnector)||void 0===t?void 0:t.reconnect()})),u.jsx(g,Object.assign({},e.props,e.getCurrentState(),{ref:_t(g)?e.decoratedRef:null}),void 0)}},void 0)}},{key:"receiveDragDropManager",value:function(e){void 0===this.manager&&(c(void 0!==e,"Could not find the drag and drop manager in the context of %s. Make sure to render a DndProvider component in your top-level component. Read more: http://react-dnd.github.io/react-dnd/docs/troubleshooting#could-not-find-the-drag-and-drop-manager-in-the-context",y,y),void 0!==e&&(this.manager=e,this.handlerMonitor=i(e),this.handlerConnector=a(e.getBackend()),this.handler=o(this.handlerMonitor,this.decoratedRef)))}}])&&Er(r.prototype,l),v}(t.Component);return _r(v,"DecoratedComponent",r),_r(v,"displayName","".concat(l,"(").concat(y,")")),Tr(v,r)}function Ur(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Hr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $r=["canDrag","beginDrag","isDragging","endDrag"],Fr=["beginDrag"],Br=function(){function e(t,r,n){var o=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Hr(this,"props",null),Hr(this,"spec",void 0),Hr(this,"monitor",void 0),Hr(this,"ref",void 0),Hr(this,"beginDrag",(function(){if(o.props)return o.spec.beginDrag(o.props,o.monitor,o.ref.current)})),this.spec=t,this.monitor=r,this.ref=n}var t,r;return t=e,(r=[{key:"receiveProps",value:function(e){this.props=e}},{key:"canDrag",value:function(){return!!this.props&&(!this.spec.canDrag||this.spec.canDrag(this.props,this.monitor))}},{key:"isDragging",value:function(e,t){return!!this.props&&(this.spec.isDragging?this.spec.isDragging(this.props,this.monitor):t===e.getSourceId())}},{key:"endDrag",value:function(){this.props&&this.spec.endDrag&&this.spec.endDrag(this.props,this.monitor,At(this.ref))}}])&&Ur(t.prototype,r),e}();function Lr(e){return Object.keys(e).forEach((function(t){c($r.indexOf(t)>-1,'Expected the drag source specification to only have some of the following keys: %s. Instead received a specification with an unexpected "%s" key. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',$r.join(", "),t),c("function"==typeof e[t],"Expected %s in the drag source specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source",t,t,e[t])})),Fr.forEach((function(t){c("function"==typeof e[t],"Expected %s in the drag source specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source",t,t,e[t])})),function(t,r){return new Br(e,t,r)}}function Gr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function qr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Wr=["canDrop","hover","drop"],Vr=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),qr(this,"props",null),qr(this,"spec",void 0),qr(this,"monitor",void 0),qr(this,"ref",void 0),this.spec=t,this.monitor=r,this.ref=n}var t,r;return t=e,(r=[{key:"receiveProps",value:function(e){this.props=e}},{key:"receiveMonitor",value:function(e){this.monitor=e}},{key:"canDrop",value:function(){return!this.spec.canDrop||this.spec.canDrop(this.props,this.monitor)}},{key:"hover",value:function(){this.spec.hover&&this.props&&this.spec.hover(this.props,this.monitor,At(this.ref))}},{key:"drop",value:function(){if(this.spec.drop)return this.spec.drop(this.props,this.monitor,this.ref.current)}}])&&Gr(t.prototype,r),e}();function Yr(e){return Object.keys(e).forEach((function(t){c(Wr.indexOf(t)>-1,'Expected the drop target specification to only have some of the following keys: %s. Instead received a specification with an unexpected "%s" key. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',Wr.join(", "),t),c("function"==typeof e[t],"Expected %s in the drop target specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target",t,t,e[t])})),function(t,r){return new Vr(e,t,r)}}function zr(e){return(zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Kr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Jr(e,t,r){return t&&Xr(e.prototype,t),r&&Xr(e,r),e}function Qr(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Zr(e,t)}function Zr(e,t){return(Zr=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function en(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,n=nn(e);if(t){var o=nn(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return tn(this,r)}}function tn(e,t){if(t&&("object"===zr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return rn(e)}function rn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nn(e){return(nn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function on(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var an="undefined"!=typeof window?t.useLayoutEffect:t.useEffect;function un(e){return(un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function cn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function sn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ln=function(){function e(t,r,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),sn(this,"spec",void 0),sn(this,"monitor",void 0),sn(this,"connector",void 0),this.spec=t,this.monitor=r,this.connector=n}var t,r;return t=e,(r=[{key:"beginDrag",value:function(){var e,t=this.spec,r=this.monitor;return null!==(e="object"===un(t.item)?t.item:"function"==typeof t.item?t.item(r):{})&&void 0!==e?e:null}},{key:"canDrag",value:function(){var e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}},{key:"isDragging",value:function(e,t){var r=this.spec,n=this.monitor,o=r.isDragging;return o?o(n):t===e.getSourceId()}},{key:"endDrag",value:function(){var e=this.spec,t=this.monitor,r=this.connector,n=e.end;n&&n(t.getItem(),t),r.reconnect()}}])&&cn(t.prototype,r),e}();function fn(){var e=t.useContext(n).dragDropManager;return c(null!=e,"Expected drag drop context"),e}function dn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pn(e,r,n){var o=fn(),i=function(e,r,n){var o=t.useMemo((function(){return new ln(e,r,n)}),[r,n]);return t.useEffect((function(){o.spec=e}),[e]),o}(e,r,n),a=function(e){return t.useMemo((function(){var t=e.type;return c(null!=t,"spec.type must be defined"),t}),[e])}(e);an((function(){if(null!=a){var e=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return dn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?dn(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(Mt(a,i,o),2),t=e[0],u=e[1];return r.receiveHandlerId(t),n.receiveHandlerId(t),u}}),[o,r,n,i,a])}function hn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function gn(e,r){var n=function(e){return function(e){if(Array.isArray(e))return hn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return hn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?hn(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(r||[]);return null==r&&"function"!=typeof e&&n.push(e),t.useMemo((function(){return"function"==typeof e?e():e}),n)}var yn=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,o,i;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,i[o]))return!1;for(o=n;0!=o--;){var a=i[o];if(!e(t[a],r[a]))return!1}return!0}return t!=t&&r!=r};function vn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function bn(e,r,n){var o=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return vn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?vn(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t.useState((function(){return r(e)})),2),i=o[0],a=o[1],u=t.useCallback((function(){var t=r(e);yn(i,t)||(a(t),n&&n())}),[i,e,n]);return an(u),[i,u]}function mn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function On(e,t,r){return function(e,t,r){var n=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return mn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?mn(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(bn(e,t,r),2),o=n[0],i=n[1];return an((function(){var t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(i,{handlerIds:[t]})}),[e,i]),o}(t,e||function(){return{}},(function(){return r.reconnect()}))}function Sn(e){return t.useMemo((function(){return e.hooks.dragSource()}),[e])}function wn(e){return t.useMemo((function(){return e.hooks.dragPreview()}),[e])}function Dn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function In(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Cn=function(){function e(t,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),In(this,"spec",void 0),In(this,"monitor",void 0),this.spec=t,this.monitor=r}var t,r;return t=e,(r=[{key:"canDrop",value:function(){var e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}},{key:"hover",value:function(){var e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}},{key:"drop",value:function(){var e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}}])&&Dn(t.prototype,r),e}();function kn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Tn(e,r,n){var o=fn(),i=function(e,r){var n=t.useMemo((function(){return new Cn(e,r)}),[r]);return t.useEffect((function(){n.spec=e}),[e]),n}(e,r),a=function(e){var r=e.accept;return t.useMemo((function(){return c(null!=e.accept,"accept must be defined"),Array.isArray(r)?r:[r]}),[r])}(e);an((function(){var e=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return kn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?kn(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(Rt(a,i,o),2),t=e[0],u=e[1];return r.receiveHandlerId(t),n.receiveHandlerId(t),u}),[o,r,i,n,a.map((function(e){return e.toString()})).join("|")])}function jn(e){return t.useMemo((function(){return e.hooks.dropTarget()}),[e])}function Pn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}e.DndContext=n,e.DndProvider=at,e.DragLayer=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c("function"==typeof e,'Expected "collect" provided as the first argument to DragLayer to be a function that collects props to inject into the component. ',"Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-layer",e),c(Ht(r),'Expected "options" provided as the second argument to DragLayer to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-layer',r),function(o){var i=o,a=r.arePropsEqual,s=void 0===a?It:a,l=i.displayName||i.name||"Component",f=function(r){Qr(a,r);var o=en(a);function a(){var e;Kr(this,a);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return on(rn(e=o.call.apply(o,[this].concat(n))),"manager",void 0),on(rn(e),"isCurrentlyMounted",!1),on(rn(e),"unsubscribeFromOffsetChange",void 0),on(rn(e),"unsubscribeFromStateChange",void 0),on(rn(e),"ref",t.createRef()),on(rn(e),"handleChange",(function(){if(e.isCurrentlyMounted){var t=e.getCurrentState();It(t,e.state)||e.setState(t)}})),e}return Jr(a,[{key:"getDecoratedComponentInstance",value:function(){return c(this.ref.current,"In order to access an instance of the decorated component, it must either be a class component or use React.forwardRef()"),this.ref.current}},{key:"shouldComponentUpdate",value:function(e,t){return!s(e,this.props)||!It(t,this.state)}},{key:"componentDidMount",value:function(){this.isCurrentlyMounted=!0,this.handleChange()}},{key:"componentWillUnmount",value:function(){this.isCurrentlyMounted=!1,this.unsubscribeFromOffsetChange&&(this.unsubscribeFromOffsetChange(),this.unsubscribeFromOffsetChange=void 0),this.unsubscribeFromStateChange&&(this.unsubscribeFromStateChange(),this.unsubscribeFromStateChange=void 0)}},{key:"render",value:function(){var e=this;return u.jsx(n.Consumer,{children:function(t){var r=t.dragDropManager;return void 0===r?null:(e.receiveDragDropManager(r),e.isCurrentlyMounted?u.jsx(i,Object.assign({},e.props,e.state,{ref:_t(i)?e.ref:null}),void 0):null)}},void 0)}},{key:"receiveDragDropManager",value:function(e){if(void 0===this.manager){this.manager=e,c("object"===zr(e),"Could not find the drag and drop manager in the context of %s. Make sure to render a DndProvider component in your top-level component. Read more: http://react-dnd.github.io/react-dnd/docs/troubleshooting#could-not-find-the-drag-and-drop-manager-in-the-context",l,l);var t=this.manager.getMonitor();this.unsubscribeFromOffsetChange=t.subscribeToOffsetChange(this.handleChange),this.unsubscribeFromStateChange=t.subscribeToStateChange(this.handleChange)}}},{key:"getCurrentState",value:function(){if(!this.manager)return{};var t=this.manager.getMonitor();return e(t,this.props)}}]),a}(t.Component);return on(f,"displayName","DragLayer(".concat(l,")")),on(f,"DecoratedComponent",o),Tr(f,o)}},e.DragPreviewImage=ct,e.DragSource=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=e;"function"!=typeof e&&(c($t(e),'Expected "type" provided as the first argument to DragSource to be a string, or a function that returns a string given the current props. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',e),o=function(){return e}),c(Ht(t),'Expected "spec" provided as the second argument to DragSource to be a plain object. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',t);var i=Lr(t);return c("function"==typeof r,'Expected "collect" provided as the third argument to DragSource to be a function that returns a plain object of props to inject. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',r),c(Ht(n),'Expected "options" provided as the fourth argument to DragSource to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source',r),function(e){return Nr({containerDisplayName:"DragSource",createHandler:i,registerHandler:Mt,createConnector:function(e){return new Tt(e)},createMonitor:function(e){return new pt(e)},DecoratedComponent:e,getType:o,collect:r,options:n})}},e.DropTarget=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=e;"function"!=typeof e&&(c($t(e,!0),'Expected "type" provided as the first argument to DropTarget to be a string, an array of strings, or a function that returns either given the current props. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',e),o=function(){return e}),c(Ht(t),'Expected "spec" provided as the second argument to DropTarget to be a plain object. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',t);var i=Yr(t);return c("function"==typeof r,'Expected "collect" provided as the third argument to DropTarget to be a function that returns a plain object of props to inject. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',r),c(Ht(n),'Expected "options" provided as the fourth argument to DropTarget to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target',r),function(e){return Nr({containerDisplayName:"DropTarget",createHandler:i,registerHandler:Rt,createMonitor:function(e){return new vt(e)},createConnector:function(e){return new Et(e)},DecoratedComponent:e,getType:o,collect:r,options:n})}},e.useDrag=function(e,r){var n=gn(e,r);c(!n.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");var o,i=(o=fn(),t.useMemo((function(){return new pt(o)}),[o])),a=function(e,r){var n=fn(),o=t.useMemo((function(){return new Tt(n.getBackend())}),[n]);return an((function(){return o.dragSourceOptions=e||null,o.reconnect(),function(){return o.disconnectDragSource()}}),[o,e]),an((function(){return o.dragPreviewOptions=r||null,o.reconnect(),function(){return o.disconnectDragPreview()}}),[o,r]),o}(n.options,n.previewOptions);return pn(n,i,a),[On(n.collect,i,a),Sn(a),wn(a)]},e.useDragDropManager=fn,e.useDragLayer=function(e){var r=fn().getMonitor(),n=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,u=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(u)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Pn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Pn(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(bn(r,e),2),o=n[0],i=n[1];return t.useEffect((function(){return r.subscribeToOffsetChange(i)})),t.useEffect((function(){return r.subscribeToStateChange(i)})),o},e.useDrop=function(e,r){var n,o=gn(e,r),i=(n=fn(),t.useMemo((function(){return new vt(n)}),[n])),a=function(e){var r=fn(),n=t.useMemo((function(){return new Et(r.getBackend())}),[r]);return an((function(){return n.dropTargetOptions=e||null,n.reconnect(),function(){return n.disconnectDropTarget()}}),[e]),n}(o.options);return Tn(o,i,a),[On(o.collect,i,a),jn(a)]},Object.defineProperty(e,"__esModule",{value:!0})}));
