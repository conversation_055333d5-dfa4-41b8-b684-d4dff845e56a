pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Paraiso
  Author: Jan <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme paraiso
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2f1e2e  Default Background
base01  #41323f  Lighter Background (Used for status bars, line number and folding marks)
base02  #4f424c  Selection Background
base03  #776e71  Comments, Invisibles, Line Highlighting
base04  #8d8687  Dark Foreground (Used for status bars)
base05  #a39e9b  Default Foreground, Caret, Delimiters, Operators
base06  #b9b6b0  Light Foreground (Not often used)
base07  #e7e9db  Light Background (Not often used)
base08  #ef6155  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #f99b15  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fec418  Classes, Markup Bold, Search Text Background
base0B  #48b685  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #5bc4bf  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #06b6ef  Functions, Methods, Attribute IDs, Headings
base0E  #815ba4  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e96ba8  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a39e9b;
  background: #2f1e2e
}
.hljs::selection,
.hljs ::selection {
  background-color: #4f424c;
  color: #a39e9b
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #776e71 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #776e71
}
/* base04 - #8d8687 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #8d8687
}
/* base05 - #a39e9b -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a39e9b
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ef6155
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #f99b15
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fec418
}
.hljs-strong {
  font-weight: bold;
  color: #fec418
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #48b685
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #5bc4bf
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #06b6ef
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #815ba4
}
.hljs-emphasis {
  color: #815ba4;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e96ba8
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}