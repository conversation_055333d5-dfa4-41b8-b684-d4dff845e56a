'use client'

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'

interface MarkdownContentProps {
  content: string
  className?: string
}

export function MarkdownContent({ content, className = '' }: MarkdownContentProps) {
  return (
    <div className={`prose prose-invert prose-sm max-w-none ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Custom styling for code blocks
          code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '')
            return !inline && match ? (
              <pre className="bg-[#1e1e1e] border border-[#333] rounded-lg p-4 overflow-x-auto">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className="bg-[#3c3c3c] px-1.5 py-0.5 rounded text-[#f8f8f2] font-mono text-sm" {...props}>
                {children}
              </code>
            )
          },
          // Custom styling for blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-[#0e639c] pl-4 italic text-[#cccccc] bg-[#2d2d30] py-2 rounded-r">
              {children}
            </blockquote>
          ),
          // Custom styling for tables
          table: ({ children }) => (
            <div className="overflow-x-auto">
              <table className="min-w-full border border-[#333] rounded-lg">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-[#333] bg-[#2d2d30] px-3 py-2 text-left font-semibold text-[#cccccc]">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-[#333] px-3 py-2 text-[#cccccc]">
              {children}
            </td>
          ),
          // Custom styling for links
          a: ({ href, children }) => (
            <a
              href={href}
              className="text-[#0e639c] hover:text-[#1177bb] underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {children}
            </a>
          ),
          // Custom styling for headings
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold text-[#cccccc] mb-4 border-b border-[#333] pb-2">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-bold text-[#cccccc] mb-3 border-b border-[#333] pb-1">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-bold text-[#cccccc] mb-2">
              {children}
            </h3>
          ),
          // Custom styling for lists
          ul: ({ children }) => (
            <ul className="list-disc list-inside space-y-1 text-[#cccccc]">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-1 text-[#cccccc]">
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-[#cccccc]">
              {children}
            </li>
          ),
          // Custom styling for paragraphs
          p: ({ children }) => (
            <p className="text-[#cccccc] leading-relaxed mb-3">
              {children}
            </p>
          ),
          // Custom styling for horizontal rules
          hr: () => (
            <hr className="border-[#333] my-4" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
