"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _markdown_content__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./markdown-content */ \"(app-pages-browser)/./components/markdown-content.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Loader2,Send,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { chat, messages, onNewMessage, onNewVersion, initialPrompt } = param;\n    _s();\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [toolResults, setToolResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const { token } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        streamingMessage,\n        toolResults\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided\n        if (initialPrompt && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n            setInput(initialPrompt);\n            setTimeout(()=>{\n                handleSendMessage(initialPrompt);\n            }, 100);\n        }\n    }, [\n        initialPrompt\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async (messageContent)=>{\n        const content = messageContent || input.trim();\n        if (!content || isLoading) return;\n        setInput(\"\");\n        setIsLoading(true);\n        setStreamingMessage(\"\");\n        setToolResults([]);\n        try {\n            var _response_body;\n            // Add user message immediately\n            const userMessage = {\n                id: Date.now(),\n                chat_id: chat.id,\n                content,\n                role: \"user\",\n                created_at: new Date().toISOString()\n            };\n            onNewMessage(userMessage);\n            // Send message to API using fetch for streaming\n            const response = await fetch(\"\".concat(\"http://localhost:8000\", \"/chat/\").concat(chat.id, \"/message\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(token || \"\")\n                },\n                body: JSON.stringify({\n                    content\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to send message\");\n            }\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) {\n                throw new Error(\"No response body\");\n            }\n            const decoder = new TextDecoder();\n            let currentStreamingMessage = \"\";\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value);\n                const lines = chunk.split(\"\\n\");\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        try {\n                            const data = JSON.parse(line.slice(6));\n                            switch(data.type){\n                                case \"message\":\n                                    currentStreamingMessage += data.content || \"\";\n                                    setStreamingMessage(currentStreamingMessage);\n                                    break;\n                                case \"tool_result\":\n                                    setToolResults((prev)=>[\n                                            ...prev,\n                                            \"\".concat(data.tool_name, \": \").concat(data.result)\n                                        ]);\n                                    break;\n                                case \"completion\":\n                                    // Add assistant message\n                                    const assistantMessage = {\n                                        id: Date.now() + 1,\n                                        chat_id: chat.id,\n                                        content: currentStreamingMessage,\n                                        role: \"assistant\",\n                                        created_at: new Date().toISOString()\n                                    };\n                                    onNewMessage(assistantMessage);\n                                    if (data.version_created && data.version_id) {\n                                        // Create version object\n                                        const newVersion = {\n                                            id: data.version_id,\n                                            chat_id: chat.id,\n                                            version_number: data.version_id,\n                                            created_at: new Date().toISOString(),\n                                            files: []\n                                        };\n                                        onNewVersion(newVersion);\n                                        toast({\n                                            title: \"Version Created\",\n                                            description: \"Created version with \".concat(data.files_count, \" files\")\n                                        });\n                                    }\n                                    return; // Exit the function\n                                case \"error\":\n                                    toast({\n                                        title: \"Error\",\n                                        description: data.error || \"Something went wrong\",\n                                        variant: \"destructive\"\n                                    });\n                                    return;\n                                case \"done\":\n                                    return;\n                            }\n                        } catch (e) {\n                        // Ignore JSON parse errors\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to send message\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n            setStreamingMessage(\"\");\n            setToolResults([]);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-[#0d1117]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-[#21262d]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-sm font-bold\",\n                                children: \"AI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-[#f0f6fc] font-semibold\",\n                                    children: chat.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#7d8590] text-sm\",\n                                    children: \"Codora chat interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4 vscode-scrollbar\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3 \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                            children: [\n                                message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] rounded-lg px-4 py-3 \".concat(message.role === \"user\" ? \"bg-[#0e639c] text-white ml-auto\" : \"bg-[#3c3c3c] text-[#cccccc]\"),\n                                    children: message.role === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm leading-relaxed text-white\",\n                                        children: message.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_markdown_content__WEBPACK_IMPORTED_MODULE_5__.MarkdownContent, {\n                                        content: message.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                message.role === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-[#3c3c3c] rounded-full flex items-center justify-center flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 text-[#cccccc]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)),\n                    streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[80%] rounded-lg px-4 py-3 bg-[#3c3c3c]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_markdown_content__WEBPACK_IMPORTED_MODULE_5__.MarkdownContent, {\n                                        content: streamingMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-2 h-4 bg-[#0e639c] ml-1 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    toolResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm text-[#858585] bg-[#2d2d30] rounded px-3 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mono\",\n                                    children: result\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-[#333] p-4 bg-[#2d2d30]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Type your message...\",\n                                className: \"min-h-[60px] max-h-[120px] resize-none bg-[#3c3c3c] border-[#555] text-[#cccccc] placeholder-[#858585] focus:border-[#0e639c] focus:ring-[#0e639c]\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleSendMessage(),\n                            disabled: !input.trim() || isLoading,\n                            className: \"h-[60px] w-[60px] bg-[#0e639c] hover:bg-[#1177bb] disabled:bg-[#555] disabled:cursor-not-allowed rounded-lg flex items-center justify-center transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-5 h-5 animate-spin text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-5 h-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"UE+duxMvjElZsdwpgxpfjp+d+3k=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast,\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-interface.tsx\n"));

/***/ })

});