pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Hardcore
  Author: <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme hardcore
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #212121  Default Background
base01  #303030  Lighter Background (Used for status bars, line number and folding marks)
base02  #353535  Selection Background
base03  #4A4A4A  Comments, Invisibles, Line Highlighting
base04  #707070  Dark Foreground (Used for status bars)
base05  #cdcdcd  Default Foreground, Caret, Delimiters, Operators
base06  #e5e5e5  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #f92672  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fd971f  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #e6db74  Classes, Markup Bold, Search Text Background
base0B  #a6e22e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #708387  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #66d9ef  Functions, Methods, Attribute IDs, Headings
base0E  #9e6ffe  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e8b882  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #cdcdcd;
  background: #212121
}
.hljs::selection,
.hljs ::selection {
  background-color: #353535;
  color: #cdcdcd
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #4A4A4A -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #4A4A4A
}
/* base04 - #707070 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #707070
}
/* base05 - #cdcdcd -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #cdcdcd
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f92672
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fd971f
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #e6db74
}
.hljs-strong {
  font-weight: bold;
  color: #e6db74
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a6e22e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #708387
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #66d9ef
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9e6ffe
}
.hljs-emphasis {
  color: #9e6ffe;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e8b882
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}