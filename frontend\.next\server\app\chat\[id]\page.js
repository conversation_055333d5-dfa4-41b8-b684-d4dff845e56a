/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/[id]/page";
exports.ids = ["app/chat/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/chat/[id]/page.tsx */ \"(rsc)/./app/chat/[id]/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/chat/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/[id]/page\",\n        pathname: \"/chat/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp%5Cchat%5C%5Bid%5D%5Cpage.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp%5Cchat%5C%5Bid%5D%5Cpage.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/chat/[id]/page.tsx */ \"(ssr)/./app/chat/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQVNVUyU1Q0Rlc2t0b3AlNUNDb2RvcmElNUNmcm9udGVuZCU1Q2FwcCU1Q2NoYXQlNUMlNUJpZCU1RCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8/YWFiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcRGVza3RvcFxcXFxDb2RvcmFcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGNoYXRcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp%5Cchat%5C%5Bid%5D%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Ccomponents%5Cui%5Ctoaster.tsx&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Clib%5Cauth-context.tsx&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Chighlight.js%5Cstyles%5Cvs2015.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Ccomponents%5Cui%5Ctoaster.tsx&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Clib%5Cauth-context.tsx&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Chighlight.js%5Cstyles%5Cvs2015.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(ssr)/./lib/auth-context.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQVNVUyU1Q0Rlc2t0b3AlNUNDb2RvcmElNUNmcm9udGVuZCU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQVNVUyU1Q0Rlc2t0b3AlNUNDb2RvcmElNUNmcm9udGVuZCU1Q2NvbXBvbmVudHMlNUN1aSU1Q3RvYXN0ZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQVNVUyU1Q0Rlc2t0b3AlNUNDb2RvcmElNUNmcm9udGVuZCU1Q2xpYiU1Q2F1dGgtY29udGV4dC50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNBU1VTJTVDRGVza3RvcCU1Q0NvZG9yYSU1Q2Zyb250ZW5kJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDQVNVUyU1Q0Rlc2t0b3AlNUNDb2RvcmElNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q2hpZ2hsaWdodC5qcyU1Q3N0eWxlcyU1Q3ZzMjAxNS5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUE0RztBQUM1RyIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8/NWE5YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFTVVNcXFxcRGVza3RvcFxcXFxDb2RvcmFcXFxcZnJvbnRlbmRcXFxcY29tcG9uZW50c1xcXFx1aVxcXFx0b2FzdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQVNVU1xcXFxEZXNrdG9wXFxcXENvZG9yYVxcXFxmcm9udGVuZFxcXFxsaWJcXFxcYXV0aC1jb250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Ccomponents%5Cui%5Ctoaster.tsx&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Clib%5Cauth-context.tsx&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Chighlight.js%5Cstyles%5Cvs2015.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat-interface */ \"(ssr)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_file_explorer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/file-explorer */ \"(ssr)/./components/file-explorer.tsx\");\n/* harmony import */ var _components_website_preview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/website-preview */ \"(ssr)/./components/website-preview.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @monaco-editor/react */ \"(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction ChatPage({ params }) {\n    const [chat, setChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentVersion, setCurrentVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, isLoading: authLoading } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Wait for auth to finish loading\n        if (authLoading) return;\n        if (!user) {\n            router.push(\"/\");\n            return;\n        }\n        loadChatData();\n    }, [\n        user,\n        authLoading,\n        params.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided in URL\n        const prompt = searchParams.get(\"prompt\");\n        if (prompt && chat && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n        // This will be handled by the ChatInterface component\n        }\n    }, [\n        chat,\n        searchParams\n    ]);\n    const loadChatData = async ()=>{\n        try {\n            const chatId = parseInt(params.id);\n            // Load chat details\n            const chatData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChat(chatId);\n            setChat(chatData);\n            // Load chat history\n            const historyData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatHistory(chatId);\n            setMessages(historyData.messages || []);\n            // Load versions\n            const versionsData = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.chatAPI.getChatVersions(chatId);\n            setVersions(versionsData);\n            // Set current version to the latest one\n            if (versionsData.length > 0) {\n                setCurrentVersion(versionsData[versionsData.length - 1]);\n            }\n        } catch (error) {\n            console.error(\"Error loading chat data:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load chat data. Please try refreshing the page.\",\n                variant: \"destructive\"\n            });\n        // Don't redirect on error, just show error state\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleNewMessage = (message)=>{\n        setMessages((prev)=>[\n                ...prev,\n                message\n            ]);\n    };\n    const handleNewVersion = (version)=>{\n        setVersions((prev)=>[\n                ...prev,\n                version\n            ]);\n        setCurrentVersion(version);\n    };\n    const handleVersionChange = (version)=>{\n        setCurrentVersion(version);\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n    };\n    const getFileLanguage = (fileName)=>{\n        const ext = fileName.split(\".\").pop()?.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return \"javascript\";\n            case \"ts\":\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    if (authLoading || !user && !authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590]\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chat && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-[#0d1117] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2 text-[#f0f6fc]\",\n                        children: \"Chat not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#7d8590] mb-4\",\n                        children: \"The chat you're looking for doesn't exist or failed to load.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(\"/dashboard\"),\n                        className: \"bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-[#0d1117] flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col\",\n                children: chat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_6__.ChatInterface, {\n                    chat: chat,\n                    messages: messages,\n                    onNewMessage: handleNewMessage,\n                    onNewVersion: handleNewVersion,\n                    initialPrompt: searchParams.get(\"prompt\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-8 h-8 animate-spin text-[#7d8590] mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#7d8590]\",\n                                children: \"Loading chat...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 bg-[#21262d] rounded-md p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(true),\n                                                className: `flex items-center space-x-1 px-2 py-1 rounded text-xs ${showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPreview(false),\n                                                className: `flex items-center space-x-1 px-2 py-1 rounded text-xs ${!showPreview ? \"bg-[#0969da] text-white\" : \"text-[#7d8590] hover:text-[#f0f6fc]\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: currentVersion?.id?.toString() || \"\",\n                                        onChange: (e)=>{\n                                            const version = versions.find((v)=>v.id.toString() === e.target.value);\n                                            if (version) handleVersionChange(version);\n                                        },\n                                        className: \"bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: version.id.toString(),\n                                                children: [\n                                                    \"v\",\n                                                    version.version_number\n                                                ]\n                                            }, version.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 16 16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 bg-[#0d1117] border-r border-[#21262d]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_explorer__WEBPACK_IMPORTED_MODULE_7__.FileExplorer, {\n                                    version: currentVersion,\n                                    onFileSelect: handleFileSelect\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 bg-[#0d1117]\",\n                                children: showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_preview__WEBPACK_IMPORTED_MODULE_8__.WebsitePreview, {\n                                    version: currentVersion\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-12 bg-[#161b22] border-b border-[#21262d] flex items-center px-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-[#7d8590]\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-[#f0f6fc] text-sm font-medium\",\n                                                        children: selectedFile ? selectedFile.path.split(\"/\").pop() : \"No file selected\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: selectedFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                height: \"100%\",\n                                                language: getFileLanguage(selectedFile.path),\n                                                value: selectedFile.content || \"// No content available\",\n                                                theme: \"vs-dark\",\n                                                options: {\n                                                    readOnly: true,\n                                                    minimap: {\n                                                        enabled: false\n                                                    },\n                                                    scrollBeyondLastLine: false,\n                                                    fontSize: 14,\n                                                    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n                                                    lineNumbers: \"on\",\n                                                    glyphMargin: false,\n                                                    folding: true,\n                                                    lineDecorationsWidth: 0,\n                                                    lineNumbersMinChars: 3,\n                                                    renderLineHighlight: \"line\",\n                                                    selectOnLineNumbers: true,\n                                                    wordWrap: \"on\",\n                                                    automaticLayout: true\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-16 h-16 mx-auto mb-4 text-[#7d8590]\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium text-[#f0f6fc] mb-2\",\n                                                            children: \"Code Editor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-[#7d8590]\",\n                                                            children: \"Select a file from the explorer to view its content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\chat\\\\[id]\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/chat/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/chat-interface.tsx":
/*!***************************************!*\
  !*** ./components/chat-interface.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./lib/auth-context.tsx\");\n/* harmony import */ var _markdown_content__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./markdown-content */ \"(ssr)/./components/markdown-content.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader2,Send,User,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader2,Send,User,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader2,Send,User,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader2,Send,User,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader2,Send,User,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,CheckCircle,Loader2,Send,User,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \n\n\n\n\n\n\n\nfunction ChatInterface({ chat, messages, onNewMessage, onNewVersion, initialPrompt }) {\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [toolResults, setToolResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const { token } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const initialPromptSent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        streamingMessage,\n        toolResults\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Send initial prompt if provided\n        if (initialPrompt && !initialPromptSent.current) {\n            initialPromptSent.current = true;\n            setInput(initialPrompt);\n            setTimeout(()=>{\n                handleSendMessage(initialPrompt);\n            }, 100);\n        }\n    }, [\n        initialPrompt\n    ]);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async (messageContent)=>{\n        const content = messageContent || input.trim();\n        if (!content || isLoading) return;\n        setInput(\"\");\n        setIsLoading(true);\n        setStreamingMessage(\"\");\n        setToolResults([]);\n        try {\n            // Add user message immediately\n            const userMessage = {\n                id: Date.now(),\n                chat_id: chat.id,\n                content,\n                role: \"user\",\n                created_at: new Date().toISOString()\n            };\n            onNewMessage(userMessage);\n            // Send message to API using fetch for streaming\n            const response = await fetch(`${\"http://localhost:8000\"}/chat/${chat.id}/message`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token || \"\"}`\n                },\n                body: JSON.stringify({\n                    content\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to send message\");\n            }\n            const reader = response.body?.getReader();\n            if (!reader) {\n                throw new Error(\"No response body\");\n            }\n            const decoder = new TextDecoder();\n            let currentStreamingMessage = \"\";\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value);\n                const lines = chunk.split(\"\\n\");\n                for (const line of lines){\n                    if (line.startsWith(\"data: \")) {\n                        try {\n                            const data = JSON.parse(line.slice(6));\n                            switch(data.type){\n                                case \"message\":\n                                    currentStreamingMessage += data.content || \"\";\n                                    setStreamingMessage(currentStreamingMessage);\n                                    break;\n                                case \"tool_result\":\n                                    setToolResults((prev)=>[\n                                            ...prev,\n                                            `${data.tool_name}: ${data.result}`\n                                        ]);\n                                    break;\n                                case \"completion\":\n                                    // Add assistant message\n                                    const assistantMessage = {\n                                        id: Date.now() + 1,\n                                        chat_id: chat.id,\n                                        content: currentStreamingMessage,\n                                        role: \"assistant\",\n                                        created_at: new Date().toISOString()\n                                    };\n                                    onNewMessage(assistantMessage);\n                                    if (data.version_created && data.version_id) {\n                                        // Create version object\n                                        const newVersion = {\n                                            id: data.version_id,\n                                            chat_id: chat.id,\n                                            version_number: data.version_id,\n                                            created_at: new Date().toISOString(),\n                                            files: []\n                                        };\n                                        onNewVersion(newVersion);\n                                        toast({\n                                            title: \"Version Created\",\n                                            description: `Created version with ${data.files_count} files`\n                                        });\n                                    }\n                                    return; // Exit the function\n                                case \"error\":\n                                    toast({\n                                        title: \"Error\",\n                                        description: data.error || \"Something went wrong\",\n                                        variant: \"destructive\"\n                                    });\n                                    return;\n                                case \"done\":\n                                    return;\n                            }\n                        } catch (e) {\n                        // Ignore JSON parse errors\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to send message\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n            setStreamingMessage(\"\");\n            setToolResults([]);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-[#0d1117]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-[#21262d]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-sm font-bold\",\n                                children: \"AI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-[#f0f6fc] font-semibold\",\n                                    children: chat.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#7d8590] text-sm\",\n                                    children: \"Codora chat interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: message.role === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-[#21262d] rounded-full flex items-center justify-center flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-3 h-3 text-[#7d8590]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-[#f0f6fc] text-sm leading-relaxed\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-3 h-3 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#f0f6fc] text-sm font-medium\",\n                                                children: \"AI Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-8 text-[#e6edf3] text-sm leading-relaxed\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_markdown_content__WEBPACK_IMPORTED_MODULE_6__.MarkdownContent, {\n                                            content: message.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        }, message.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)),\n                    streamingMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-3 h-3 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#f0f6fc] text-sm font-medium\",\n                                        children: \"AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-8 text-[#e6edf3] text-sm leading-relaxed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_markdown_content__WEBPACK_IMPORTED_MODULE_6__.MarkdownContent, {\n                                        content: streamingMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block w-2 h-4 bg-[#0969da] ml-1 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this),\n                    toolResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-8 p-3 bg-[#161b22] border border-[#30363d] rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-[#7d8590]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-[#7d8590]\",\n                                            children: \"Tool executed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 text-[#3fb950]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-[#7d8590] font-mono bg-[#0d1117] p-2 rounded border border-[#21262d]\",\n                                    children: result\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-[#21262d] p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                    value: input,\n                                    onChange: (e)=>setInput(e.target.value),\n                                    onKeyPress: handleKeyPress,\n                                    placeholder: \"Ask a follow up...\",\n                                    className: \"w-full bg-[#0d1117] border border-[#30363d] text-[#f0f6fc] placeholder-[#7d8590] resize-none min-h-[80px] max-h-[200px] pr-12 rounded-lg focus:border-[#0969da] focus:ring-1 focus:ring-[#0969da]\",\n                                    disabled: isLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>handleSendMessage(),\n                                    disabled: isLoading || !input.trim(),\n                                    className: \"absolute bottom-3 right-3 bg-[#0969da] hover:bg-[#0860ca] text-white p-2 h-8 w-8 rounded-md\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm text-[#7d8590]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_CheckCircle_Loader2_Send_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"AI may make mistakes. Please use with discretion.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-interface.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./components/custom-file-tree.tsx":
/*!*****************************************!*\
  !*** ./components/custom-file-tree.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomFileTree: () => (/* binding */ CustomFileTree)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_arborist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-arborist */ \"(ssr)/./node_modules/react-arborist/dist/module/components/tree.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* __next_internal_client_entry_do_not_use__ CustomFileTree auto */ \n\n\n\nfunction Node({ node, style, dragHandle }) {\n    const getFileIcon = (fileName)=>{\n        const ext = fileName.split(\".\").pop()?.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#f7df1e]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case \"ts\":\n            case \"tsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#3178c6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case \"html\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#e34c26]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            case \"css\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#1572b6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n            case \"json\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-[#cbcb41]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case \"md\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-[#083fa1]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n            case \"gif\":\n            case \"svg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-[#4caf50]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-[#858585]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        ref: dragHandle,\n        className: `flex items-center py-1 px-2 cursor-pointer hover:bg-[#161b22] transition-colors text-sm ${node.isSelected ? \"bg-[#21262d]\" : \"\"}`,\n        onClick: ()=>node.isInternal ? node.toggle() : node.select(),\n        children: [\n            node.isInternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 flex items-center justify-center mr-1\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-3 h-3 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-3 h-3 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: getFileIcon(node.data.name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-[#f0f6fc] truncate text-sm\",\n                children: node.data.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\nfunction CustomFileTree({ data, onFileSelect, selectedPath }) {\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [dimensions, setDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 300,\n        height: 400\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateDimensions = ()=>{\n            if (containerRef.current) {\n                const { width, height } = containerRef.current.getBoundingClientRect();\n                setDimensions({\n                    width: Math.max(width, 200),\n                    height: Math.max(height, 200)\n                });\n            }\n        };\n        updateDimensions();\n        window.addEventListener(\"resize\", updateDimensions);\n        return ()=>window.removeEventListener(\"resize\", updateDimensions);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full bg-[#0d1117]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_arborist__WEBPACK_IMPORTED_MODULE_11__.Tree, {\n            data: data,\n            openByDefault: false,\n            width: dimensions.width,\n            height: dimensions.height,\n            indent: 16,\n            rowHeight: 28,\n            onSelect: (nodes)=>{\n                const node = nodes[0];\n                if (node && node.data.file && onFileSelect) {\n                    onFileSelect(node.data.file);\n                }\n            },\n            children: Node\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/custom-file-tree.tsx\n");

/***/ }),

/***/ "(ssr)/./components/file-explorer.tsx":
/*!**************************************!*\
  !*** ./components/file-explorer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileExplorer: () => (/* binding */ FileExplorer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* harmony import */ var _custom_file_tree__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./custom-file-tree */ \"(ssr)/./components/custom-file-tree.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Folder,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Folder,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Folder,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ FileExplorer auto */ \n\n\n\n\n\nfunction FileExplorer({ version, onFileSelect }) {\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [treeData, setTreeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (version) {\n            loadFiles();\n        } else {\n            setFiles([]);\n            setTreeData([]);\n            setSelectedFile(null);\n        }\n    }, [\n        version\n    ]);\n    const loadFiles = async ()=>{\n        if (!version) return;\n        setIsLoading(true);\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getVersionPreview(version.chat_id, version.id);\n            setFiles(data.files || []);\n            buildFileTree(data.files || []);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load files\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const buildFileTree = (files)=>{\n        const nodeMap = new Map();\n        const rootNodes = [];\n        // Sort files by path\n        const sortedFiles = files.sort((a, b)=>a.path.localeCompare(b.path));\n        for (const file of sortedFiles){\n            const pathParts = file.path.split(\"/\").filter(Boolean);\n            let currentPath = \"\";\n            for(let i = 0; i < pathParts.length; i++){\n                const part = pathParts[i];\n                const parentPath = currentPath;\n                currentPath = currentPath ? `${currentPath}/${part}` : part;\n                const isFile = i === pathParts.length - 1;\n                if (!nodeMap.has(currentPath)) {\n                    const newNode = {\n                        id: currentPath,\n                        name: part,\n                        type: isFile ? \"file\" : \"folder\",\n                        path: currentPath,\n                        children: isFile ? undefined : [],\n                        file: isFile ? file : undefined\n                    };\n                    if (parentPath === \"\") {\n                        // Root level\n                        rootNodes.push(newNode);\n                    } else {\n                        const parentNode = nodeMap.get(parentPath);\n                        if (parentNode && parentNode.children) {\n                            parentNode.children.push(newNode);\n                        }\n                    }\n                    nodeMap.set(currentPath, newNode);\n                }\n            }\n        }\n        setTreeData(rootNodes);\n    };\n    const getFileLanguage = (fileName)=>{\n        const ext = fileName.split(\".\").pop()?.toLowerCase();\n        switch(ext){\n            case \"js\":\n                return \"javascript\";\n            case \"ts\":\n                return \"typescript\";\n            case \"jsx\":\n                return \"javascript\";\n            case \"tsx\":\n                return \"typescript\";\n            case \"html\":\n                return \"html\";\n            case \"css\":\n                return \"css\";\n            case \"json\":\n                return \"json\";\n            case \"md\":\n                return \"markdown\";\n            case \"py\":\n                return \"python\";\n            case \"java\":\n                return \"java\";\n            case \"cpp\":\n            case \"c\":\n                return \"cpp\";\n            case \"php\":\n                return \"php\";\n            case \"sql\":\n                return \"sql\";\n            case \"xml\":\n                return \"xml\";\n            case \"yaml\":\n            case \"yml\":\n                return \"yaml\";\n            default:\n                return \"plaintext\";\n        }\n    };\n    const handleFileSelect = (file)=>{\n        setSelectedFile(file);\n        if (onFileSelect) {\n            onFileSelect(file);\n        }\n    };\n    if (!version) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center text-muted-foreground\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No version selected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-[#0d1117]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 border-b border-[#21262d]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-medium text-sm text-[#f0f6fc] flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"app\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#7d8590] hover:text-[#f0f6fc] rounded\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 16 16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-2\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-6 h-6 animate-spin text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, this) : !treeData || treeData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-[#7d8590]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Folder_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"No files yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_custom_file_tree__WEBPACK_IMPORTED_MODULE_4__.CustomFileTree, {\n                        data: treeData,\n                        onFileSelect: handleFileSelect,\n                        selectedPath: selectedFile?.path\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\file-explorer.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/file-explorer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/markdown-content.tsx":
/*!*****************************************!*\
  !*** ./components/markdown-content.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownContent: () => (/* binding */ MarkdownContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rehype-highlight */ \"(ssr)/./node_modules/rehype-highlight/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ MarkdownContent auto */ \n\n\n\nfunction MarkdownContent({ content, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `prose prose-invert prose-sm max-w-none ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_1__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_highlight__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            ],\n            components: {\n                // Custom styling for code blocks\n                code: ({ node, inline, className, children, ...props })=>{\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-[#1e1e1e] border border-[#333] rounded-lg p-4 overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 15\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-[#3c3c3c] px-1.5 py-0.5 rounded text-[#f8f8f2] font-mono text-sm\",\n                        ...props,\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Custom styling for blockquotes\n                blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-[#0e639c] pl-4 italic text-[#cccccc] bg-[#2d2d30] py-2 rounded-r\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, void 0),\n                // Custom styling for tables\n                table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border border-[#333] rounded-lg\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0),\n                th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"border border-[#333] bg-[#2d2d30] px-3 py-2 text-left font-semibold text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, void 0),\n                td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"border border-[#333] px-3 py-2 text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, void 0),\n                // Custom styling for links\n                a: ({ href, children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        className: \"text-[#0e639c] hover:text-[#1177bb] underline\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, void 0),\n                // Custom styling for headings\n                h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-[#cccccc] mb-4 border-b border-[#333] pb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, void 0),\n                h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-[#cccccc] mb-3 border-b border-[#333] pb-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0),\n                h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-[#cccccc] mb-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 13\n                    }, void 0),\n                // Custom styling for lists\n                ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-1 text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, void 0),\n                ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside space-y-1 text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, void 0),\n                li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-[#cccccc]\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, void 0),\n                // Custom styling for paragraphs\n                p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[#cccccc] leading-relaxed mb-3\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, void 0),\n                // Custom styling for horizontal rules\n                hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-[#333] my-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, void 0)\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\markdown-content.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/markdown-content.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUMvQixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hCLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCx3U0FDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL2NvbXBvbmVudHMvdWkvdGV4dGFyZWEudHN4P2I4MDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIFRleHRhcmVhUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+IHt9XG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MVGV4dEFyZWFFbGVtZW50LCBUZXh0YXJlYVByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IG1pbi1oLVs4MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSBcIlRleHRhcmVhXCJcblxuZXhwb3J0IHsgVGV4dGFyZWEgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3VzZS10b2FzdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs0RUFFOEI7QUFPOUIsTUFBTUMsY0FBYztBQUNwQixNQUFNQyxxQkFBcUI7QUFTM0IsTUFBTUMsY0FBYztJQUNsQkMsV0FBVztJQUNYQyxjQUFjO0lBQ2RDLGVBQWU7SUFDZkMsY0FBYztBQUNoQjtBQUVBLElBQUlDLFFBQVE7QUFFWixTQUFTQztJQUNQRCxRQUFRLENBQUNBLFFBQVEsS0FBS0UsT0FBT0MsZ0JBQWdCO0lBQzdDLE9BQU9ILE1BQU1JLFFBQVE7QUFDdkI7QUEwQkEsTUFBTUMsZ0JBQWdCLElBQUlDO0FBRTFCLE1BQU1DLG1CQUFtQixDQUFDQztJQUN4QixJQUFJSCxjQUFjSSxHQUFHLENBQUNELFVBQVU7UUFDOUI7SUFDRjtJQUVBLE1BQU1FLFVBQVVDLFdBQVc7UUFDekJOLGNBQWNPLE1BQU0sQ0FBQ0o7UUFDckJLLFNBQVM7WUFDUEMsTUFBTTtZQUNOTixTQUFTQTtRQUNYO0lBQ0YsR0FBR2Q7SUFFSFcsY0FBY1UsR0FBRyxDQUFDUCxTQUFTRTtBQUM3QjtBQUVPLE1BQU1NLFVBQVUsQ0FBQ0MsT0FBY0M7SUFDcEMsT0FBUUEsT0FBT0osSUFBSTtRQUNqQixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHRyxLQUFLO2dCQUNSRSxRQUFRO29CQUFDRCxPQUFPRSxLQUFLO3VCQUFLSCxNQUFNRSxNQUFNO2lCQUFDLENBQUNFLEtBQUssQ0FBQyxHQUFHNUI7WUFDbkQ7UUFFRixLQUFLO1lBQ0gsT0FBTztnQkFDTCxHQUFHd0IsS0FBSztnQkFDUkUsUUFBUUYsTUFBTUUsTUFBTSxDQUFDRyxHQUFHLENBQUMsQ0FBQ0MsSUFDeEJBLEVBQUVDLEVBQUUsS0FBS04sT0FBT0UsS0FBSyxDQUFDSSxFQUFFLEdBQUc7d0JBQUUsR0FBR0QsQ0FBQzt3QkFBRSxHQUFHTCxPQUFPRSxLQUFLO29CQUFDLElBQUlHO1lBRTNEO1FBRUYsS0FBSztZQUFpQjtnQkFDcEIsTUFBTSxFQUFFZixPQUFPLEVBQUUsR0FBR1U7Z0JBRXBCLElBQUlWLFNBQVM7b0JBQ1hELGlCQUFpQkM7Z0JBQ25CLE9BQU87b0JBQ0xTLE1BQU1FLE1BQU0sQ0FBQ00sT0FBTyxDQUFDLENBQUNMO3dCQUNwQmIsaUJBQWlCYSxNQUFNSSxFQUFFO29CQUMzQjtnQkFDRjtnQkFFQSxPQUFPO29CQUNMLEdBQUdQLEtBQUs7b0JBQ1JFLFFBQVFGLE1BQU1FLE1BQU0sQ0FBQ0csR0FBRyxDQUFDLENBQUNDLElBQ3hCQSxFQUFFQyxFQUFFLEtBQUtoQixXQUFXQSxZQUFZa0IsWUFDNUI7NEJBQ0UsR0FBR0gsQ0FBQzs0QkFDSkksTUFBTTt3QkFDUixJQUNBSjtnQkFFUjtZQUNGO1FBQ0EsS0FBSztZQUNILElBQUlMLE9BQU9WLE9BQU8sS0FBS2tCLFdBQVc7Z0JBQ2hDLE9BQU87b0JBQ0wsR0FBR1QsS0FBSztvQkFDUkUsUUFBUSxFQUFFO2dCQUNaO1lBQ0Y7WUFDQSxPQUFPO2dCQUNMLEdBQUdGLEtBQUs7Z0JBQ1JFLFFBQVFGLE1BQU1FLE1BQU0sQ0FBQ1MsTUFBTSxDQUFDLENBQUNMLElBQU1BLEVBQUVDLEVBQUUsS0FBS04sT0FBT1YsT0FBTztZQUM1RDtJQUNKO0FBQ0YsRUFBQztBQUVELE1BQU1xQixZQUEyQyxFQUFFO0FBRW5ELElBQUlDLGNBQXFCO0lBQUVYLFFBQVEsRUFBRTtBQUFDO0FBRXRDLFNBQVNOLFNBQVNLLE1BQWM7SUFDOUJZLGNBQWNkLFFBQVFjLGFBQWFaO0lBQ25DVyxVQUFVSixPQUFPLENBQUMsQ0FBQ007UUFDakJBLFNBQVNEO0lBQ1g7QUFDRjtBQUlBLFNBQVNWLE1BQU0sRUFBRSxHQUFHWSxPQUFjO0lBQ2hDLE1BQU1SLEtBQUt2QjtJQUVYLE1BQU1nQyxTQUFTLENBQUNELFFBQ2RuQixTQUFTO1lBQ1BDLE1BQU07WUFDTk0sT0FBTztnQkFBRSxHQUFHWSxLQUFLO2dCQUFFUjtZQUFHO1FBQ3hCO0lBQ0YsTUFBTVUsVUFBVSxJQUFNckIsU0FBUztZQUFFQyxNQUFNO1lBQWlCTixTQUFTZ0I7UUFBRztJQUVwRVgsU0FBUztRQUNQQyxNQUFNO1FBQ05NLE9BQU87WUFDTCxHQUFHWSxLQUFLO1lBQ1JSO1lBQ0FHLE1BQU07WUFDTlEsY0FBYyxDQUFDUjtnQkFDYixJQUFJLENBQUNBLE1BQU1PO1lBQ2I7UUFDRjtJQUNGO0lBRUEsT0FBTztRQUNMVixJQUFJQTtRQUNKVTtRQUNBRDtJQUNGO0FBQ0Y7QUFFQSxTQUFTRztJQUNQLE1BQU0sQ0FBQ25CLE9BQU9vQixTQUFTLEdBQUc3QywyQ0FBYyxDQUFRc0M7SUFFaER0Qyw0Q0FBZSxDQUFDO1FBQ2RxQyxVQUFVVyxJQUFJLENBQUNIO1FBQ2YsT0FBTztZQUNMLE1BQU1JLFFBQVFaLFVBQVVhLE9BQU8sQ0FBQ0w7WUFDaEMsSUFBSUksUUFBUSxDQUFDLEdBQUc7Z0JBQ2RaLFVBQVVjLE1BQU0sQ0FBQ0YsT0FBTztZQUMxQjtRQUNGO0lBQ0YsR0FBRztRQUFDeEI7S0FBTTtJQUVWLE9BQU87UUFDTCxHQUFHQSxLQUFLO1FBQ1JHO1FBQ0FjLFNBQVMsQ0FBQzFCLFVBQXFCSyxTQUFTO2dCQUFFQyxNQUFNO2dCQUFpQk47WUFBUTtJQUMzRTtBQUNGO0FBRTBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vY29tcG9uZW50cy91aS91c2UtdG9hc3QudHM/ODA1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgdHlwZSB7XG4gIFRvYXN0QWN0aW9uRWxlbWVudCxcbiAgVG9hc3RQcm9wcyxcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdFwiXG5cbmNvbnN0IFRPQVNUX0xJTUlUID0gMVxuY29uc3QgVE9BU1RfUkVNT1ZFX0RFTEFZID0gMTAwMDAwMFxuXG50eXBlIFRvYXN0ZXJUb2FzdCA9IFRvYXN0UHJvcHMgJiB7XG4gIGlkOiBzdHJpbmdcbiAgdGl0bGU/OiBSZWFjdC5SZWFjdE5vZGVcbiAgZGVzY3JpcHRpb24/OiBSZWFjdC5SZWFjdE5vZGVcbiAgYWN0aW9uPzogVG9hc3RBY3Rpb25FbGVtZW50XG59XG5cbmNvbnN0IGFjdGlvblR5cGVzID0ge1xuICBBRERfVE9BU1Q6IFwiQUREX1RPQVNUXCIsXG4gIFVQREFURV9UT0FTVDogXCJVUERBVEVfVE9BU1RcIixcbiAgRElTTUlTU19UT0FTVDogXCJESVNNSVNTX1RPQVNUXCIsXG4gIFJFTU9WRV9UT0FTVDogXCJSRU1PVkVfVE9BU1RcIixcbn0gYXMgY29uc3RcblxubGV0IGNvdW50ID0gMFxuXG5mdW5jdGlvbiBnZW5JZCgpIHtcbiAgY291bnQgPSAoY291bnQgKyAxKSAlIE51bWJlci5NQVhfU0FGRV9JTlRFR0VSXG4gIHJldHVybiBjb3VudC50b1N0cmluZygpXG59XG5cbnR5cGUgQWN0aW9uVHlwZSA9IHR5cGVvZiBhY3Rpb25UeXBlc1xuXG50eXBlIEFjdGlvbiA9XG4gIHwge1xuICAgICAgdHlwZTogQWN0aW9uVHlwZVtcIkFERF9UT0FTVFwiXVxuICAgICAgdG9hc3Q6IFRvYXN0ZXJUb2FzdFxuICAgIH1cbiAgfCB7XG4gICAgICB0eXBlOiBBY3Rpb25UeXBlW1wiVVBEQVRFX1RPQVNUXCJdXG4gICAgICB0b2FzdDogUGFydGlhbDxUb2FzdGVyVG9hc3Q+XG4gICAgfVxuICB8IHtcbiAgICAgIHR5cGU6IEFjdGlvblR5cGVbXCJESVNNSVNTX1RPQVNUXCJdXG4gICAgICB0b2FzdElkPzogVG9hc3RlclRvYXN0W1wiaWRcIl1cbiAgICB9XG4gIHwge1xuICAgICAgdHlwZTogQWN0aW9uVHlwZVtcIlJFTU9WRV9UT0FTVFwiXVxuICAgICAgdG9hc3RJZD86IFRvYXN0ZXJUb2FzdFtcImlkXCJdXG4gICAgfVxuXG5pbnRlcmZhY2UgU3RhdGUge1xuICB0b2FzdHM6IFRvYXN0ZXJUb2FzdFtdXG59XG5cbmNvbnN0IHRvYXN0VGltZW91dHMgPSBuZXcgTWFwPHN0cmluZywgUmV0dXJuVHlwZTx0eXBlb2Ygc2V0VGltZW91dD4+KClcblxuY29uc3QgYWRkVG9SZW1vdmVRdWV1ZSA9ICh0b2FzdElkOiBzdHJpbmcpID0+IHtcbiAgaWYgKHRvYXN0VGltZW91dHMuaGFzKHRvYXN0SWQpKSB7XG4gICAgcmV0dXJuXG4gIH1cblxuICBjb25zdCB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgdG9hc3RUaW1lb3V0cy5kZWxldGUodG9hc3RJZClcbiAgICBkaXNwYXRjaCh7XG4gICAgICB0eXBlOiBcIlJFTU9WRV9UT0FTVFwiLFxuICAgICAgdG9hc3RJZDogdG9hc3RJZCxcbiAgICB9KVxuICB9LCBUT0FTVF9SRU1PVkVfREVMQVkpXG5cbiAgdG9hc3RUaW1lb3V0cy5zZXQodG9hc3RJZCwgdGltZW91dClcbn1cblxuZXhwb3J0IGNvbnN0IHJlZHVjZXIgPSAoc3RhdGU6IFN0YXRlLCBhY3Rpb246IEFjdGlvbik6IFN0YXRlID0+IHtcbiAgc3dpdGNoIChhY3Rpb24udHlwZSkge1xuICAgIGNhc2UgXCJBRERfVE9BU1RcIjpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICB0b2FzdHM6IFthY3Rpb24udG9hc3QsIC4uLnN0YXRlLnRvYXN0c10uc2xpY2UoMCwgVE9BU1RfTElNSVQpLFxuICAgICAgfVxuXG4gICAgY2FzZSBcIlVQREFURV9UT0FTVFwiOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHRvYXN0czogc3RhdGUudG9hc3RzLm1hcCgodCkgPT5cbiAgICAgICAgICB0LmlkID09PSBhY3Rpb24udG9hc3QuaWQgPyB7IC4uLnQsIC4uLmFjdGlvbi50b2FzdCB9IDogdFxuICAgICAgICApLFxuICAgICAgfVxuXG4gICAgY2FzZSBcIkRJU01JU1NfVE9BU1RcIjoge1xuICAgICAgY29uc3QgeyB0b2FzdElkIH0gPSBhY3Rpb25cblxuICAgICAgaWYgKHRvYXN0SWQpIHtcbiAgICAgICAgYWRkVG9SZW1vdmVRdWV1ZSh0b2FzdElkKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc3RhdGUudG9hc3RzLmZvckVhY2goKHRvYXN0KSA9PiB7XG4gICAgICAgICAgYWRkVG9SZW1vdmVRdWV1ZSh0b2FzdC5pZClcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHRvYXN0czogc3RhdGUudG9hc3RzLm1hcCgodCkgPT5cbiAgICAgICAgICB0LmlkID09PSB0b2FzdElkIHx8IHRvYXN0SWQgPT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgPyB7XG4gICAgICAgICAgICAgICAgLi4udCxcbiAgICAgICAgICAgICAgICBvcGVuOiBmYWxzZSxcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgOiB0XG4gICAgICAgICksXG4gICAgICB9XG4gICAgfVxuICAgIGNhc2UgXCJSRU1PVkVfVE9BU1RcIjpcbiAgICAgIGlmIChhY3Rpb24udG9hc3RJZCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgdG9hc3RzOiBbXSxcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHRvYXN0czogc3RhdGUudG9hc3RzLmZpbHRlcigodCkgPT4gdC5pZCAhPT0gYWN0aW9uLnRvYXN0SWQpLFxuICAgICAgfVxuICB9XG59XG5cbmNvbnN0IGxpc3RlbmVyczogQXJyYXk8KHN0YXRlOiBTdGF0ZSkgPT4gdm9pZD4gPSBbXVxuXG5sZXQgbWVtb3J5U3RhdGU6IFN0YXRlID0geyB0b2FzdHM6IFtdIH1cblxuZnVuY3Rpb24gZGlzcGF0Y2goYWN0aW9uOiBBY3Rpb24pIHtcbiAgbWVtb3J5U3RhdGUgPSByZWR1Y2VyKG1lbW9yeVN0YXRlLCBhY3Rpb24pXG4gIGxpc3RlbmVycy5mb3JFYWNoKChsaXN0ZW5lcikgPT4ge1xuICAgIGxpc3RlbmVyKG1lbW9yeVN0YXRlKVxuICB9KVxufVxuXG50eXBlIFRvYXN0ID0gT21pdDxUb2FzdGVyVG9hc3QsIFwiaWRcIj5cblxuZnVuY3Rpb24gdG9hc3QoeyAuLi5wcm9wcyB9OiBUb2FzdCkge1xuICBjb25zdCBpZCA9IGdlbklkKClcblxuICBjb25zdCB1cGRhdGUgPSAocHJvcHM6IFRvYXN0ZXJUb2FzdCkgPT5cbiAgICBkaXNwYXRjaCh7XG4gICAgICB0eXBlOiBcIlVQREFURV9UT0FTVFwiLFxuICAgICAgdG9hc3Q6IHsgLi4ucHJvcHMsIGlkIH0sXG4gICAgfSlcbiAgY29uc3QgZGlzbWlzcyA9ICgpID0+IGRpc3BhdGNoKHsgdHlwZTogXCJESVNNSVNTX1RPQVNUXCIsIHRvYXN0SWQ6IGlkIH0pXG5cbiAgZGlzcGF0Y2goe1xuICAgIHR5cGU6IFwiQUREX1RPQVNUXCIsXG4gICAgdG9hc3Q6IHtcbiAgICAgIC4uLnByb3BzLFxuICAgICAgaWQsXG4gICAgICBvcGVuOiB0cnVlLFxuICAgICAgb25PcGVuQ2hhbmdlOiAob3BlbikgPT4ge1xuICAgICAgICBpZiAoIW9wZW4pIGRpc21pc3MoKVxuICAgICAgfSxcbiAgICB9LFxuICB9KVxuXG4gIHJldHVybiB7XG4gICAgaWQ6IGlkLFxuICAgIGRpc21pc3MsXG4gICAgdXBkYXRlLFxuICB9XG59XG5cbmZ1bmN0aW9uIHVzZVRvYXN0KCkge1xuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IFJlYWN0LnVzZVN0YXRlPFN0YXRlPihtZW1vcnlTdGF0ZSlcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxpc3RlbmVycy5wdXNoKHNldFN0YXRlKVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjb25zdCBpbmRleCA9IGxpc3RlbmVycy5pbmRleE9mKHNldFN0YXRlKVxuICAgICAgaWYgKGluZGV4ID4gLTEpIHtcbiAgICAgICAgbGlzdGVuZXJzLnNwbGljZShpbmRleCwgMSlcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzdGF0ZV0pXG5cbiAgcmV0dXJuIHtcbiAgICAuLi5zdGF0ZSxcbiAgICB0b2FzdCxcbiAgICBkaXNtaXNzOiAodG9hc3RJZD86IHN0cmluZykgPT4gZGlzcGF0Y2goeyB0eXBlOiBcIkRJU01JU1NfVE9BU1RcIiwgdG9hc3RJZCB9KSxcbiAgfVxufVxuXG5leHBvcnQgeyB1c2VUb2FzdCwgdG9hc3QgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVE9BU1RfTElNSVQiLCJUT0FTVF9SRU1PVkVfREVMQVkiLCJhY3Rpb25UeXBlcyIsIkFERF9UT0FTVCIsIlVQREFURV9UT0FTVCIsIkRJU01JU1NfVE9BU1QiLCJSRU1PVkVfVE9BU1QiLCJjb3VudCIsImdlbklkIiwiTnVtYmVyIiwiTUFYX1NBRkVfSU5URUdFUiIsInRvU3RyaW5nIiwidG9hc3RUaW1lb3V0cyIsIk1hcCIsImFkZFRvUmVtb3ZlUXVldWUiLCJ0b2FzdElkIiwiaGFzIiwidGltZW91dCIsInNldFRpbWVvdXQiLCJkZWxldGUiLCJkaXNwYXRjaCIsInR5cGUiLCJzZXQiLCJyZWR1Y2VyIiwic3RhdGUiLCJhY3Rpb24iLCJ0b2FzdHMiLCJ0b2FzdCIsInNsaWNlIiwibWFwIiwidCIsImlkIiwiZm9yRWFjaCIsInVuZGVmaW5lZCIsIm9wZW4iLCJmaWx0ZXIiLCJsaXN0ZW5lcnMiLCJtZW1vcnlTdGF0ZSIsImxpc3RlbmVyIiwicHJvcHMiLCJ1cGRhdGUiLCJkaXNtaXNzIiwib25PcGVuQ2hhbmdlIiwidXNlVG9hc3QiLCJzZXRTdGF0ZSIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwicHVzaCIsImluZGV4IiwiaW5kZXhPZiIsInNwbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./components/website-preview.tsx":
/*!****************************************!*\
  !*** ./components/website-preview.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebsitePreview: () => (/* binding */ WebsitePreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tablet.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Globe,Loader2,Monitor,RefreshCw,Smartphone,Tablet!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ WebsitePreview auto */ \n\n\n\n\nfunction WebsitePreview({ version }) {\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [viewportSize, setViewportSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desktop\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (version) {\n            loadFiles();\n        } else {\n            setFiles([]);\n        }\n    }, [\n        version\n    ]);\n    const loadFiles = async ()=>{\n        if (!version) return;\n        setIsLoading(true);\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.chatAPI.getVersionPreview(version.chat_id, version.id);\n            setFiles(data.files || []);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load files\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const htmlContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!files.length) return \"\";\n        // Find the main HTML file\n        const htmlFile = files.find((f)=>f.path === \"/index.html\" || f.path.endsWith(\".html\"));\n        if (!htmlFile || !htmlFile.content) return \"\";\n        let content = htmlFile.content;\n        // Inject CSS files\n        const cssFiles = files.filter((f)=>f.path.endsWith(\".css\"));\n        for (const cssFile of cssFiles){\n            if (cssFile.content) {\n                content = content.replace(\"</head>\", `<style>${cssFile.content}</style></head>`);\n            }\n        }\n        // Inject JS files\n        const jsFiles = files.filter((f)=>f.path.endsWith(\".js\"));\n        for (const jsFile of jsFiles){\n            if (jsFile.content) {\n                content = content.replace(\"</body>\", `<script>${jsFile.content}</script></body>`);\n            }\n        }\n        return content;\n    }, [\n        files\n    ]);\n    const getViewportDimensions = ()=>{\n        switch(viewportSize){\n            case \"mobile\":\n                return {\n                    width: \"375px\",\n                    height: \"667px\"\n                };\n            case \"tablet\":\n                return {\n                    width: \"768px\",\n                    height: \"1024px\"\n                };\n            case \"desktop\":\n                return {\n                    width: \"100%\",\n                    height: \"100%\"\n                };\n        }\n    };\n    const handleRefresh = ()=>{\n        setRefreshKey((prev)=>prev + 1);\n    };\n    const handleOpenInNewTab = ()=>{\n        if (!htmlContent) return;\n        const blob = new Blob([\n            htmlContent\n        ], {\n            type: \"text/html\"\n        });\n        const url = URL.createObjectURL(blob);\n        window.open(url, \"_blank\");\n    };\n    const viewportButtons = [\n        {\n            size: \"mobile\",\n            icon: _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: \"Mobile\"\n        },\n        {\n            size: \"tablet\",\n            icon: _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: \"Tablet\"\n        },\n        {\n            size: \"desktop\",\n            icon: _barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: \"Desktop\"\n        }\n    ];\n    if (!version) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center text-muted-foreground\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No version selected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-[#1e1e1e]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 border-b border-[#333] bg-[#2d2d30] flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4 text-[#519aba]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-sm text-[#cccccc]\",\n                                children: \"Preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin text-[#cccccc]\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center bg-[#3c3c3c] rounded p-1\",\n                                children: viewportButtons.map(({ size, icon: Icon, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewportSize(size),\n                                        className: `p-1.5 rounded transition-colors ${viewportSize === size ? \"bg-[#0e639c] text-white\" : \"text-[#cccccc] hover:bg-[#4c4c4c]\"}`,\n                                        title: label,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, size, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                className: \"p-1.5 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors\",\n                                title: \"Refresh\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleOpenInNewTab,\n                                disabled: !htmlContent,\n                                className: \"p-1.5 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                title: \"Open in new tab\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-3 py-2 border-b border-[#333] bg-[#252526] flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#858585] hover:text-[#cccccc] disabled:opacity-50\",\n                                disabled: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#858585] hover:text-[#cccccc] disabled:opacity-50\",\n                                disabled: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                className: \"p-1 text-[#858585] hover:text-[#cccccc]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-[#3c3c3c] rounded px-3 py-1 text-sm text-[#cccccc] font-mono\",\n                        children: \"localhost:3000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1 text-[#858585] hover:text-[#cccccc]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 20 20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden bg-[#1e1e1e] p-4\",\n                children: htmlContent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-2xl overflow-hidden border border-[#333]\",\n                        style: {\n                            ...getViewportDimensions(),\n                            maxWidth: \"100%\",\n                            maxHeight: \"100%\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            srcDoc: htmlContent,\n                            className: \"w-full h-full border-0\",\n                            sandbox: \"allow-scripts allow-same-origin\",\n                            title: \"Website Preview\"\n                        }, refreshKey, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex items-center justify-center text-[#858585]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Globe_Loader2_Monitor_RefreshCw_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2\",\n                                children: \"No website to preview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm\",\n                                children: \"Start building with the AI to see your website here\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\website-preview.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/website-preview.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   chatAPI: () => (/* binding */ chatAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:8000\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Auth API\nconst authAPI = {\n    register: async (userData)=>{\n        const response = await api.post(\"/auth/register\", userData);\n        return response.data;\n    },\n    login: async (credentials)=>{\n        const response = await api.post(\"/auth/login\", credentials);\n        return response.data;\n    }\n};\n// Chat API\nconst chatAPI = {\n    getChats: async ()=>{\n        const response = await api.get(\"/chats/\");\n        return response.data;\n    },\n    createChat: async (chatData)=>{\n        const response = await api.post(\"/chats/\", chatData);\n        return response.data;\n    },\n    getChat: async (chatId)=>{\n        const response = await api.get(`/chats/${chatId}`);\n        return response.data;\n    },\n    getChatHistory: async (chatId)=>{\n        const response = await api.get(`/chat/${chatId}/history`);\n        return response.data;\n    },\n    sendMessage: async (chatId, content)=>{\n        // This returns a stream, handle differently\n        return api.post(`/chat/${chatId}/message`, {\n            content\n        }, {\n            responseType: \"stream\"\n        });\n    },\n    getVersionPreview: async (chatId, versionId)=>{\n        const response = await api.get(`/chat/${chatId}/versions/${versionId}/preview`);\n        return response.data;\n    },\n    getChatVersions: async (chatId)=>{\n        const response = await api.get(`/chats/${chatId}/versions`);\n        return response.data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api */ \"(ssr)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"auth_token\");\n        const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"user_data\");\n        if (savedToken && savedUser) {\n            try {\n                const userData = JSON.parse(savedUser);\n                setToken(savedToken);\n                setUser(userData);\n                _api__WEBPACK_IMPORTED_MODULE_3__.api.defaults.headers.common[\"Authorization\"] = `Bearer ${savedToken}`;\n            } catch (error) {\n                console.error(\"Error parsing saved user data:\", error);\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n                js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"user_data\");\n            }\n        }\n        setIsLoading(false);\n    }, []);\n    const login = (newToken, userData)=>{\n        setToken(newToken);\n        setUser(userData);\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"auth_token\", newToken, {\n            expires: 7\n        });\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"user_data\", JSON.stringify(userData), {\n            expires: 7\n        });\n        _api__WEBPACK_IMPORTED_MODULE_3__.api.defaults.headers.common[\"Authorization\"] = `Bearer ${newToken}`;\n    };\n    const logout = ()=>{\n        setToken(null);\n        setUser(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"auth_token\");\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"user_data\");\n        delete _api__WEBPACK_IMPORTED_MODULE_3__.api.defaults.headers.common[\"Authorization\"];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            login,\n            logout,\n            isLoading\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d464f3f763f3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/MTJkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQ0NjRmM2Y3NjNmM1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/chat/[id]/page.tsx":
/*!********************************!*\
  !*** ./app/chat/[id]/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Codora\frontend\app\chat\[id]\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var highlight_js_styles_vs2015_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! highlight.js/styles/vs2015.css */ \"(rsc)/./node_modules/highlight.js/styles/vs2015.css\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./lib/auth-context.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Codora - AI Web Development Assistant\",\n    description: \"Build full-stack web and mobile apps with AI assistance\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQU9NQTtBQUxnQjtBQUNpQjtBQUNVO0FBQ0E7QUFJMUMsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVO2tCQUN4Qiw0RUFBQ0M7WUFBS0QsV0FBV1YsMkpBQWU7c0JBQzlCLDRFQUFDQywyREFBWUE7O29CQUNWTTtrQ0FDRCw4REFBQ0wsMkRBQU9BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0ICdoaWdobGlnaHQuanMvc3R5bGVzL3ZzMjAxNS5jc3MnXG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICdAL2xpYi9hdXRoLWNvbnRleHQnXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RvYXN0ZXInXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdDb2RvcmEgLSBBSSBXZWIgRGV2ZWxvcG1lbnQgQXNzaXN0YW50JyxcbiAgZGVzY3JpcHRpb246ICdCdWlsZCBmdWxsLXN0YWNrIHdlYiBhbmQgbW9iaWxlIGFwcHMgd2l0aCBBSSBhc3Npc3RhbmNlJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPVwiZGFya1wiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8VG9hc3RlciAvPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkF1dGhQcm92aWRlciIsIlRvYXN0ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Codora\frontend\components\ui\toaster.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Codora\frontend\components\ui\toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Codora\frontend\lib\auth-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Codora\frontend\lib\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Codora\frontend\lib\auth-context.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/highlight.js","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/follow-redirects","vendor-chunks/lucide-react","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/class-variance-authority","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/react-arborist","vendor-chunks/mdast-util-to-markdown","vendor-chunks/react-dnd","vendor-chunks/mdast-util-to-hast","vendor-chunks/dnd-core","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/react-dnd-html5-backend","vendor-chunks/@monaco-editor","vendor-chunks/@babel","vendor-chunks/micromark","vendor-chunks/@react-dnd","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/lowlight","vendor-chunks/use-sync-external-store","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/unist-util-find-after","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/rehype-highlight","vendor-chunks/redux","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-text","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/hast-util-is-element","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/style-to-object","vendor-chunks/state-local","vendor-chunks/react-window","vendor-chunks/memoize-one","vendor-chunks/inline-style-parser","vendor-chunks/fast-deep-equal","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2F%5Bid%5D%2Fpage&page=%2Fchat%2F%5Bid%5D%2Fpage&appPaths=%2Fchat%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fchat%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();