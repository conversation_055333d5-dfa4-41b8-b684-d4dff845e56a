"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-window";
exports.ids = ["vendor-chunks/react-window"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-window/dist/index.esm.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-window/dist/index.esm.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FixedSizeGrid: () => (/* binding */ FixedSizeGrid),\n/* harmony export */   FixedSizeList: () => (/* binding */ FixedSizeList),\n/* harmony export */   VariableSizeGrid: () => (/* binding */ VariableSizeGrid),\n/* harmony export */   VariableSizeList: () => (/* binding */ VariableSizeList),\n/* harmony export */   areEqual: () => (/* binding */ areEqual),\n/* harmony export */   shouldComponentUpdate: () => (/* binding */ shouldComponentUpdate)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/assertThisInitialized */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js\");\n/* harmony import */ var memoize_one__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! memoize-one */ \"(ssr)/./node_modules/memoize-one/dist/memoize-one.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n\n\n\n\n\n\n// Animation frame based implementation of setTimeout.\n// Inspired by Joe Lambert, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\nvar hasNativePerformanceNow = typeof performance === \"object\" && typeof performance.now === \"function\";\nvar now = hasNativePerformanceNow ? function() {\n    return performance.now();\n} : function() {\n    return Date.now();\n};\nfunction cancelTimeout(timeoutID) {\n    cancelAnimationFrame(timeoutID.id);\n}\nfunction requestTimeout(callback, delay) {\n    var start = now();\n    function tick() {\n        if (now() - start >= delay) {\n            callback.call(null);\n        } else {\n            timeoutID.id = requestAnimationFrame(tick);\n        }\n    }\n    var timeoutID = {\n        id: requestAnimationFrame(tick)\n    };\n    return timeoutID;\n}\nvar size = -1; // This utility copied from \"dom-helpers\" package.\nfunction getScrollbarSize(recalculate) {\n    if (recalculate === void 0) {\n        recalculate = false;\n    }\n    if (size === -1 || recalculate) {\n        var div = document.createElement(\"div\");\n        var style = div.style;\n        style.width = \"50px\";\n        style.height = \"50px\";\n        style.overflow = \"scroll\";\n        document.body.appendChild(div);\n        size = div.offsetWidth - div.clientWidth;\n        document.body.removeChild(div);\n    }\n    return size;\n}\nvar cachedRTLResult = null; // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// Safari's elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nfunction getRTLOffsetType(recalculate) {\n    if (recalculate === void 0) {\n        recalculate = false;\n    }\n    if (cachedRTLResult === null || recalculate) {\n        var outerDiv = document.createElement(\"div\");\n        var outerStyle = outerDiv.style;\n        outerStyle.width = \"50px\";\n        outerStyle.height = \"50px\";\n        outerStyle.overflow = \"scroll\";\n        outerStyle.direction = \"rtl\";\n        var innerDiv = document.createElement(\"div\");\n        var innerStyle = innerDiv.style;\n        innerStyle.width = \"100px\";\n        innerStyle.height = \"100px\";\n        outerDiv.appendChild(innerDiv);\n        document.body.appendChild(outerDiv);\n        if (outerDiv.scrollLeft > 0) {\n            cachedRTLResult = \"positive-descending\";\n        } else {\n            outerDiv.scrollLeft = 1;\n            if (outerDiv.scrollLeft === 0) {\n                cachedRTLResult = \"negative\";\n            } else {\n                cachedRTLResult = \"positive-ascending\";\n            }\n        }\n        document.body.removeChild(outerDiv);\n        return cachedRTLResult;\n    }\n    return cachedRTLResult;\n}\nvar IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\nvar defaultItemKey = function defaultItemKey(_ref) {\n    var columnIndex = _ref.columnIndex, data = _ref.data, rowIndex = _ref.rowIndex;\n    return rowIndex + \":\" + columnIndex;\n}; // In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nvar devWarningsOverscanCount = null;\nvar devWarningsOverscanRowsColumnsCount = null;\nvar devWarningsTagName = null;\nif (true) {\n    if (false) {}\n}\nfunction createGridComponent(_ref2) {\n    var _class;\n    var getColumnOffset = _ref2.getColumnOffset, getColumnStartIndexForOffset = _ref2.getColumnStartIndexForOffset, getColumnStopIndexForStartIndex = _ref2.getColumnStopIndexForStartIndex, getColumnWidth = _ref2.getColumnWidth, getEstimatedTotalHeight = _ref2.getEstimatedTotalHeight, getEstimatedTotalWidth = _ref2.getEstimatedTotalWidth, getOffsetForColumnAndAlignment = _ref2.getOffsetForColumnAndAlignment, getOffsetForRowAndAlignment = _ref2.getOffsetForRowAndAlignment, getRowHeight = _ref2.getRowHeight, getRowOffset = _ref2.getRowOffset, getRowStartIndexForOffset = _ref2.getRowStartIndexForOffset, getRowStopIndexForStartIndex = _ref2.getRowStopIndexForStartIndex, initInstanceProps = _ref2.initInstanceProps, shouldResetStyleCacheOnItemSizeChange = _ref2.shouldResetStyleCacheOnItemSizeChange, validateProps = _ref2.validateProps;\n    return _class = /*#__PURE__*/ function(_PureComponent) {\n        (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(Grid, _PureComponent);\n        // Always use explicit constructor for React components.\n        // It produces less code after transpilation. (#26)\n        // eslint-disable-next-line no-useless-constructor\n        function Grid(props) {\n            var _this;\n            _this = _PureComponent.call(this, props) || this;\n            _this._instanceProps = initInstanceProps(_this.props, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this));\n            _this._resetIsScrollingTimeoutId = null;\n            _this._outerRef = void 0;\n            _this.state = {\n                instance: (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this),\n                isScrolling: false,\n                horizontalScrollDirection: \"forward\",\n                scrollLeft: typeof _this.props.initialScrollLeft === \"number\" ? _this.props.initialScrollLeft : 0,\n                scrollTop: typeof _this.props.initialScrollTop === \"number\" ? _this.props.initialScrollTop : 0,\n                scrollUpdateWasRequested: false,\n                verticalScrollDirection: \"forward\"\n            };\n            _this._callOnItemsRendered = void 0;\n            _this._callOnItemsRendered = (0,memoize_one__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function(overscanColumnStartIndex, overscanColumnStopIndex, overscanRowStartIndex, overscanRowStopIndex, visibleColumnStartIndex, visibleColumnStopIndex, visibleRowStartIndex, visibleRowStopIndex) {\n                return _this.props.onItemsRendered({\n                    overscanColumnStartIndex: overscanColumnStartIndex,\n                    overscanColumnStopIndex: overscanColumnStopIndex,\n                    overscanRowStartIndex: overscanRowStartIndex,\n                    overscanRowStopIndex: overscanRowStopIndex,\n                    visibleColumnStartIndex: visibleColumnStartIndex,\n                    visibleColumnStopIndex: visibleColumnStopIndex,\n                    visibleRowStartIndex: visibleRowStartIndex,\n                    visibleRowStopIndex: visibleRowStopIndex\n                });\n            });\n            _this._callOnScroll = void 0;\n            _this._callOnScroll = (0,memoize_one__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function(scrollLeft, scrollTop, horizontalScrollDirection, verticalScrollDirection, scrollUpdateWasRequested) {\n                return _this.props.onScroll({\n                    horizontalScrollDirection: horizontalScrollDirection,\n                    scrollLeft: scrollLeft,\n                    scrollTop: scrollTop,\n                    verticalScrollDirection: verticalScrollDirection,\n                    scrollUpdateWasRequested: scrollUpdateWasRequested\n                });\n            });\n            _this._getItemStyle = void 0;\n            _this._getItemStyle = function(rowIndex, columnIndex) {\n                var _this$props = _this.props, columnWidth = _this$props.columnWidth, direction = _this$props.direction, rowHeight = _this$props.rowHeight;\n                var itemStyleCache = _this._getItemStyleCache(shouldResetStyleCacheOnItemSizeChange && columnWidth, shouldResetStyleCacheOnItemSizeChange && direction, shouldResetStyleCacheOnItemSizeChange && rowHeight);\n                var key = rowIndex + \":\" + columnIndex;\n                var style;\n                if (itemStyleCache.hasOwnProperty(key)) {\n                    style = itemStyleCache[key];\n                } else {\n                    var _offset = getColumnOffset(_this.props, columnIndex, _this._instanceProps);\n                    var isRtl = direction === \"rtl\";\n                    itemStyleCache[key] = style = {\n                        position: \"absolute\",\n                        left: isRtl ? undefined : _offset,\n                        right: isRtl ? _offset : undefined,\n                        top: getRowOffset(_this.props, rowIndex, _this._instanceProps),\n                        height: getRowHeight(_this.props, rowIndex, _this._instanceProps),\n                        width: getColumnWidth(_this.props, columnIndex, _this._instanceProps)\n                    };\n                }\n                return style;\n            };\n            _this._getItemStyleCache = void 0;\n            _this._getItemStyleCache = (0,memoize_one__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function(_, __, ___) {\n                return {};\n            });\n            _this._onScroll = function(event) {\n                var _event$currentTarget = event.currentTarget, clientHeight = _event$currentTarget.clientHeight, clientWidth = _event$currentTarget.clientWidth, scrollLeft = _event$currentTarget.scrollLeft, scrollTop = _event$currentTarget.scrollTop, scrollHeight = _event$currentTarget.scrollHeight, scrollWidth = _event$currentTarget.scrollWidth;\n                _this.setState(function(prevState) {\n                    if (prevState.scrollLeft === scrollLeft && prevState.scrollTop === scrollTop) {\n                        // Scroll position may have been updated by cDM/cDU,\n                        // In which case we don't need to trigger another render,\n                        // And we don't want to update state.isScrolling.\n                        return null;\n                    }\n                    var direction = _this.props.direction; // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n                    // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n                    // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n                    // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n                    var calculatedScrollLeft = scrollLeft;\n                    if (direction === \"rtl\") {\n                        switch(getRTLOffsetType()){\n                            case \"negative\":\n                                calculatedScrollLeft = -scrollLeft;\n                                break;\n                            case \"positive-descending\":\n                                calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n                                break;\n                        }\n                    } // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n                    calculatedScrollLeft = Math.max(0, Math.min(calculatedScrollLeft, scrollWidth - clientWidth));\n                    var calculatedScrollTop = Math.max(0, Math.min(scrollTop, scrollHeight - clientHeight));\n                    return {\n                        isScrolling: true,\n                        horizontalScrollDirection: prevState.scrollLeft < scrollLeft ? \"forward\" : \"backward\",\n                        scrollLeft: calculatedScrollLeft,\n                        scrollTop: calculatedScrollTop,\n                        verticalScrollDirection: prevState.scrollTop < scrollTop ? \"forward\" : \"backward\",\n                        scrollUpdateWasRequested: false\n                    };\n                }, _this._resetIsScrollingDebounced);\n            };\n            _this._outerRefSetter = function(ref) {\n                var outerRef = _this.props.outerRef;\n                _this._outerRef = ref;\n                if (typeof outerRef === \"function\") {\n                    outerRef(ref);\n                } else if (outerRef != null && typeof outerRef === \"object\" && outerRef.hasOwnProperty(\"current\")) {\n                    outerRef.current = ref;\n                }\n            };\n            _this._resetIsScrollingDebounced = function() {\n                if (_this._resetIsScrollingTimeoutId !== null) {\n                    cancelTimeout(_this._resetIsScrollingTimeoutId);\n                }\n                _this._resetIsScrollingTimeoutId = requestTimeout(_this._resetIsScrolling, IS_SCROLLING_DEBOUNCE_INTERVAL);\n            };\n            _this._resetIsScrolling = function() {\n                _this._resetIsScrollingTimeoutId = null;\n                _this.setState({\n                    isScrolling: false\n                }, function() {\n                    // Clear style cache after state update has been committed.\n                    // This way we don't break pure sCU for items that don't use isScrolling param.\n                    _this._getItemStyleCache(-1);\n                });\n            };\n            return _this;\n        }\n        Grid.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, prevState) {\n            validateSharedProps(nextProps, prevState);\n            validateProps(nextProps);\n            return null;\n        };\n        var _proto = Grid.prototype;\n        _proto.scrollTo = function scrollTo(_ref3) {\n            var scrollLeft = _ref3.scrollLeft, scrollTop = _ref3.scrollTop;\n            if (scrollLeft !== undefined) {\n                scrollLeft = Math.max(0, scrollLeft);\n            }\n            if (scrollTop !== undefined) {\n                scrollTop = Math.max(0, scrollTop);\n            }\n            this.setState(function(prevState) {\n                if (scrollLeft === undefined) {\n                    scrollLeft = prevState.scrollLeft;\n                }\n                if (scrollTop === undefined) {\n                    scrollTop = prevState.scrollTop;\n                }\n                if (prevState.scrollLeft === scrollLeft && prevState.scrollTop === scrollTop) {\n                    return null;\n                }\n                return {\n                    horizontalScrollDirection: prevState.scrollLeft < scrollLeft ? \"forward\" : \"backward\",\n                    scrollLeft: scrollLeft,\n                    scrollTop: scrollTop,\n                    scrollUpdateWasRequested: true,\n                    verticalScrollDirection: prevState.scrollTop < scrollTop ? \"forward\" : \"backward\"\n                };\n            }, this._resetIsScrollingDebounced);\n        };\n        _proto.scrollToItem = function scrollToItem(_ref4) {\n            var _ref4$align = _ref4.align, align = _ref4$align === void 0 ? \"auto\" : _ref4$align, columnIndex = _ref4.columnIndex, rowIndex = _ref4.rowIndex;\n            var _this$props2 = this.props, columnCount = _this$props2.columnCount, height = _this$props2.height, rowCount = _this$props2.rowCount, width = _this$props2.width;\n            var _this$state = this.state, scrollLeft = _this$state.scrollLeft, scrollTop = _this$state.scrollTop;\n            var scrollbarSize = getScrollbarSize();\n            if (columnIndex !== undefined) {\n                columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n            }\n            if (rowIndex !== undefined) {\n                rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n            }\n            var estimatedTotalHeight = getEstimatedTotalHeight(this.props, this._instanceProps);\n            var estimatedTotalWidth = getEstimatedTotalWidth(this.props, this._instanceProps); // The scrollbar size should be considered when scrolling an item into view,\n            // to ensure it's fully visible.\n            // But we only need to account for its size when it's actually visible.\n            var horizontalScrollbarSize = estimatedTotalWidth > width ? scrollbarSize : 0;\n            var verticalScrollbarSize = estimatedTotalHeight > height ? scrollbarSize : 0;\n            this.scrollTo({\n                scrollLeft: columnIndex !== undefined ? getOffsetForColumnAndAlignment(this.props, columnIndex, align, scrollLeft, this._instanceProps, verticalScrollbarSize) : scrollLeft,\n                scrollTop: rowIndex !== undefined ? getOffsetForRowAndAlignment(this.props, rowIndex, align, scrollTop, this._instanceProps, horizontalScrollbarSize) : scrollTop\n            });\n        };\n        _proto.componentDidMount = function componentDidMount() {\n            var _this$props3 = this.props, initialScrollLeft = _this$props3.initialScrollLeft, initialScrollTop = _this$props3.initialScrollTop;\n            if (this._outerRef != null) {\n                var outerRef = this._outerRef;\n                if (typeof initialScrollLeft === \"number\") {\n                    outerRef.scrollLeft = initialScrollLeft;\n                }\n                if (typeof initialScrollTop === \"number\") {\n                    outerRef.scrollTop = initialScrollTop;\n                }\n            }\n            this._callPropsCallbacks();\n        };\n        _proto.componentDidUpdate = function componentDidUpdate() {\n            var direction = this.props.direction;\n            var _this$state2 = this.state, scrollLeft = _this$state2.scrollLeft, scrollTop = _this$state2.scrollTop, scrollUpdateWasRequested = _this$state2.scrollUpdateWasRequested;\n            if (scrollUpdateWasRequested && this._outerRef != null) {\n                // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n                // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n                // So we need to determine which browser behavior we're dealing with, and mimic it.\n                var outerRef = this._outerRef;\n                if (direction === \"rtl\") {\n                    switch(getRTLOffsetType()){\n                        case \"negative\":\n                            outerRef.scrollLeft = -scrollLeft;\n                            break;\n                        case \"positive-ascending\":\n                            outerRef.scrollLeft = scrollLeft;\n                            break;\n                        default:\n                            var clientWidth = outerRef.clientWidth, scrollWidth = outerRef.scrollWidth;\n                            outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n                            break;\n                    }\n                } else {\n                    outerRef.scrollLeft = Math.max(0, scrollLeft);\n                }\n                outerRef.scrollTop = Math.max(0, scrollTop);\n            }\n            this._callPropsCallbacks();\n        };\n        _proto.componentWillUnmount = function componentWillUnmount() {\n            if (this._resetIsScrollingTimeoutId !== null) {\n                cancelTimeout(this._resetIsScrollingTimeoutId);\n            }\n        };\n        _proto.render = function render() {\n            var _this$props4 = this.props, children = _this$props4.children, className = _this$props4.className, columnCount = _this$props4.columnCount, direction = _this$props4.direction, height = _this$props4.height, innerRef = _this$props4.innerRef, innerElementType = _this$props4.innerElementType, innerTagName = _this$props4.innerTagName, itemData = _this$props4.itemData, _this$props4$itemKey = _this$props4.itemKey, itemKey = _this$props4$itemKey === void 0 ? defaultItemKey : _this$props4$itemKey, outerElementType = _this$props4.outerElementType, outerTagName = _this$props4.outerTagName, rowCount = _this$props4.rowCount, style = _this$props4.style, useIsScrolling = _this$props4.useIsScrolling, width = _this$props4.width;\n            var isScrolling = this.state.isScrolling;\n            var _this$_getHorizontalR = this._getHorizontalRangeToRender(), columnStartIndex = _this$_getHorizontalR[0], columnStopIndex = _this$_getHorizontalR[1];\n            var _this$_getVerticalRan = this._getVerticalRangeToRender(), rowStartIndex = _this$_getVerticalRan[0], rowStopIndex = _this$_getVerticalRan[1];\n            var items = [];\n            if (columnCount > 0 && rowCount) {\n                for(var _rowIndex = rowStartIndex; _rowIndex <= rowStopIndex; _rowIndex++){\n                    for(var _columnIndex = columnStartIndex; _columnIndex <= columnStopIndex; _columnIndex++){\n                        items.push(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createElement)(children, {\n                            columnIndex: _columnIndex,\n                            data: itemData,\n                            isScrolling: useIsScrolling ? isScrolling : undefined,\n                            key: itemKey({\n                                columnIndex: _columnIndex,\n                                data: itemData,\n                                rowIndex: _rowIndex\n                            }),\n                            rowIndex: _rowIndex,\n                            style: this._getItemStyle(_rowIndex, _columnIndex)\n                        }));\n                    }\n                }\n            } // Read this value AFTER items have been created,\n            // So their actual sizes (if variable) are taken into consideration.\n            var estimatedTotalHeight = getEstimatedTotalHeight(this.props, this._instanceProps);\n            var estimatedTotalWidth = getEstimatedTotalWidth(this.props, this._instanceProps);\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createElement)(outerElementType || outerTagName || \"div\", {\n                className: className,\n                onScroll: this._onScroll,\n                ref: this._outerRefSetter,\n                style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n                    position: \"relative\",\n                    height: height,\n                    width: width,\n                    overflow: \"auto\",\n                    WebkitOverflowScrolling: \"touch\",\n                    willChange: \"transform\",\n                    direction: direction\n                }, style)\n            }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createElement)(innerElementType || innerTagName || \"div\", {\n                children: items,\n                ref: innerRef,\n                style: {\n                    height: estimatedTotalHeight,\n                    pointerEvents: isScrolling ? \"none\" : undefined,\n                    width: estimatedTotalWidth\n                }\n            }));\n        };\n        _proto._callPropsCallbacks = function _callPropsCallbacks() {\n            var _this$props5 = this.props, columnCount = _this$props5.columnCount, onItemsRendered = _this$props5.onItemsRendered, onScroll = _this$props5.onScroll, rowCount = _this$props5.rowCount;\n            if (typeof onItemsRendered === \"function\") {\n                if (columnCount > 0 && rowCount > 0) {\n                    var _this$_getHorizontalR2 = this._getHorizontalRangeToRender(), _overscanColumnStartIndex = _this$_getHorizontalR2[0], _overscanColumnStopIndex = _this$_getHorizontalR2[1], _visibleColumnStartIndex = _this$_getHorizontalR2[2], _visibleColumnStopIndex = _this$_getHorizontalR2[3];\n                    var _this$_getVerticalRan2 = this._getVerticalRangeToRender(), _overscanRowStartIndex = _this$_getVerticalRan2[0], _overscanRowStopIndex = _this$_getVerticalRan2[1], _visibleRowStartIndex = _this$_getVerticalRan2[2], _visibleRowStopIndex = _this$_getVerticalRan2[3];\n                    this._callOnItemsRendered(_overscanColumnStartIndex, _overscanColumnStopIndex, _overscanRowStartIndex, _overscanRowStopIndex, _visibleColumnStartIndex, _visibleColumnStopIndex, _visibleRowStartIndex, _visibleRowStopIndex);\n                }\n            }\n            if (typeof onScroll === \"function\") {\n                var _this$state3 = this.state, _horizontalScrollDirection = _this$state3.horizontalScrollDirection, _scrollLeft = _this$state3.scrollLeft, _scrollTop = _this$state3.scrollTop, _scrollUpdateWasRequested = _this$state3.scrollUpdateWasRequested, _verticalScrollDirection = _this$state3.verticalScrollDirection;\n                this._callOnScroll(_scrollLeft, _scrollTop, _horizontalScrollDirection, _verticalScrollDirection, _scrollUpdateWasRequested);\n            }\n        } // Lazily create and cache item styles while scrolling,\n        ;\n        _proto._getHorizontalRangeToRender = function _getHorizontalRangeToRender() {\n            var _this$props6 = this.props, columnCount = _this$props6.columnCount, overscanColumnCount = _this$props6.overscanColumnCount, overscanColumnsCount = _this$props6.overscanColumnsCount, overscanCount = _this$props6.overscanCount, rowCount = _this$props6.rowCount;\n            var _this$state4 = this.state, horizontalScrollDirection = _this$state4.horizontalScrollDirection, isScrolling = _this$state4.isScrolling, scrollLeft = _this$state4.scrollLeft;\n            var overscanCountResolved = overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n            if (columnCount === 0 || rowCount === 0) {\n                return [\n                    0,\n                    0,\n                    0,\n                    0\n                ];\n            }\n            var startIndex = getColumnStartIndexForOffset(this.props, scrollLeft, this._instanceProps);\n            var stopIndex = getColumnStopIndexForStartIndex(this.props, startIndex, scrollLeft, this._instanceProps); // Overscan by one item in each direction so that tab/focus works.\n            // If there isn't at least one extra item, tab loops back around.\n            var overscanBackward = !isScrolling || horizontalScrollDirection === \"backward\" ? Math.max(1, overscanCountResolved) : 1;\n            var overscanForward = !isScrolling || horizontalScrollDirection === \"forward\" ? Math.max(1, overscanCountResolved) : 1;\n            return [\n                Math.max(0, startIndex - overscanBackward),\n                Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n                startIndex,\n                stopIndex\n            ];\n        };\n        _proto._getVerticalRangeToRender = function _getVerticalRangeToRender() {\n            var _this$props7 = this.props, columnCount = _this$props7.columnCount, overscanCount = _this$props7.overscanCount, overscanRowCount = _this$props7.overscanRowCount, overscanRowsCount = _this$props7.overscanRowsCount, rowCount = _this$props7.rowCount;\n            var _this$state5 = this.state, isScrolling = _this$state5.isScrolling, verticalScrollDirection = _this$state5.verticalScrollDirection, scrollTop = _this$state5.scrollTop;\n            var overscanCountResolved = overscanRowCount || overscanRowsCount || overscanCount || 1;\n            if (columnCount === 0 || rowCount === 0) {\n                return [\n                    0,\n                    0,\n                    0,\n                    0\n                ];\n            }\n            var startIndex = getRowStartIndexForOffset(this.props, scrollTop, this._instanceProps);\n            var stopIndex = getRowStopIndexForStartIndex(this.props, startIndex, scrollTop, this._instanceProps); // Overscan by one item in each direction so that tab/focus works.\n            // If there isn't at least one extra item, tab loops back around.\n            var overscanBackward = !isScrolling || verticalScrollDirection === \"backward\" ? Math.max(1, overscanCountResolved) : 1;\n            var overscanForward = !isScrolling || verticalScrollDirection === \"forward\" ? Math.max(1, overscanCountResolved) : 1;\n            return [\n                Math.max(0, startIndex - overscanBackward),\n                Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n                startIndex,\n                stopIndex\n            ];\n        };\n        return Grid;\n    }(react__WEBPACK_IMPORTED_MODULE_3__.PureComponent), _class.defaultProps = {\n        direction: \"ltr\",\n        itemData: undefined,\n        useIsScrolling: false\n    }, _class;\n}\nvar validateSharedProps = function validateSharedProps(_ref5, _ref6) {\n    var children = _ref5.children, direction = _ref5.direction, height = _ref5.height, innerTagName = _ref5.innerTagName, outerTagName = _ref5.outerTagName, overscanColumnsCount = _ref5.overscanColumnsCount, overscanCount = _ref5.overscanCount, overscanRowsCount = _ref5.overscanRowsCount, width = _ref5.width;\n    var instance = _ref6.instance;\n    if (true) {\n        if (typeof overscanCount === \"number\") {\n            if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n                devWarningsOverscanCount.add(instance);\n                console.warn(\"The overscanCount prop has been deprecated. \" + \"Please use the overscanColumnCount and overscanRowCount props instead.\");\n            }\n        }\n        if (typeof overscanColumnsCount === \"number\" || typeof overscanRowsCount === \"number\") {\n            if (devWarningsOverscanRowsColumnsCount && !devWarningsOverscanRowsColumnsCount.has(instance)) {\n                devWarningsOverscanRowsColumnsCount.add(instance);\n                console.warn(\"The overscanColumnsCount and overscanRowsCount props have been deprecated. \" + \"Please use the overscanColumnCount and overscanRowCount props instead.\");\n            }\n        }\n        if (innerTagName != null || outerTagName != null) {\n            if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n                devWarningsTagName.add(instance);\n                console.warn(\"The innerTagName and outerTagName props have been deprecated. \" + \"Please use the innerElementType and outerElementType props instead.\");\n            }\n        }\n        if (children == null) {\n            throw Error('An invalid \"children\" prop has been specified. ' + \"Value should be a React component. \" + ('\"' + (children === null ? \"null\" : typeof children) + '\" was specified.'));\n        }\n        switch(direction){\n            case \"ltr\":\n            case \"rtl\":\n                break;\n            default:\n                throw Error('An invalid \"direction\" prop has been specified. ' + 'Value should be either \"ltr\" or \"rtl\". ' + ('\"' + direction + '\" was specified.'));\n        }\n        if (typeof width !== \"number\") {\n            throw Error('An invalid \"width\" prop has been specified. ' + \"Grids must specify a number for width. \" + ('\"' + (width === null ? \"null\" : typeof width) + '\" was specified.'));\n        }\n        if (typeof height !== \"number\") {\n            throw Error('An invalid \"height\" prop has been specified. ' + \"Grids must specify a number for height. \" + ('\"' + (height === null ? \"null\" : typeof height) + '\" was specified.'));\n        }\n    }\n};\nvar DEFAULT_ESTIMATED_ITEM_SIZE = 50;\nvar getEstimatedTotalHeight = function getEstimatedTotalHeight(_ref, _ref2) {\n    var rowCount = _ref.rowCount;\n    var rowMetadataMap = _ref2.rowMetadataMap, estimatedRowHeight = _ref2.estimatedRowHeight, lastMeasuredRowIndex = _ref2.lastMeasuredRowIndex;\n    var totalSizeOfMeasuredRows = 0; // Edge case check for when the number of items decreases while a scroll is in progress.\n    // https://github.com/bvaughn/react-window/pull/138\n    if (lastMeasuredRowIndex >= rowCount) {\n        lastMeasuredRowIndex = rowCount - 1;\n    }\n    if (lastMeasuredRowIndex >= 0) {\n        var itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n        totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n    }\n    var numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n    var totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n    return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\nvar getEstimatedTotalWidth = function getEstimatedTotalWidth(_ref3, _ref4) {\n    var columnCount = _ref3.columnCount;\n    var columnMetadataMap = _ref4.columnMetadataMap, estimatedColumnWidth = _ref4.estimatedColumnWidth, lastMeasuredColumnIndex = _ref4.lastMeasuredColumnIndex;\n    var totalSizeOfMeasuredRows = 0; // Edge case check for when the number of items decreases while a scroll is in progress.\n    // https://github.com/bvaughn/react-window/pull/138\n    if (lastMeasuredColumnIndex >= columnCount) {\n        lastMeasuredColumnIndex = columnCount - 1;\n    }\n    if (lastMeasuredColumnIndex >= 0) {\n        var itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n        totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n    }\n    var numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n    var totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n    return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\nvar getItemMetadata = function getItemMetadata(itemType, props, index, instanceProps) {\n    var itemMetadataMap, itemSize, lastMeasuredIndex;\n    if (itemType === \"column\") {\n        itemMetadataMap = instanceProps.columnMetadataMap;\n        itemSize = props.columnWidth;\n        lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n    } else {\n        itemMetadataMap = instanceProps.rowMetadataMap;\n        itemSize = props.rowHeight;\n        lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n    }\n    if (index > lastMeasuredIndex) {\n        var offset = 0;\n        if (lastMeasuredIndex >= 0) {\n            var itemMetadata = itemMetadataMap[lastMeasuredIndex];\n            offset = itemMetadata.offset + itemMetadata.size;\n        }\n        for(var i = lastMeasuredIndex + 1; i <= index; i++){\n            var size = itemSize(i);\n            itemMetadataMap[i] = {\n                offset: offset,\n                size: size\n            };\n            offset += size;\n        }\n        if (itemType === \"column\") {\n            instanceProps.lastMeasuredColumnIndex = index;\n        } else {\n            instanceProps.lastMeasuredRowIndex = index;\n        }\n    }\n    return itemMetadataMap[index];\n};\nvar findNearestItem = function findNearestItem(itemType, props, instanceProps, offset) {\n    var itemMetadataMap, lastMeasuredIndex;\n    if (itemType === \"column\") {\n        itemMetadataMap = instanceProps.columnMetadataMap;\n        lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n    } else {\n        itemMetadataMap = instanceProps.rowMetadataMap;\n        lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n    }\n    var lastMeasuredItemOffset = lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n    if (lastMeasuredItemOffset >= offset) {\n        // If we've already measured items within this range just use a binary search as it's faster.\n        return findNearestItemBinarySearch(itemType, props, instanceProps, lastMeasuredIndex, 0, offset);\n    } else {\n        // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n        // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n        // The overall complexity for this approach is O(log n).\n        return findNearestItemExponentialSearch(itemType, props, instanceProps, Math.max(0, lastMeasuredIndex), offset);\n    }\n};\nvar findNearestItemBinarySearch = function findNearestItemBinarySearch(itemType, props, instanceProps, high, low, offset) {\n    while(low <= high){\n        var middle = low + Math.floor((high - low) / 2);\n        var currentOffset = getItemMetadata(itemType, props, middle, instanceProps).offset;\n        if (currentOffset === offset) {\n            return middle;\n        } else if (currentOffset < offset) {\n            low = middle + 1;\n        } else if (currentOffset > offset) {\n            high = middle - 1;\n        }\n    }\n    if (low > 0) {\n        return low - 1;\n    } else {\n        return 0;\n    }\n};\nvar findNearestItemExponentialSearch = function findNearestItemExponentialSearch(itemType, props, instanceProps, index, offset) {\n    var itemCount = itemType === \"column\" ? props.columnCount : props.rowCount;\n    var interval = 1;\n    while(index < itemCount && getItemMetadata(itemType, props, index, instanceProps).offset < offset){\n        index += interval;\n        interval *= 2;\n    }\n    return findNearestItemBinarySearch(itemType, props, instanceProps, Math.min(index, itemCount - 1), Math.floor(index / 2), offset);\n};\nvar getOffsetForIndexAndAlignment = function getOffsetForIndexAndAlignment(itemType, props, index, align, scrollOffset, instanceProps, scrollbarSize) {\n    var size = itemType === \"column\" ? props.width : props.height;\n    var itemMetadata = getItemMetadata(itemType, props, index, instanceProps); // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    var estimatedTotalSize = itemType === \"column\" ? getEstimatedTotalWidth(props, instanceProps) : getEstimatedTotalHeight(props, instanceProps);\n    var maxOffset = Math.max(0, Math.min(estimatedTotalSize - size, itemMetadata.offset));\n    var minOffset = Math.max(0, itemMetadata.offset - size + scrollbarSize + itemMetadata.size);\n    if (align === \"smart\") {\n        if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n            align = \"auto\";\n        } else {\n            align = \"center\";\n        }\n    }\n    switch(align){\n        case \"start\":\n            return maxOffset;\n        case \"end\":\n            return minOffset;\n        case \"center\":\n            return Math.round(minOffset + (maxOffset - minOffset) / 2);\n        case \"auto\":\n        default:\n            if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n                return scrollOffset;\n            } else if (minOffset > maxOffset) {\n                // Because we only take into account the scrollbar size when calculating minOffset\n                // this value can be larger than maxOffset when at the end of the list\n                return minOffset;\n            } else if (scrollOffset < minOffset) {\n                return minOffset;\n            } else {\n                return maxOffset;\n            }\n    }\n};\nvar VariableSizeGrid = /*#__PURE__*/ createGridComponent({\n    getColumnOffset: function getColumnOffset(props, index, instanceProps) {\n        return getItemMetadata(\"column\", props, index, instanceProps).offset;\n    },\n    getColumnStartIndexForOffset: function getColumnStartIndexForOffset(props, scrollLeft, instanceProps) {\n        return findNearestItem(\"column\", props, instanceProps, scrollLeft);\n    },\n    getColumnStopIndexForStartIndex: function getColumnStopIndexForStartIndex(props, startIndex, scrollLeft, instanceProps) {\n        var columnCount = props.columnCount, width = props.width;\n        var itemMetadata = getItemMetadata(\"column\", props, startIndex, instanceProps);\n        var maxOffset = scrollLeft + width;\n        var offset = itemMetadata.offset + itemMetadata.size;\n        var stopIndex = startIndex;\n        while(stopIndex < columnCount - 1 && offset < maxOffset){\n            stopIndex++;\n            offset += getItemMetadata(\"column\", props, stopIndex, instanceProps).size;\n        }\n        return stopIndex;\n    },\n    getColumnWidth: function getColumnWidth(props, index, instanceProps) {\n        return instanceProps.columnMetadataMap[index].size;\n    },\n    getEstimatedTotalHeight: getEstimatedTotalHeight,\n    getEstimatedTotalWidth: getEstimatedTotalWidth,\n    getOffsetForColumnAndAlignment: function getOffsetForColumnAndAlignment(props, index, align, scrollOffset, instanceProps, scrollbarSize) {\n        return getOffsetForIndexAndAlignment(\"column\", props, index, align, scrollOffset, instanceProps, scrollbarSize);\n    },\n    getOffsetForRowAndAlignment: function getOffsetForRowAndAlignment(props, index, align, scrollOffset, instanceProps, scrollbarSize) {\n        return getOffsetForIndexAndAlignment(\"row\", props, index, align, scrollOffset, instanceProps, scrollbarSize);\n    },\n    getRowOffset: function getRowOffset(props, index, instanceProps) {\n        return getItemMetadata(\"row\", props, index, instanceProps).offset;\n    },\n    getRowHeight: function getRowHeight(props, index, instanceProps) {\n        return instanceProps.rowMetadataMap[index].size;\n    },\n    getRowStartIndexForOffset: function getRowStartIndexForOffset(props, scrollTop, instanceProps) {\n        return findNearestItem(\"row\", props, instanceProps, scrollTop);\n    },\n    getRowStopIndexForStartIndex: function getRowStopIndexForStartIndex(props, startIndex, scrollTop, instanceProps) {\n        var rowCount = props.rowCount, height = props.height;\n        var itemMetadata = getItemMetadata(\"row\", props, startIndex, instanceProps);\n        var maxOffset = scrollTop + height;\n        var offset = itemMetadata.offset + itemMetadata.size;\n        var stopIndex = startIndex;\n        while(stopIndex < rowCount - 1 && offset < maxOffset){\n            stopIndex++;\n            offset += getItemMetadata(\"row\", props, stopIndex, instanceProps).size;\n        }\n        return stopIndex;\n    },\n    initInstanceProps: function initInstanceProps(props, instance) {\n        var _ref5 = props, estimatedColumnWidth = _ref5.estimatedColumnWidth, estimatedRowHeight = _ref5.estimatedRowHeight;\n        var instanceProps = {\n            columnMetadataMap: {},\n            estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n            estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n            lastMeasuredColumnIndex: -1,\n            lastMeasuredRowIndex: -1,\n            rowMetadataMap: {}\n        };\n        instance.resetAfterColumnIndex = function(columnIndex, shouldForceUpdate) {\n            if (shouldForceUpdate === void 0) {\n                shouldForceUpdate = true;\n            }\n            instance.resetAfterIndices({\n                columnIndex: columnIndex,\n                shouldForceUpdate: shouldForceUpdate\n            });\n        };\n        instance.resetAfterRowIndex = function(rowIndex, shouldForceUpdate) {\n            if (shouldForceUpdate === void 0) {\n                shouldForceUpdate = true;\n            }\n            instance.resetAfterIndices({\n                rowIndex: rowIndex,\n                shouldForceUpdate: shouldForceUpdate\n            });\n        };\n        instance.resetAfterIndices = function(_ref6) {\n            var columnIndex = _ref6.columnIndex, rowIndex = _ref6.rowIndex, _ref6$shouldForceUpda = _ref6.shouldForceUpdate, shouldForceUpdate = _ref6$shouldForceUpda === void 0 ? true : _ref6$shouldForceUpda;\n            if (typeof columnIndex === \"number\") {\n                instanceProps.lastMeasuredColumnIndex = Math.min(instanceProps.lastMeasuredColumnIndex, columnIndex - 1);\n            }\n            if (typeof rowIndex === \"number\") {\n                instanceProps.lastMeasuredRowIndex = Math.min(instanceProps.lastMeasuredRowIndex, rowIndex - 1);\n            } // We could potentially optimize further by only evicting styles after this index,\n            // But since styles are only cached while scrolling is in progress-\n            // It seems an unnecessary optimization.\n            // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n            instance._getItemStyleCache(-1);\n            if (shouldForceUpdate) {\n                instance.forceUpdate();\n            }\n        };\n        return instanceProps;\n    },\n    shouldResetStyleCacheOnItemSizeChange: false,\n    validateProps: function validateProps(_ref7) {\n        var columnWidth = _ref7.columnWidth, rowHeight = _ref7.rowHeight;\n        if (true) {\n            if (typeof columnWidth !== \"function\") {\n                throw Error('An invalid \"columnWidth\" prop has been specified. ' + \"Value should be a function. \" + ('\"' + (columnWidth === null ? \"null\" : typeof columnWidth) + '\" was specified.'));\n            } else if (typeof rowHeight !== \"function\") {\n                throw Error('An invalid \"rowHeight\" prop has been specified. ' + \"Value should be a function. \" + ('\"' + (rowHeight === null ? \"null\" : typeof rowHeight) + '\" was specified.'));\n            }\n        }\n    }\n});\nvar IS_SCROLLING_DEBOUNCE_INTERVAL$1 = 150;\nvar defaultItemKey$1 = function defaultItemKey(index, data) {\n    return index;\n}; // In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nvar devWarningsDirection = null;\nvar devWarningsTagName$1 = null;\nif (true) {\n    if (false) {}\n}\nfunction createListComponent(_ref) {\n    var _class;\n    var getItemOffset = _ref.getItemOffset, getEstimatedTotalSize = _ref.getEstimatedTotalSize, getItemSize = _ref.getItemSize, getOffsetForIndexAndAlignment = _ref.getOffsetForIndexAndAlignment, getStartIndexForOffset = _ref.getStartIndexForOffset, getStopIndexForStartIndex = _ref.getStopIndexForStartIndex, initInstanceProps = _ref.initInstanceProps, shouldResetStyleCacheOnItemSizeChange = _ref.shouldResetStyleCacheOnItemSizeChange, validateProps = _ref.validateProps;\n    return _class = /*#__PURE__*/ function(_PureComponent) {\n        (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(List, _PureComponent);\n        // Always use explicit constructor for React components.\n        // It produces less code after transpilation. (#26)\n        // eslint-disable-next-line no-useless-constructor\n        function List(props) {\n            var _this;\n            _this = _PureComponent.call(this, props) || this;\n            _this._instanceProps = initInstanceProps(_this.props, (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this));\n            _this._outerRef = void 0;\n            _this._resetIsScrollingTimeoutId = null;\n            _this.state = {\n                instance: (0,_babel_runtime_helpers_esm_assertThisInitialized__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_this),\n                isScrolling: false,\n                scrollDirection: \"forward\",\n                scrollOffset: typeof _this.props.initialScrollOffset === \"number\" ? _this.props.initialScrollOffset : 0,\n                scrollUpdateWasRequested: false\n            };\n            _this._callOnItemsRendered = void 0;\n            _this._callOnItemsRendered = (0,memoize_one__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function(overscanStartIndex, overscanStopIndex, visibleStartIndex, visibleStopIndex) {\n                return _this.props.onItemsRendered({\n                    overscanStartIndex: overscanStartIndex,\n                    overscanStopIndex: overscanStopIndex,\n                    visibleStartIndex: visibleStartIndex,\n                    visibleStopIndex: visibleStopIndex\n                });\n            });\n            _this._callOnScroll = void 0;\n            _this._callOnScroll = (0,memoize_one__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function(scrollDirection, scrollOffset, scrollUpdateWasRequested) {\n                return _this.props.onScroll({\n                    scrollDirection: scrollDirection,\n                    scrollOffset: scrollOffset,\n                    scrollUpdateWasRequested: scrollUpdateWasRequested\n                });\n            });\n            _this._getItemStyle = void 0;\n            _this._getItemStyle = function(index) {\n                var _this$props = _this.props, direction = _this$props.direction, itemSize = _this$props.itemSize, layout = _this$props.layout;\n                var itemStyleCache = _this._getItemStyleCache(shouldResetStyleCacheOnItemSizeChange && itemSize, shouldResetStyleCacheOnItemSizeChange && layout, shouldResetStyleCacheOnItemSizeChange && direction);\n                var style;\n                if (itemStyleCache.hasOwnProperty(index)) {\n                    style = itemStyleCache[index];\n                } else {\n                    var _offset = getItemOffset(_this.props, index, _this._instanceProps);\n                    var size = getItemSize(_this.props, index, _this._instanceProps); // TODO Deprecate direction \"horizontal\"\n                    var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n                    var isRtl = direction === \"rtl\";\n                    var offsetHorizontal = isHorizontal ? _offset : 0;\n                    itemStyleCache[index] = style = {\n                        position: \"absolute\",\n                        left: isRtl ? undefined : offsetHorizontal,\n                        right: isRtl ? offsetHorizontal : undefined,\n                        top: !isHorizontal ? _offset : 0,\n                        height: !isHorizontal ? size : \"100%\",\n                        width: isHorizontal ? size : \"100%\"\n                    };\n                }\n                return style;\n            };\n            _this._getItemStyleCache = void 0;\n            _this._getItemStyleCache = (0,memoize_one__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function(_, __, ___) {\n                return {};\n            });\n            _this._onScrollHorizontal = function(event) {\n                var _event$currentTarget = event.currentTarget, clientWidth = _event$currentTarget.clientWidth, scrollLeft = _event$currentTarget.scrollLeft, scrollWidth = _event$currentTarget.scrollWidth;\n                _this.setState(function(prevState) {\n                    if (prevState.scrollOffset === scrollLeft) {\n                        // Scroll position may have been updated by cDM/cDU,\n                        // In which case we don't need to trigger another render,\n                        // And we don't want to update state.isScrolling.\n                        return null;\n                    }\n                    var direction = _this.props.direction;\n                    var scrollOffset = scrollLeft;\n                    if (direction === \"rtl\") {\n                        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n                        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n                        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n                        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n                        switch(getRTLOffsetType()){\n                            case \"negative\":\n                                scrollOffset = -scrollLeft;\n                                break;\n                            case \"positive-descending\":\n                                scrollOffset = scrollWidth - clientWidth - scrollLeft;\n                                break;\n                        }\n                    } // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n                    scrollOffset = Math.max(0, Math.min(scrollOffset, scrollWidth - clientWidth));\n                    return {\n                        isScrolling: true,\n                        scrollDirection: prevState.scrollOffset < scrollOffset ? \"forward\" : \"backward\",\n                        scrollOffset: scrollOffset,\n                        scrollUpdateWasRequested: false\n                    };\n                }, _this._resetIsScrollingDebounced);\n            };\n            _this._onScrollVertical = function(event) {\n                var _event$currentTarget2 = event.currentTarget, clientHeight = _event$currentTarget2.clientHeight, scrollHeight = _event$currentTarget2.scrollHeight, scrollTop = _event$currentTarget2.scrollTop;\n                _this.setState(function(prevState) {\n                    if (prevState.scrollOffset === scrollTop) {\n                        // Scroll position may have been updated by cDM/cDU,\n                        // In which case we don't need to trigger another render,\n                        // And we don't want to update state.isScrolling.\n                        return null;\n                    } // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n                    var scrollOffset = Math.max(0, Math.min(scrollTop, scrollHeight - clientHeight));\n                    return {\n                        isScrolling: true,\n                        scrollDirection: prevState.scrollOffset < scrollOffset ? \"forward\" : \"backward\",\n                        scrollOffset: scrollOffset,\n                        scrollUpdateWasRequested: false\n                    };\n                }, _this._resetIsScrollingDebounced);\n            };\n            _this._outerRefSetter = function(ref) {\n                var outerRef = _this.props.outerRef;\n                _this._outerRef = ref;\n                if (typeof outerRef === \"function\") {\n                    outerRef(ref);\n                } else if (outerRef != null && typeof outerRef === \"object\" && outerRef.hasOwnProperty(\"current\")) {\n                    outerRef.current = ref;\n                }\n            };\n            _this._resetIsScrollingDebounced = function() {\n                if (_this._resetIsScrollingTimeoutId !== null) {\n                    cancelTimeout(_this._resetIsScrollingTimeoutId);\n                }\n                _this._resetIsScrollingTimeoutId = requestTimeout(_this._resetIsScrolling, IS_SCROLLING_DEBOUNCE_INTERVAL$1);\n            };\n            _this._resetIsScrolling = function() {\n                _this._resetIsScrollingTimeoutId = null;\n                _this.setState({\n                    isScrolling: false\n                }, function() {\n                    // Clear style cache after state update has been committed.\n                    // This way we don't break pure sCU for items that don't use isScrolling param.\n                    _this._getItemStyleCache(-1, null);\n                });\n            };\n            return _this;\n        }\n        List.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, prevState) {\n            validateSharedProps$1(nextProps, prevState);\n            validateProps(nextProps);\n            return null;\n        };\n        var _proto = List.prototype;\n        _proto.scrollTo = function scrollTo(scrollOffset) {\n            scrollOffset = Math.max(0, scrollOffset);\n            this.setState(function(prevState) {\n                if (prevState.scrollOffset === scrollOffset) {\n                    return null;\n                }\n                return {\n                    scrollDirection: prevState.scrollOffset < scrollOffset ? \"forward\" : \"backward\",\n                    scrollOffset: scrollOffset,\n                    scrollUpdateWasRequested: true\n                };\n            }, this._resetIsScrollingDebounced);\n        };\n        _proto.scrollToItem = function scrollToItem(index, align) {\n            if (align === void 0) {\n                align = \"auto\";\n            }\n            var _this$props2 = this.props, itemCount = _this$props2.itemCount, layout = _this$props2.layout;\n            var scrollOffset = this.state.scrollOffset;\n            index = Math.max(0, Math.min(index, itemCount - 1)); // The scrollbar size should be considered when scrolling an item into view, to ensure it's fully visible.\n            // But we only need to account for its size when it's actually visible.\n            // This is an edge case for lists; normally they only scroll in the dominant direction.\n            var scrollbarSize = 0;\n            if (this._outerRef) {\n                var outerRef = this._outerRef;\n                if (layout === \"vertical\") {\n                    scrollbarSize = outerRef.scrollWidth > outerRef.clientWidth ? getScrollbarSize() : 0;\n                } else {\n                    scrollbarSize = outerRef.scrollHeight > outerRef.clientHeight ? getScrollbarSize() : 0;\n                }\n            }\n            this.scrollTo(getOffsetForIndexAndAlignment(this.props, index, align, scrollOffset, this._instanceProps, scrollbarSize));\n        };\n        _proto.componentDidMount = function componentDidMount() {\n            var _this$props3 = this.props, direction = _this$props3.direction, initialScrollOffset = _this$props3.initialScrollOffset, layout = _this$props3.layout;\n            if (typeof initialScrollOffset === \"number\" && this._outerRef != null) {\n                var outerRef = this._outerRef; // TODO Deprecate direction \"horizontal\"\n                if (direction === \"horizontal\" || layout === \"horizontal\") {\n                    outerRef.scrollLeft = initialScrollOffset;\n                } else {\n                    outerRef.scrollTop = initialScrollOffset;\n                }\n            }\n            this._callPropsCallbacks();\n        };\n        _proto.componentDidUpdate = function componentDidUpdate() {\n            var _this$props4 = this.props, direction = _this$props4.direction, layout = _this$props4.layout;\n            var _this$state = this.state, scrollOffset = _this$state.scrollOffset, scrollUpdateWasRequested = _this$state.scrollUpdateWasRequested;\n            if (scrollUpdateWasRequested && this._outerRef != null) {\n                var outerRef = this._outerRef; // TODO Deprecate direction \"horizontal\"\n                if (direction === \"horizontal\" || layout === \"horizontal\") {\n                    if (direction === \"rtl\") {\n                        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n                        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n                        // So we need to determine which browser behavior we're dealing with, and mimic it.\n                        switch(getRTLOffsetType()){\n                            case \"negative\":\n                                outerRef.scrollLeft = -scrollOffset;\n                                break;\n                            case \"positive-ascending\":\n                                outerRef.scrollLeft = scrollOffset;\n                                break;\n                            default:\n                                var clientWidth = outerRef.clientWidth, scrollWidth = outerRef.scrollWidth;\n                                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                                break;\n                        }\n                    } else {\n                        outerRef.scrollLeft = scrollOffset;\n                    }\n                } else {\n                    outerRef.scrollTop = scrollOffset;\n                }\n            }\n            this._callPropsCallbacks();\n        };\n        _proto.componentWillUnmount = function componentWillUnmount() {\n            if (this._resetIsScrollingTimeoutId !== null) {\n                cancelTimeout(this._resetIsScrollingTimeoutId);\n            }\n        };\n        _proto.render = function render() {\n            var _this$props5 = this.props, children = _this$props5.children, className = _this$props5.className, direction = _this$props5.direction, height = _this$props5.height, innerRef = _this$props5.innerRef, innerElementType = _this$props5.innerElementType, innerTagName = _this$props5.innerTagName, itemCount = _this$props5.itemCount, itemData = _this$props5.itemData, _this$props5$itemKey = _this$props5.itemKey, itemKey = _this$props5$itemKey === void 0 ? defaultItemKey$1 : _this$props5$itemKey, layout = _this$props5.layout, outerElementType = _this$props5.outerElementType, outerTagName = _this$props5.outerTagName, style = _this$props5.style, useIsScrolling = _this$props5.useIsScrolling, width = _this$props5.width;\n            var isScrolling = this.state.isScrolling; // TODO Deprecate direction \"horizontal\"\n            var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n            var onScroll = isHorizontal ? this._onScrollHorizontal : this._onScrollVertical;\n            var _this$_getRangeToRend = this._getRangeToRender(), startIndex = _this$_getRangeToRend[0], stopIndex = _this$_getRangeToRend[1];\n            var items = [];\n            if (itemCount > 0) {\n                for(var _index = startIndex; _index <= stopIndex; _index++){\n                    items.push(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createElement)(children, {\n                        data: itemData,\n                        key: itemKey(_index, itemData),\n                        index: _index,\n                        isScrolling: useIsScrolling ? isScrolling : undefined,\n                        style: this._getItemStyle(_index)\n                    }));\n                }\n            } // Read this value AFTER items have been created,\n            // So their actual sizes (if variable) are taken into consideration.\n            var estimatedTotalSize = getEstimatedTotalSize(this.props, this._instanceProps);\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createElement)(outerElementType || outerTagName || \"div\", {\n                className: className,\n                onScroll: onScroll,\n                ref: this._outerRefSetter,\n                style: (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n                    position: \"relative\",\n                    height: height,\n                    width: width,\n                    overflow: \"auto\",\n                    WebkitOverflowScrolling: \"touch\",\n                    willChange: \"transform\",\n                    direction: direction\n                }, style)\n            }, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_3__.createElement)(innerElementType || innerTagName || \"div\", {\n                children: items,\n                ref: innerRef,\n                style: {\n                    height: isHorizontal ? \"100%\" : estimatedTotalSize,\n                    pointerEvents: isScrolling ? \"none\" : undefined,\n                    width: isHorizontal ? estimatedTotalSize : \"100%\"\n                }\n            }));\n        };\n        _proto._callPropsCallbacks = function _callPropsCallbacks() {\n            if (typeof this.props.onItemsRendered === \"function\") {\n                var itemCount = this.props.itemCount;\n                if (itemCount > 0) {\n                    var _this$_getRangeToRend2 = this._getRangeToRender(), _overscanStartIndex = _this$_getRangeToRend2[0], _overscanStopIndex = _this$_getRangeToRend2[1], _visibleStartIndex = _this$_getRangeToRend2[2], _visibleStopIndex = _this$_getRangeToRend2[3];\n                    this._callOnItemsRendered(_overscanStartIndex, _overscanStopIndex, _visibleStartIndex, _visibleStopIndex);\n                }\n            }\n            if (typeof this.props.onScroll === \"function\") {\n                var _this$state2 = this.state, _scrollDirection = _this$state2.scrollDirection, _scrollOffset = _this$state2.scrollOffset, _scrollUpdateWasRequested = _this$state2.scrollUpdateWasRequested;\n                this._callOnScroll(_scrollDirection, _scrollOffset, _scrollUpdateWasRequested);\n            }\n        } // Lazily create and cache item styles while scrolling,\n        ;\n        _proto._getRangeToRender = function _getRangeToRender() {\n            var _this$props6 = this.props, itemCount = _this$props6.itemCount, overscanCount = _this$props6.overscanCount;\n            var _this$state3 = this.state, isScrolling = _this$state3.isScrolling, scrollDirection = _this$state3.scrollDirection, scrollOffset = _this$state3.scrollOffset;\n            if (itemCount === 0) {\n                return [\n                    0,\n                    0,\n                    0,\n                    0\n                ];\n            }\n            var startIndex = getStartIndexForOffset(this.props, scrollOffset, this._instanceProps);\n            var stopIndex = getStopIndexForStartIndex(this.props, startIndex, scrollOffset, this._instanceProps); // Overscan by one item in each direction so that tab/focus works.\n            // If there isn't at least one extra item, tab loops back around.\n            var overscanBackward = !isScrolling || scrollDirection === \"backward\" ? Math.max(1, overscanCount) : 1;\n            var overscanForward = !isScrolling || scrollDirection === \"forward\" ? Math.max(1, overscanCount) : 1;\n            return [\n                Math.max(0, startIndex - overscanBackward),\n                Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n                startIndex,\n                stopIndex\n            ];\n        };\n        return List;\n    }(react__WEBPACK_IMPORTED_MODULE_3__.PureComponent), _class.defaultProps = {\n        direction: \"ltr\",\n        itemData: undefined,\n        layout: \"vertical\",\n        overscanCount: 2,\n        useIsScrolling: false\n    }, _class;\n} // NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\nvar validateSharedProps$1 = function validateSharedProps(_ref2, _ref3) {\n    var children = _ref2.children, direction = _ref2.direction, height = _ref2.height, layout = _ref2.layout, innerTagName = _ref2.innerTagName, outerTagName = _ref2.outerTagName, width = _ref2.width;\n    var instance = _ref3.instance;\n    if (true) {\n        if (innerTagName != null || outerTagName != null) {\n            if (devWarningsTagName$1 && !devWarningsTagName$1.has(instance)) {\n                devWarningsTagName$1.add(instance);\n                console.warn(\"The innerTagName and outerTagName props have been deprecated. \" + \"Please use the innerElementType and outerElementType props instead.\");\n            }\n        } // TODO Deprecate direction \"horizontal\"\n        var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n        switch(direction){\n            case \"horizontal\":\n            case \"vertical\":\n                if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n                    devWarningsDirection.add(instance);\n                    console.warn('The direction prop should be either \"ltr\" (default) or \"rtl\". ' + 'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.');\n                }\n                break;\n            case \"ltr\":\n            case \"rtl\":\n                break;\n            default:\n                throw Error('An invalid \"direction\" prop has been specified. ' + 'Value should be either \"ltr\" or \"rtl\". ' + ('\"' + direction + '\" was specified.'));\n        }\n        switch(layout){\n            case \"horizontal\":\n            case \"vertical\":\n                break;\n            default:\n                throw Error('An invalid \"layout\" prop has been specified. ' + 'Value should be either \"horizontal\" or \"vertical\". ' + ('\"' + layout + '\" was specified.'));\n        }\n        if (children == null) {\n            throw Error('An invalid \"children\" prop has been specified. ' + \"Value should be a React component. \" + ('\"' + (children === null ? \"null\" : typeof children) + '\" was specified.'));\n        }\n        if (isHorizontal && typeof width !== \"number\") {\n            throw Error('An invalid \"width\" prop has been specified. ' + \"Horizontal lists must specify a number for width. \" + ('\"' + (width === null ? \"null\" : typeof width) + '\" was specified.'));\n        } else if (!isHorizontal && typeof height !== \"number\") {\n            throw Error('An invalid \"height\" prop has been specified. ' + \"Vertical lists must specify a number for height. \" + ('\"' + (height === null ? \"null\" : typeof height) + '\" was specified.'));\n        }\n    }\n};\nvar DEFAULT_ESTIMATED_ITEM_SIZE$1 = 50;\nvar getItemMetadata$1 = function getItemMetadata(props, index, instanceProps) {\n    var _ref = props, itemSize = _ref.itemSize;\n    var itemMetadataMap = instanceProps.itemMetadataMap, lastMeasuredIndex = instanceProps.lastMeasuredIndex;\n    if (index > lastMeasuredIndex) {\n        var offset = 0;\n        if (lastMeasuredIndex >= 0) {\n            var itemMetadata = itemMetadataMap[lastMeasuredIndex];\n            offset = itemMetadata.offset + itemMetadata.size;\n        }\n        for(var i = lastMeasuredIndex + 1; i <= index; i++){\n            var size = itemSize(i);\n            itemMetadataMap[i] = {\n                offset: offset,\n                size: size\n            };\n            offset += size;\n        }\n        instanceProps.lastMeasuredIndex = index;\n    }\n    return itemMetadataMap[index];\n};\nvar findNearestItem$1 = function findNearestItem(props, instanceProps, offset) {\n    var itemMetadataMap = instanceProps.itemMetadataMap, lastMeasuredIndex = instanceProps.lastMeasuredIndex;\n    var lastMeasuredItemOffset = lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n    if (lastMeasuredItemOffset >= offset) {\n        // If we've already measured items within this range just use a binary search as it's faster.\n        return findNearestItemBinarySearch$1(props, instanceProps, lastMeasuredIndex, 0, offset);\n    } else {\n        // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n        // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n        // The overall complexity for this approach is O(log n).\n        return findNearestItemExponentialSearch$1(props, instanceProps, Math.max(0, lastMeasuredIndex), offset);\n    }\n};\nvar findNearestItemBinarySearch$1 = function findNearestItemBinarySearch(props, instanceProps, high, low, offset) {\n    while(low <= high){\n        var middle = low + Math.floor((high - low) / 2);\n        var currentOffset = getItemMetadata$1(props, middle, instanceProps).offset;\n        if (currentOffset === offset) {\n            return middle;\n        } else if (currentOffset < offset) {\n            low = middle + 1;\n        } else if (currentOffset > offset) {\n            high = middle - 1;\n        }\n    }\n    if (low > 0) {\n        return low - 1;\n    } else {\n        return 0;\n    }\n};\nvar findNearestItemExponentialSearch$1 = function findNearestItemExponentialSearch(props, instanceProps, index, offset) {\n    var itemCount = props.itemCount;\n    var interval = 1;\n    while(index < itemCount && getItemMetadata$1(props, index, instanceProps).offset < offset){\n        index += interval;\n        interval *= 2;\n    }\n    return findNearestItemBinarySearch$1(props, instanceProps, Math.min(index, itemCount - 1), Math.floor(index / 2), offset);\n};\nvar getEstimatedTotalSize = function getEstimatedTotalSize(_ref2, _ref3) {\n    var itemCount = _ref2.itemCount;\n    var itemMetadataMap = _ref3.itemMetadataMap, estimatedItemSize = _ref3.estimatedItemSize, lastMeasuredIndex = _ref3.lastMeasuredIndex;\n    var totalSizeOfMeasuredItems = 0; // Edge case check for when the number of items decreases while a scroll is in progress.\n    // https://github.com/bvaughn/react-window/pull/138\n    if (lastMeasuredIndex >= itemCount) {\n        lastMeasuredIndex = itemCount - 1;\n    }\n    if (lastMeasuredIndex >= 0) {\n        var itemMetadata = itemMetadataMap[lastMeasuredIndex];\n        totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n    }\n    var numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n    var totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n    return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\nvar VariableSizeList = /*#__PURE__*/ createListComponent({\n    getItemOffset: function getItemOffset(props, index, instanceProps) {\n        return getItemMetadata$1(props, index, instanceProps).offset;\n    },\n    getItemSize: function getItemSize(props, index, instanceProps) {\n        return instanceProps.itemMetadataMap[index].size;\n    },\n    getEstimatedTotalSize: getEstimatedTotalSize,\n    getOffsetForIndexAndAlignment: function getOffsetForIndexAndAlignment(props, index, align, scrollOffset, instanceProps, scrollbarSize) {\n        var direction = props.direction, height = props.height, layout = props.layout, width = props.width; // TODO Deprecate direction \"horizontal\"\n        var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n        var size = isHorizontal ? width : height;\n        var itemMetadata = getItemMetadata$1(props, index, instanceProps); // Get estimated total size after ItemMetadata is computed,\n        // To ensure it reflects actual measurements instead of just estimates.\n        var estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n        var maxOffset = Math.max(0, Math.min(estimatedTotalSize - size, itemMetadata.offset));\n        var minOffset = Math.max(0, itemMetadata.offset - size + itemMetadata.size + scrollbarSize);\n        if (align === \"smart\") {\n            if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n                align = \"auto\";\n            } else {\n                align = \"center\";\n            }\n        }\n        switch(align){\n            case \"start\":\n                return maxOffset;\n            case \"end\":\n                return minOffset;\n            case \"center\":\n                return Math.round(minOffset + (maxOffset - minOffset) / 2);\n            case \"auto\":\n            default:\n                if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n                    return scrollOffset;\n                } else if (scrollOffset < minOffset) {\n                    return minOffset;\n                } else {\n                    return maxOffset;\n                }\n        }\n    },\n    getStartIndexForOffset: function getStartIndexForOffset(props, offset, instanceProps) {\n        return findNearestItem$1(props, instanceProps, offset);\n    },\n    getStopIndexForStartIndex: function getStopIndexForStartIndex(props, startIndex, scrollOffset, instanceProps) {\n        var direction = props.direction, height = props.height, itemCount = props.itemCount, layout = props.layout, width = props.width; // TODO Deprecate direction \"horizontal\"\n        var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n        var size = isHorizontal ? width : height;\n        var itemMetadata = getItemMetadata$1(props, startIndex, instanceProps);\n        var maxOffset = scrollOffset + size;\n        var offset = itemMetadata.offset + itemMetadata.size;\n        var stopIndex = startIndex;\n        while(stopIndex < itemCount - 1 && offset < maxOffset){\n            stopIndex++;\n            offset += getItemMetadata$1(props, stopIndex, instanceProps).size;\n        }\n        return stopIndex;\n    },\n    initInstanceProps: function initInstanceProps(props, instance) {\n        var _ref4 = props, estimatedItemSize = _ref4.estimatedItemSize;\n        var instanceProps = {\n            itemMetadataMap: {},\n            estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE$1,\n            lastMeasuredIndex: -1\n        };\n        instance.resetAfterIndex = function(index, shouldForceUpdate) {\n            if (shouldForceUpdate === void 0) {\n                shouldForceUpdate = true;\n            }\n            instanceProps.lastMeasuredIndex = Math.min(instanceProps.lastMeasuredIndex, index - 1); // We could potentially optimize further by only evicting styles after this index,\n            // But since styles are only cached while scrolling is in progress-\n            // It seems an unnecessary optimization.\n            // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n            instance._getItemStyleCache(-1);\n            if (shouldForceUpdate) {\n                instance.forceUpdate();\n            }\n        };\n        return instanceProps;\n    },\n    shouldResetStyleCacheOnItemSizeChange: false,\n    validateProps: function validateProps(_ref5) {\n        var itemSize = _ref5.itemSize;\n        if (true) {\n            if (typeof itemSize !== \"function\") {\n                throw Error('An invalid \"itemSize\" prop has been specified. ' + \"Value should be a function. \" + ('\"' + (itemSize === null ? \"null\" : typeof itemSize) + '\" was specified.'));\n            }\n        }\n    }\n});\nvar FixedSizeGrid = /*#__PURE__*/ createGridComponent({\n    getColumnOffset: function getColumnOffset(_ref, index) {\n        var columnWidth = _ref.columnWidth;\n        return index * columnWidth;\n    },\n    getColumnWidth: function getColumnWidth(_ref2, index) {\n        var columnWidth = _ref2.columnWidth;\n        return columnWidth;\n    },\n    getRowOffset: function getRowOffset(_ref3, index) {\n        var rowHeight = _ref3.rowHeight;\n        return index * rowHeight;\n    },\n    getRowHeight: function getRowHeight(_ref4, index) {\n        var rowHeight = _ref4.rowHeight;\n        return rowHeight;\n    },\n    getEstimatedTotalHeight: function getEstimatedTotalHeight(_ref5) {\n        var rowCount = _ref5.rowCount, rowHeight = _ref5.rowHeight;\n        return rowHeight * rowCount;\n    },\n    getEstimatedTotalWidth: function getEstimatedTotalWidth(_ref6) {\n        var columnCount = _ref6.columnCount, columnWidth = _ref6.columnWidth;\n        return columnWidth * columnCount;\n    },\n    getOffsetForColumnAndAlignment: function getOffsetForColumnAndAlignment(_ref7, columnIndex, align, scrollLeft, instanceProps, scrollbarSize) {\n        var columnCount = _ref7.columnCount, columnWidth = _ref7.columnWidth, width = _ref7.width;\n        var lastColumnOffset = Math.max(0, columnCount * columnWidth - width);\n        var maxOffset = Math.min(lastColumnOffset, columnIndex * columnWidth);\n        var minOffset = Math.max(0, columnIndex * columnWidth - width + scrollbarSize + columnWidth);\n        if (align === \"smart\") {\n            if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n                align = \"auto\";\n            } else {\n                align = \"center\";\n            }\n        }\n        switch(align){\n            case \"start\":\n                return maxOffset;\n            case \"end\":\n                return minOffset;\n            case \"center\":\n                // \"Centered\" offset is usually the average of the min and max.\n                // But near the edges of the list, this doesn't hold true.\n                var middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2);\n                if (middleOffset < Math.ceil(width / 2)) {\n                    return 0; // near the beginning\n                } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n                    return lastColumnOffset; // near the end\n                } else {\n                    return middleOffset;\n                }\n            case \"auto\":\n            default:\n                if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n                    return scrollLeft;\n                } else if (minOffset > maxOffset) {\n                    // Because we only take into account the scrollbar size when calculating minOffset\n                    // this value can be larger than maxOffset when at the end of the list\n                    return minOffset;\n                } else if (scrollLeft < minOffset) {\n                    return minOffset;\n                } else {\n                    return maxOffset;\n                }\n        }\n    },\n    getOffsetForRowAndAlignment: function getOffsetForRowAndAlignment(_ref8, rowIndex, align, scrollTop, instanceProps, scrollbarSize) {\n        var rowHeight = _ref8.rowHeight, height = _ref8.height, rowCount = _ref8.rowCount;\n        var lastRowOffset = Math.max(0, rowCount * rowHeight - height);\n        var maxOffset = Math.min(lastRowOffset, rowIndex * rowHeight);\n        var minOffset = Math.max(0, rowIndex * rowHeight - height + scrollbarSize + rowHeight);\n        if (align === \"smart\") {\n            if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n                align = \"auto\";\n            } else {\n                align = \"center\";\n            }\n        }\n        switch(align){\n            case \"start\":\n                return maxOffset;\n            case \"end\":\n                return minOffset;\n            case \"center\":\n                // \"Centered\" offset is usually the average of the min and max.\n                // But near the edges of the list, this doesn't hold true.\n                var middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2);\n                if (middleOffset < Math.ceil(height / 2)) {\n                    return 0; // near the beginning\n                } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n                    return lastRowOffset; // near the end\n                } else {\n                    return middleOffset;\n                }\n            case \"auto\":\n            default:\n                if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n                    return scrollTop;\n                } else if (minOffset > maxOffset) {\n                    // Because we only take into account the scrollbar size when calculating minOffset\n                    // this value can be larger than maxOffset when at the end of the list\n                    return minOffset;\n                } else if (scrollTop < minOffset) {\n                    return minOffset;\n                } else {\n                    return maxOffset;\n                }\n        }\n    },\n    getColumnStartIndexForOffset: function getColumnStartIndexForOffset(_ref9, scrollLeft) {\n        var columnWidth = _ref9.columnWidth, columnCount = _ref9.columnCount;\n        return Math.max(0, Math.min(columnCount - 1, Math.floor(scrollLeft / columnWidth)));\n    },\n    getColumnStopIndexForStartIndex: function getColumnStopIndexForStartIndex(_ref10, startIndex, scrollLeft) {\n        var columnWidth = _ref10.columnWidth, columnCount = _ref10.columnCount, width = _ref10.width;\n        var left = startIndex * columnWidth;\n        var numVisibleColumns = Math.ceil((width + scrollLeft - left) / columnWidth);\n        return Math.max(0, Math.min(columnCount - 1, startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n        ));\n    },\n    getRowStartIndexForOffset: function getRowStartIndexForOffset(_ref11, scrollTop) {\n        var rowHeight = _ref11.rowHeight, rowCount = _ref11.rowCount;\n        return Math.max(0, Math.min(rowCount - 1, Math.floor(scrollTop / rowHeight)));\n    },\n    getRowStopIndexForStartIndex: function getRowStopIndexForStartIndex(_ref12, startIndex, scrollTop) {\n        var rowHeight = _ref12.rowHeight, rowCount = _ref12.rowCount, height = _ref12.height;\n        var top = startIndex * rowHeight;\n        var numVisibleRows = Math.ceil((height + scrollTop - top) / rowHeight);\n        return Math.max(0, Math.min(rowCount - 1, startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n        ));\n    },\n    initInstanceProps: function initInstanceProps(props) {},\n    shouldResetStyleCacheOnItemSizeChange: true,\n    validateProps: function validateProps(_ref13) {\n        var columnWidth = _ref13.columnWidth, rowHeight = _ref13.rowHeight;\n        if (true) {\n            if (typeof columnWidth !== \"number\") {\n                throw Error('An invalid \"columnWidth\" prop has been specified. ' + \"Value should be a number. \" + ('\"' + (columnWidth === null ? \"null\" : typeof columnWidth) + '\" was specified.'));\n            }\n            if (typeof rowHeight !== \"number\") {\n                throw Error('An invalid \"rowHeight\" prop has been specified. ' + \"Value should be a number. \" + ('\"' + (rowHeight === null ? \"null\" : typeof rowHeight) + '\" was specified.'));\n            }\n        }\n    }\n});\nvar FixedSizeList = /*#__PURE__*/ createListComponent({\n    getItemOffset: function getItemOffset(_ref, index) {\n        var itemSize = _ref.itemSize;\n        return index * itemSize;\n    },\n    getItemSize: function getItemSize(_ref2, index) {\n        var itemSize = _ref2.itemSize;\n        return itemSize;\n    },\n    getEstimatedTotalSize: function getEstimatedTotalSize(_ref3) {\n        var itemCount = _ref3.itemCount, itemSize = _ref3.itemSize;\n        return itemSize * itemCount;\n    },\n    getOffsetForIndexAndAlignment: function getOffsetForIndexAndAlignment(_ref4, index, align, scrollOffset, instanceProps, scrollbarSize) {\n        var direction = _ref4.direction, height = _ref4.height, itemCount = _ref4.itemCount, itemSize = _ref4.itemSize, layout = _ref4.layout, width = _ref4.width;\n        // TODO Deprecate direction \"horizontal\"\n        var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n        var size = isHorizontal ? width : height;\n        var lastItemOffset = Math.max(0, itemCount * itemSize - size);\n        var maxOffset = Math.min(lastItemOffset, index * itemSize);\n        var minOffset = Math.max(0, index * itemSize - size + itemSize + scrollbarSize);\n        if (align === \"smart\") {\n            if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n                align = \"auto\";\n            } else {\n                align = \"center\";\n            }\n        }\n        switch(align){\n            case \"start\":\n                return maxOffset;\n            case \"end\":\n                return minOffset;\n            case \"center\":\n                {\n                    // \"Centered\" offset is usually the average of the min and max.\n                    // But near the edges of the list, this doesn't hold true.\n                    var middleOffset = Math.round(minOffset + (maxOffset - minOffset) / 2);\n                    if (middleOffset < Math.ceil(size / 2)) {\n                        return 0; // near the beginning\n                    } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n                        return lastItemOffset; // near the end\n                    } else {\n                        return middleOffset;\n                    }\n                }\n            case \"auto\":\n            default:\n                if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n                    return scrollOffset;\n                } else if (scrollOffset < minOffset) {\n                    return minOffset;\n                } else {\n                    return maxOffset;\n                }\n        }\n    },\n    getStartIndexForOffset: function getStartIndexForOffset(_ref5, offset) {\n        var itemCount = _ref5.itemCount, itemSize = _ref5.itemSize;\n        return Math.max(0, Math.min(itemCount - 1, Math.floor(offset / itemSize)));\n    },\n    getStopIndexForStartIndex: function getStopIndexForStartIndex(_ref6, startIndex, scrollOffset) {\n        var direction = _ref6.direction, height = _ref6.height, itemCount = _ref6.itemCount, itemSize = _ref6.itemSize, layout = _ref6.layout, width = _ref6.width;\n        // TODO Deprecate direction \"horizontal\"\n        var isHorizontal = direction === \"horizontal\" || layout === \"horizontal\";\n        var offset = startIndex * itemSize;\n        var size = isHorizontal ? width : height;\n        var numVisibleItems = Math.ceil((size + scrollOffset - offset) / itemSize);\n        return Math.max(0, Math.min(itemCount - 1, startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n        ));\n    },\n    initInstanceProps: function initInstanceProps(props) {},\n    shouldResetStyleCacheOnItemSizeChange: true,\n    validateProps: function validateProps(_ref7) {\n        var itemSize = _ref7.itemSize;\n        if (true) {\n            if (typeof itemSize !== \"number\") {\n                throw Error('An invalid \"itemSize\" prop has been specified. ' + \"Value should be a number. \" + ('\"' + (itemSize === null ? \"null\" : typeof itemSize) + '\" was specified.'));\n            }\n        }\n    }\n});\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nfunction shallowDiffers(prev, next) {\n    for(var attribute in prev){\n        if (!(attribute in next)) {\n            return true;\n        }\n    }\n    for(var _attribute in next){\n        if (prev[_attribute] !== next[_attribute]) {\n            return true;\n        }\n    }\n    return false;\n}\nvar _excluded = [\n    \"style\"\n], _excluded2 = [\n    \"style\"\n];\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nfunction areEqual(prevProps, nextProps) {\n    var prevStyle = prevProps.style, prevRest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(prevProps, _excluded);\n    var nextStyle = nextProps.style, nextRest = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(nextProps, _excluded2);\n    return !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest);\n}\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nfunction shouldComponentUpdate(nextProps, nextState) {\n    return !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState);\n}\n //# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-window/dist/index.esm.js\n");

/***/ })

};
;