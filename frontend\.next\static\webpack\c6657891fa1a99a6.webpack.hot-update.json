{"c": ["app/layout", "app/chat/[id]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/config/index.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/index.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js", "(app-pages-browser)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js", "(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js", "(app-pages-browser)/./node_modules/state-local/lib/es/state-local.js"]}