pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Default Dark
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme default-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #181818  Default Background
base01  #282828  Lighter Background (Used for status bars, line number and folding marks)
base02  #383838  Selection Background
base03  #585858  Comments, Invisibles, Line Highlighting
base04  #b8b8b8  Dark Foreground (Used for status bars)
base05  #d8d8d8  Default Foreground, Caret, Delimiters, Operators
base06  #e8e8e8  Light Foreground (Not often used)
base07  #f8f8f8  Light Background (Not often used)
base08  #ab4642  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #dc9656  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f7ca88  Classes, Markup Bold, Search Text Background
base0B  #a1b56c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #86c1b9  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #7cafc2  Functions, Methods, Attribute IDs, Headings
base0E  #ba8baf  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #a16946  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #d8d8d8;
  background: #181818
}
.hljs::selection,
.hljs ::selection {
  background-color: #383838;
  color: #d8d8d8
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #585858 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #585858
}
/* base04 - #b8b8b8 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b8b8b8
}
/* base05 - #d8d8d8 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #d8d8d8
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ab4642
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #dc9656
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f7ca88
}
.hljs-strong {
  font-weight: bold;
  color: #f7ca88
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #a1b56c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #86c1b9
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #7cafc2
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ba8baf
}
.hljs-emphasis {
  color: #ba8baf;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #a16946
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}