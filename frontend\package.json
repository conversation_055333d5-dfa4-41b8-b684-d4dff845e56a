{"name": "codora-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "@tailwindcss/typography": "^0.5.10", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-switch": "^1.0.3", "axios": "^1.6.2", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "tailwind-merge": "^2.0.0", "@monaco-editor/react": "^4.6.0", "react-resizable-panels": "^0.0.55", "react-arborist": "^3.4.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "highlight.js": "^11.9.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.0.4"}}