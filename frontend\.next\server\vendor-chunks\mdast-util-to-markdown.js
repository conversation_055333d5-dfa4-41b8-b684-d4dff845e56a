"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function blockquote(node, _, state, info) {\n    const exit = state.enter(\"blockquote\");\n    const tracker = state.createTracker(info);\n    tracker.move(\"> \");\n    tracker.shift(2);\n    const value = state.indentLines(state.containerFlow(node, tracker.current()), map);\n    exit();\n    return value;\n}\n/** @type {Map} */ function map(line, _, blank) {\n    return \">\" + (blank ? \"\" : \" \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */ \n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function hardBreak(_, _1, state, info) {\n    let index = -1;\n    while(++index < state.unsafe.length){\n        // If we can’t put eols in this construct (setext headings, tables), use a\n        // space instead.\n        if (state.unsafe[index].character === \"\\n\" && (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])) {\n            return /[ \\t]/.test(info.before) ? \"\" : \" \";\n        }\n    }\n    return \"\\\\\\n\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7OztDQUdDLEdBRXlEO0FBRTFEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNDLFVBQVVDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxLQUFLLEVBQUVDLElBQUk7SUFDMUMsSUFBSUMsUUFBUSxDQUFDO0lBRWIsTUFBTyxFQUFFQSxRQUFRRixNQUFNRyxNQUFNLENBQUNDLE1BQU0sQ0FBRTtRQUNwQywwRUFBMEU7UUFDMUUsaUJBQWlCO1FBQ2pCLElBQ0VKLE1BQU1HLE1BQU0sQ0FBQ0QsTUFBTSxDQUFDRyxTQUFTLEtBQUssUUFDbENULHlFQUFjQSxDQUFDSSxNQUFNTSxLQUFLLEVBQUVOLE1BQU1HLE1BQU0sQ0FBQ0QsTUFBTSxHQUMvQztZQUNBLE9BQU8sUUFBUUssSUFBSSxDQUFDTixLQUFLTyxNQUFNLElBQUksS0FBSztRQUMxQztJQUNGO0lBRUEsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9icmVhay5qcz9jMzM3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QnJlYWssIFBhcmVudHN9IGZyb20gJ21kYXN0J1xuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuaW1wb3J0IHtwYXR0ZXJuSW5TY29wZX0gZnJvbSAnLi4vdXRpbC9wYXR0ZXJuLWluLXNjb3BlLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7QnJlYWt9IF9cbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gXzFcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXJkQnJlYWsoXywgXzEsIHN0YXRlLCBpbmZvKSB7XG4gIGxldCBpbmRleCA9IC0xXG5cbiAgd2hpbGUgKCsraW5kZXggPCBzdGF0ZS51bnNhZmUubGVuZ3RoKSB7XG4gICAgLy8gSWYgd2UgY2Fu4oCZdCBwdXQgZW9scyBpbiB0aGlzIGNvbnN0cnVjdCAoc2V0ZXh0IGhlYWRpbmdzLCB0YWJsZXMpLCB1c2UgYVxuICAgIC8vIHNwYWNlIGluc3RlYWQuXG4gICAgaWYgKFxuICAgICAgc3RhdGUudW5zYWZlW2luZGV4XS5jaGFyYWN0ZXIgPT09ICdcXG4nICYmXG4gICAgICBwYXR0ZXJuSW5TY29wZShzdGF0ZS5zdGFjaywgc3RhdGUudW5zYWZlW2luZGV4XSlcbiAgICApIHtcbiAgICAgIHJldHVybiAvWyBcXHRdLy50ZXN0KGluZm8uYmVmb3JlKSA/ICcnIDogJyAnXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuICdcXFxcXFxuJ1xufVxuIl0sIm5hbWVzIjpbInBhdHRlcm5JblNjb3BlIiwiaGFyZEJyZWFrIiwiXyIsIl8xIiwic3RhdGUiLCJpbmZvIiwiaW5kZXgiLCJ1bnNhZmUiLCJsZW5ndGgiLCJjaGFyYWN0ZXIiLCJzdGFjayIsInRlc3QiLCJiZWZvcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(ssr)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */ \n\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function code(node, _, state, info) {\n    const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state);\n    const raw = node.value || \"\";\n    const suffix = marker === \"`\" ? \"GraveAccent\" : \"Tilde\";\n    if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n        const exit = state.enter(\"codeIndented\");\n        const value = state.indentLines(raw, map);\n        exit();\n        return value;\n    }\n    const tracker = state.createTracker(info);\n    const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3));\n    const exit = state.enter(\"codeFenced\");\n    let value = tracker.move(sequence);\n    if (node.lang) {\n        const subexit = state.enter(`codeFencedLang${suffix}`);\n        value += tracker.move(state.safe(node.lang, {\n            before: value,\n            after: \" \",\n            encode: [\n                \"`\"\n            ],\n            ...tracker.current()\n        }));\n        subexit();\n    }\n    if (node.lang && node.meta) {\n        const subexit = state.enter(`codeFencedMeta${suffix}`);\n        value += tracker.move(\" \");\n        value += tracker.move(state.safe(node.meta, {\n            before: value,\n            after: \"\\n\",\n            encode: [\n                \"`\"\n            ],\n            ...tracker.current()\n        }));\n        subexit();\n    }\n    value += tracker.move(\"\\n\");\n    if (raw) {\n        value += tracker.move(raw + \"\\n\");\n    }\n    value += tracker.move(sequence);\n    exit();\n    return value;\n}\n/** @type {Map} */ function map(line, _, blank) {\n    return (blank ? \"\" : \"    \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */ \n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function definition(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const exit = state.enter(\"definition\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[\");\n    value += tracker.move(state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"]: \");\n    subexit();\n    if (// If there’s no url, or…\n    !node.url || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \"\\n\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */ \n\n\nemphasis.peek = emphasisPeek;\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function emphasis(node, _, state, info) {\n    const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state);\n    const exit = state.enter(\"emphasis\");\n    const tracker = state.createTracker(info);\n    const before = tracker.move(marker);\n    let between = tracker.move(state.containerPhrasing(node, {\n        after: marker,\n        before,\n        ...tracker.current()\n    }));\n    const betweenHead = between.charCodeAt(0);\n    const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.before.charCodeAt(info.before.length - 1), betweenHead, marker);\n    if (open.inside) {\n        between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1);\n    }\n    const betweenTail = between.charCodeAt(between.length - 1);\n    const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker);\n    if (close.inside) {\n        between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail);\n    }\n    const after = tracker.move(marker);\n    exit();\n    state.attentionEncodeSurroundingInfo = {\n        after: close.outside,\n        before: open.outside\n    };\n    return before + between + after;\n}\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function emphasisPeek(_, _1, state) {\n    return state.options.emphasis || \"*\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */ \n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function heading(node, _, state, info) {\n    const rank = Math.max(Math.min(6, node.depth || 1), 1);\n    const tracker = state.createTracker(info);\n    if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n        const exit = state.enter(\"headingSetext\");\n        const subexit = state.enter(\"phrasing\");\n        const value = state.containerPhrasing(node, {\n            ...tracker.current(),\n            before: \"\\n\",\n            after: \"\\n\"\n        });\n        subexit();\n        exit();\n        return value + \"\\n\" + (rank === 1 ? \"=\" : \"-\").repeat(// The whole size…\n        value.length - // Minus the position of the character after the last EOL (or\n        // 0 if there is none)…\n        (Math.max(value.lastIndexOf(\"\\r\"), value.lastIndexOf(\"\\n\")) + 1));\n    }\n    const sequence = \"#\".repeat(rank);\n    const exit = state.enter(\"headingAtx\");\n    const subexit = state.enter(\"phrasing\");\n    // Note: for proper tracking, we should reset the output positions when there\n    // is no content returned, because then the space is not output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    tracker.move(sequence + \" \");\n    let value = state.containerPhrasing(node, {\n        before: \"# \",\n        after: \"\\n\",\n        ...tracker.current()\n    });\n    if (/^[\\t ]/.test(value)) {\n        // To do: what effect has the character reference on tracking?\n        value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1);\n    }\n    value = value ? sequence + \" \" + value : sequence;\n    if (state.options.closeAtx) {\n        value += \" \" + sequence;\n    }\n    subexit();\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @import {Html} from 'mdast'\n */ html.peek = htmlPeek;\n/**\n * @param {Html} node\n * @returns {string}\n */ function html(node) {\n    return node.value || \"\";\n}\n/**\n * @returns {string}\n */ function htmlPeek() {\n    return \"<\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRURBLEtBQUtDLElBQUksR0FBR0M7QUFFWjs7O0NBR0MsR0FDTSxTQUFTRixLQUFLRyxJQUFJO0lBQ3ZCLE9BQU9BLEtBQUtDLEtBQUssSUFBSTtBQUN2QjtBQUVBOztDQUVDLEdBQ0QsU0FBU0Y7SUFDUCxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanM/NzE1MiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0h0bWx9IGZyb20gJ21kYXN0J1xuICovXG5cbmh0bWwucGVlayA9IGh0bWxQZWVrXG5cbi8qKlxuICogQHBhcmFtIHtIdG1sfSBub2RlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaHRtbChub2RlKSB7XG4gIHJldHVybiBub2RlLnZhbHVlIHx8ICcnXG59XG5cbi8qKlxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZnVuY3Rpb24gaHRtbFBlZWsoKSB7XG4gIHJldHVybiAnPCdcbn1cbiJdLCJuYW1lcyI6WyJodG1sIiwicGVlayIsImh0bWxQZWVrIiwibm9kZSIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */ imageReference.peek = imageReferencePeek;\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function imageReference(node, _, state, info) {\n    const type = node.referenceType;\n    const exit = state.enter(\"imageReference\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"![\");\n    const alt = state.safe(node.alt, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    value += tracker.move(alt + \"][\");\n    subexit();\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack;\n    state.stack = [];\n    subexit = state.enter(\"reference\");\n    // Note: for proper tracking, we should reset the output positions when we end\n    // up making a `shortcut` reference, because then there is no brace output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    const reference = state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    subexit();\n    state.stack = stack;\n    exit();\n    if (type === \"full\" || !alt || alt !== reference) {\n        value += tracker.move(reference + \"]\");\n    } else if (type === \"shortcut\") {\n        // Remove the unwanted `[`.\n        value = value.slice(0, -1);\n    } else {\n        value += tracker.move(\"]\");\n    }\n    return value;\n}\n/**\n * @returns {string}\n */ function imageReferencePeek() {\n    return \"!\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */ \nimage.peek = imagePeek;\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function image(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const exit = state.enter(\"image\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"![\");\n    value += tracker.move(state.safe(node.alt, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"](\");\n    subexit();\n    if (// If there’s no url but there is a title…\n    !node.url && node.title || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \")\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    value += tracker.move(\")\");\n    exit();\n    return value;\n}\n/**\n * @returns {string}\n */ function imagePeek() {\n    return \"!\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */ const handle = {\n    blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n    break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n    definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n    emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n    hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n    html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n    image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n    imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n    inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n    link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n    linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n    list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n    listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n    paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n    root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n    strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n    text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n    thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */ inlineCode.peek = inlineCodePeek;\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */ function inlineCode(node, _, state) {\n    let value = node.value || \"\";\n    let sequence = \"`\";\n    let index = -1;\n    // If there is a single grave accent on its own in the code, use a fence of\n    // two.\n    // If there are two in a row, use one.\n    while(new RegExp(\"(^|[^`])\" + sequence + \"([^`]|$)\").test(value)){\n        sequence += \"`\";\n    }\n    // If this is not just spaces or eols (tabs don’t count), and either the\n    // first or last character are a space, eol, or tick, then pad with spaces.\n    if (/[^ \\r\\n]/.test(value) && (/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value) || /^`|`$/.test(value))) {\n        value = \" \" + value + \" \";\n    }\n    // We have a potential problem: certain characters after eols could result in\n    // blocks being seen.\n    // For example, if someone injected the string `'\\n# b'`, then that would\n    // result in an ATX heading.\n    // We can’t escape characters in `inlineCode`, but because eols are\n    // transformed to spaces when going from markdown to HTML anyway, we can swap\n    // them out.\n    while(++index < state.unsafe.length){\n        const pattern = state.unsafe[index];\n        const expression = state.compilePattern(pattern);\n        /** @type {RegExpExecArray | null} */ let match;\n        // Only look for `atBreak`s.\n        // Btw: note that `atBreak` patterns will always start the regex at LF or\n        // CR.\n        if (!pattern.atBreak) continue;\n        while(match = expression.exec(value)){\n            let position = match.index;\n            // Support CRLF (patterns only look for one of the characters).\n            if (value.charCodeAt(position) === 10 /* `\\n` */  && value.charCodeAt(position - 1) === 13 /* `\\r` */ ) {\n                position--;\n            }\n            value = value.slice(0, position) + \" \" + value.slice(match.index + 1);\n        }\n    }\n    return sequence + value + sequence;\n}\n/**\n * @returns {string}\n */ function inlineCodePeek() {\n    return \"`\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */ linkReference.peek = linkReferencePeek;\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function linkReference(node, _, state, info) {\n    const type = node.referenceType;\n    const exit = state.enter(\"linkReference\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[\");\n    const text = state.containerPhrasing(node, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    value += tracker.move(text + \"][\");\n    subexit();\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack;\n    state.stack = [];\n    subexit = state.enter(\"reference\");\n    // Note: for proper tracking, we should reset the output positions when we end\n    // up making a `shortcut` reference, because then there is no brace output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    const reference = state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    subexit();\n    state.stack = stack;\n    exit();\n    if (type === \"full\" || !text || text !== reference) {\n        value += tracker.move(reference + \"]\");\n    } else if (type === \"shortcut\") {\n        // Remove the unwanted `[`.\n        value = value.slice(0, -1);\n    } else {\n        value += tracker.move(\"]\");\n    }\n    return value;\n}\n/**\n * @returns {string}\n */ function linkReferencePeek() {\n    return \"[\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */ \n\nlink.peek = linkPeek;\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function link(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const tracker = state.createTracker(info);\n    /** @type {Exit} */ let exit;\n    /** @type {Exit} */ let subexit;\n    if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n        // Hide the fact that we’re in phrasing, because escapes don’t work.\n        const stack = state.stack;\n        state.stack = [];\n        exit = state.enter(\"autolink\");\n        let value = tracker.move(\"<\");\n        value += tracker.move(state.containerPhrasing(node, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n        exit();\n        state.stack = stack;\n        return value;\n    }\n    exit = state.enter(\"link\");\n    subexit = state.enter(\"label\");\n    let value = tracker.move(\"[\");\n    value += tracker.move(state.containerPhrasing(node, {\n        before: value,\n        after: \"](\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"](\");\n    subexit();\n    if (// If there’s no url but there is a title…\n    !node.url && node.title || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \")\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    value += tracker.move(\")\");\n    exit();\n    return value;\n}\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */ function linkPeek(node, _, state) {\n    return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? \"<\" : \"[\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */ \n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function listItem(node, parent, state, info) {\n    const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state);\n    let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state);\n    // Add the marker value for ordered lists.\n    if (parent && parent.type === \"list\" && parent.ordered) {\n        bullet = (typeof parent.start === \"number\" && parent.start > -1 ? parent.start : 1) + (state.options.incrementListMarker === false ? 0 : parent.children.indexOf(node)) + bullet;\n    }\n    let size = bullet.length + 1;\n    if (listItemIndent === \"tab\" || listItemIndent === \"mixed\" && (parent && parent.type === \"list\" && parent.spread || node.spread)) {\n        size = Math.ceil(size / 4) * 4;\n    }\n    const tracker = state.createTracker(info);\n    tracker.move(bullet + \" \".repeat(size - bullet.length));\n    tracker.shift(size);\n    const exit = state.enter(\"listItem\");\n    const value = state.indentLines(state.containerFlow(node, tracker.current()), map);\n    exit();\n    return value;\n    /** @type {Map} */ function map(line, index, blank) {\n        if (index) {\n            return (blank ? \"\" : \" \".repeat(size)) + line;\n        }\n        return (blank ? bullet : bullet + \" \".repeat(size - bullet.length)) + line;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */ \n\n\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function list(node, parent, state, info) {\n    const exit = state.enter(\"list\");\n    const bulletCurrent = state.bulletCurrent;\n    /** @type {string} */ let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state);\n    /** @type {string} */ const bulletOther = node.ordered ? bullet === \".\" ? \")\" : \".\" : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state);\n    let useDifferentMarker = parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false;\n    if (!node.ordered) {\n        const firstListItem = node.children ? node.children[0] : undefined;\n        // If there’s an empty first list item directly in two list items,\n        // we have to use a different bullet:\n        //\n        // ```markdown\n        // * - *\n        // ```\n        //\n        // …because otherwise it would become one big thematic break.\n        if (// Bullet could be used as a thematic break marker:\n        (bullet === \"*\" || bullet === \"-\") && // Empty first list item:\n        firstListItem && (!firstListItem.children || !firstListItem.children[0]) && // Directly in two other list items:\n        state.stack[state.stack.length - 1] === \"list\" && state.stack[state.stack.length - 2] === \"listItem\" && state.stack[state.stack.length - 3] === \"list\" && state.stack[state.stack.length - 4] === \"listItem\" && // That are each the first child.\n        state.indexStack[state.indexStack.length - 1] === 0 && state.indexStack[state.indexStack.length - 2] === 0 && state.indexStack[state.indexStack.length - 3] === 0) {\n            useDifferentMarker = true;\n        }\n        // If there’s a thematic break at the start of the first list item,\n        // we have to use a different bullet:\n        //\n        // ```markdown\n        // * ---\n        // ```\n        //\n        // …because otherwise it would become one big thematic break.\n        if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {\n            let index = -1;\n            while(++index < node.children.length){\n                const item = node.children[index];\n                if (item && item.type === \"listItem\" && item.children && item.children[0] && item.children[0].type === \"thematicBreak\") {\n                    useDifferentMarker = true;\n                    break;\n                }\n            }\n        }\n    }\n    if (useDifferentMarker) {\n        bullet = bulletOther;\n    }\n    state.bulletCurrent = bullet;\n    const value = state.containerFlow(node, info);\n    state.bulletLastUsed = bullet;\n    state.bulletCurrent = bulletCurrent;\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */ /**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function paragraph(node, _, state, info) {\n    const exit = state.enter(\"paragraph\");\n    const subexit = state.enter(\"phrasing\");\n    const value = state.containerPhrasing(node, info);\n    subexit();\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBRUQ7Ozs7OztDQU1DLEdBQ00sU0FBU0EsVUFBVUMsSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUM1QyxNQUFNQyxPQUFPRixNQUFNRyxLQUFLLENBQUM7SUFDekIsTUFBTUMsVUFBVUosTUFBTUcsS0FBSyxDQUFDO0lBQzVCLE1BQU1FLFFBQVFMLE1BQU1NLGlCQUFpQixDQUFDUixNQUFNRztJQUM1Q0c7SUFDQUY7SUFDQSxPQUFPRztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9wYXJhZ3JhcGguanM/MDk2NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7UGFyYWdyYXBoLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7UGFyYWdyYXBofSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJhZ3JhcGgobm9kZSwgXywgc3RhdGUsIGluZm8pIHtcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdwYXJhZ3JhcGgnKVxuICBjb25zdCBzdWJleGl0ID0gc3RhdGUuZW50ZXIoJ3BocmFzaW5nJylcbiAgY29uc3QgdmFsdWUgPSBzdGF0ZS5jb250YWluZXJQaHJhc2luZyhub2RlLCBpbmZvKVxuICBzdWJleGl0KClcbiAgZXhpdCgpXG4gIHJldHVybiB2YWx1ZVxufVxuIl0sIm5hbWVzIjpbInBhcmFncmFwaCIsIm5vZGUiLCJfIiwic3RhdGUiLCJpbmZvIiwiZXhpdCIsImVudGVyIiwic3ViZXhpdCIsInZhbHVlIiwiY29udGFpbmVyUGhyYXNpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */ \n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function root(node, _, state, info) {\n    // Note: `html` nodes are ambiguous.\n    const hasPhrasing = node.children.some(function(d) {\n        return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d);\n    });\n    const container = hasPhrasing ? state.containerPhrasing : state.containerFlow;\n    return container.call(state, node, info);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FFMkM7QUFFNUM7Ozs7OztDQU1DLEdBQ00sU0FBU0MsS0FBS0MsSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUN2QyxvQ0FBb0M7SUFDcEMsTUFBTUMsY0FBY0osS0FBS0ssUUFBUSxDQUFDQyxJQUFJLENBQUMsU0FBVUMsQ0FBQztRQUNoRCxPQUFPVCw2REFBUUEsQ0FBQ1M7SUFDbEI7SUFFQSxNQUFNQyxZQUFZSixjQUFjRixNQUFNTyxpQkFBaUIsR0FBR1AsTUFBTVEsYUFBYTtJQUM3RSxPQUFPRixVQUFVRyxJQUFJLENBQUNULE9BQU9GLE1BQU1HO0FBQ3JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9yb290LmpzP2E2YjgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFJvb3R9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7cGhyYXNpbmd9IGZyb20gJ21kYXN0LXV0aWwtcGhyYXNpbmcnXG5cbi8qKlxuICogQHBhcmFtIHtSb290fSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByb290KG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIC8vIE5vdGU6IGBodG1sYCBub2RlcyBhcmUgYW1iaWd1b3VzLlxuICBjb25zdCBoYXNQaHJhc2luZyA9IG5vZGUuY2hpbGRyZW4uc29tZShmdW5jdGlvbiAoZCkge1xuICAgIHJldHVybiBwaHJhc2luZyhkKVxuICB9KVxuXG4gIGNvbnN0IGNvbnRhaW5lciA9IGhhc1BocmFzaW5nID8gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcgOiBzdGF0ZS5jb250YWluZXJGbG93XG4gIHJldHVybiBjb250YWluZXIuY2FsbChzdGF0ZSwgbm9kZSwgaW5mbylcbn1cbiJdLCJuYW1lcyI6WyJwaHJhc2luZyIsInJvb3QiLCJub2RlIiwiXyIsInN0YXRlIiwiaW5mbyIsImhhc1BocmFzaW5nIiwiY2hpbGRyZW4iLCJzb21lIiwiZCIsImNvbnRhaW5lciIsImNvbnRhaW5lclBocmFzaW5nIiwiY29udGFpbmVyRmxvdyIsImNhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */ \n\n\nstrong.peek = strongPeek;\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function strong(node, _, state, info) {\n    const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state);\n    const exit = state.enter(\"strong\");\n    const tracker = state.createTracker(info);\n    const before = tracker.move(marker + marker);\n    let between = tracker.move(state.containerPhrasing(node, {\n        after: marker,\n        before,\n        ...tracker.current()\n    }));\n    const betweenHead = between.charCodeAt(0);\n    const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.before.charCodeAt(info.before.length - 1), betweenHead, marker);\n    if (open.inside) {\n        between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1);\n    }\n    const betweenTail = between.charCodeAt(between.length - 1);\n    const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker);\n    if (close.inside) {\n        between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail);\n    }\n    const after = tracker.move(marker + marker);\n    exit();\n    state.attentionEncodeSurroundingInfo = {\n        after: close.outside,\n        before: open.outside\n    };\n    return before + between + after;\n}\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function strongPeek(_, _1, state) {\n    return state.options.strong || \"*\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */ /**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function text(node, _, state, info) {\n    return state.safe(node.value, info);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7Q0FHQyxHQUVEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNBLEtBQUtDLElBQUksRUFBRUMsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLElBQUk7SUFDdkMsT0FBT0QsTUFBTUUsSUFBSSxDQUFDSixLQUFLSyxLQUFLLEVBQUVGO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS90ZXh0LmpzPzhjY2YiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFRleHR9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtUZXh0fSBub2RlXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF9cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcGFyYW0ge0luZm99IGluZm9cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0ZXh0KG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIHJldHVybiBzdGF0ZS5zYWZlKG5vZGUudmFsdWUsIGluZm8pXG59XG4iXSwibmFtZXMiOlsidGV4dCIsIm5vZGUiLCJfIiwic3RhdGUiLCJpbmZvIiwic2FmZSIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */ \n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function thematicBreak(_, _1, state) {\n    const value = ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? \" \" : \"\")).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state));\n    return state.options.ruleSpaces ? value.slice(0, -1) : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7Q0FHQyxHQUVtRTtBQUNyQjtBQUUvQzs7Ozs7Q0FLQyxHQUNNLFNBQVNFLGNBQWNDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxLQUFLO0lBQ3hDLE1BQU1DLFFBQVEsQ0FDWkwsOERBQVNBLENBQUNJLFNBQVVBLENBQUFBLE1BQU1FLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLE1BQU0sRUFBQyxDQUFDLEVBQ3ZEQyxNQUFNLENBQUNULG1GQUFtQkEsQ0FBQ0s7SUFFN0IsT0FBT0EsTUFBTUUsT0FBTyxDQUFDQyxVQUFVLEdBQUdGLE1BQU1JLEtBQUssQ0FBQyxHQUFHLENBQUMsS0FBS0o7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzP2MxNDAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFRoZW1hdGljQnJlYWt9IGZyb20gJ21kYXN0J1xuICovXG5cbmltcG9ydCB7Y2hlY2tSdWxlUmVwZXRpdGlvbn0gZnJvbSAnLi4vdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMnXG5pbXBvcnQge2NoZWNrUnVsZX0gZnJvbSAnLi4vdXRpbC9jaGVjay1ydWxlLmpzJ1xuXG4vKipcbiAqIEBwYXJhbSB7VGhlbWF0aWNCcmVha30gX1xuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfMVxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0aGVtYXRpY0JyZWFrKF8sIF8xLCBzdGF0ZSkge1xuICBjb25zdCB2YWx1ZSA9IChcbiAgICBjaGVja1J1bGUoc3RhdGUpICsgKHN0YXRlLm9wdGlvbnMucnVsZVNwYWNlcyA/ICcgJyA6ICcnKVxuICApLnJlcGVhdChjaGVja1J1bGVSZXBldGl0aW9uKHN0YXRlKSlcblxuICByZXR1cm4gc3RhdGUub3B0aW9ucy5ydWxlU3BhY2VzID8gdmFsdWUuc2xpY2UoMCwgLTEpIDogdmFsdWVcbn1cbiJdLCJuYW1lcyI6WyJjaGVja1J1bGVSZXBldGl0aW9uIiwiY2hlY2tSdWxlIiwidGhlbWF0aWNCcmVhayIsIl8iLCJfMSIsInN0YXRlIiwidmFsdWUiLCJvcHRpb25zIiwicnVsZVNwYWNlcyIsInJlcGVhdCIsInNsaWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */ function checkBulletOrdered(state) {\n    const marker = state.options.bulletOrdered || \".\";\n    if (marker !== \".\" && marker !== \")\") {\n        throw new Error(\"Cannot serialize items with `\" + marker + \"` for `options.bulletOrdered`, expected `.` or `)`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxtQkFBbUJDLEtBQUs7SUFDdEMsTUFBTUMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxhQUFhLElBQUk7SUFFOUMsSUFBSUYsV0FBVyxPQUFPQSxXQUFXLEtBQUs7UUFDcEMsTUFBTSxJQUFJRyxNQUNSLGtDQUNFSCxTQUNBO0lBRU47SUFFQSxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stYnVsbGV0LW9yZGVyZWQuanM/OWUzMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2J1bGxldE9yZGVyZWQnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0J1bGxldE9yZGVyZWQoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5idWxsZXRPcmRlcmVkIHx8ICcuJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcuJyAmJiBtYXJrZXIgIT09ICcpJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0T3JkZXJlZGAsIGV4cGVjdGVkIGAuYCBvciBgKWAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrQnVsbGV0T3JkZXJlZCIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsImJ1bGxldE9yZGVyZWQiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ \n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */ function checkBulletOther(state) {\n    const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state);\n    const bulletOther = state.options.bulletOther;\n    if (!bulletOther) {\n        return bullet === \"*\" ? \"-\" : \"*\";\n    }\n    if (bulletOther !== \"*\" && bulletOther !== \"+\" && bulletOther !== \"-\") {\n        throw new Error(\"Cannot serialize items with `\" + bulletOther + \"` for `options.bulletOther`, expected `*`, `+`, or `-`\");\n    }\n    if (bulletOther === bullet) {\n        throw new Error(\"Expected `bullet` (`\" + bullet + \"`) and `bulletOther` (`\" + bulletOther + \"`) to be different\");\n    }\n    return bulletOther;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */ function checkBullet(state) {\n    const marker = state.options.bullet || \"*\";\n    if (marker !== \"*\" && marker !== \"+\" && marker !== \"-\") {\n        throw new Error(\"Cannot serialize items with `\" + marker + \"` for `options.bullet`, expected `*`, `+`, or `-`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0EsWUFBWUMsS0FBSztJQUMvQixNQUFNQyxTQUFTRCxNQUFNRSxPQUFPLENBQUNDLE1BQU0sSUFBSTtJQUV2QyxJQUFJRixXQUFXLE9BQU9BLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3RELE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC5qcz9mYWU2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snYnVsbGV0J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXQoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5idWxsZXQgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJysnICYmIG1hcmtlciAhPT0gJy0nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgaXRlbXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5idWxsZXRgLCBleHBlY3RlZCBgKmAsIGArYCwgb3IgYC1gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja0J1bGxldCIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsImJ1bGxldCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */ function checkEmphasis(state) {\n    const marker = state.options.emphasis || \"*\";\n    if (marker !== \"*\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize emphasis with `\" + marker + \"` for `options.emphasis`, expected `*`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxjQUFjQyxLQUFLO0lBQ2pDLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsUUFBUSxJQUFJO0lBRXpDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixxQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWVtcGhhc2lzLmpzPzhiMDMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydlbXBoYXNpcyddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrRW1waGFzaXMoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5lbXBoYXNpcyB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBlbXBoYXNpcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmVtcGhhc2lzYCwgZXhwZWN0ZWQgYCpgLCBvciBgX2AnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrRW1waGFzaXMiLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJlbXBoYXNpcyIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */ function checkFence(state) {\n    const marker = state.options.fence || \"`\";\n    if (marker !== \"`\" && marker !== \"~\") {\n        throw new Error(\"Cannot serialize code with `\" + marker + \"` for `options.fence`, expected `` ` `` or `~`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxXQUFXQyxLQUFLO0lBQzlCLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJO0lBRXRDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixpQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWZlbmNlLmpzP2JlYWUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydmZW5jZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrRmVuY2Uoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5mZW5jZSB8fCAnYCdcblxuICBpZiAobWFya2VyICE9PSAnYCcgJiYgbWFya2VyICE9PSAnficpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBjb2RlIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuZmVuY2VgLCBleHBlY3RlZCBgYCBgIGBgIG9yIGB+YCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tGZW5jZSIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsImZlbmNlIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */ function checkListItemIndent(state) {\n    const style = state.options.listItemIndent || \"one\";\n    if (style !== \"tab\" && style !== \"one\" && style !== \"mixed\") {\n        throw new Error(\"Cannot serialize items with `\" + style + \"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`\");\n    }\n    return style;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBLG9CQUFvQkMsS0FBSztJQUN2QyxNQUFNQyxRQUFRRCxNQUFNRSxPQUFPLENBQUNDLGNBQWMsSUFBSTtJQUU5QyxJQUFJRixVQUFVLFNBQVNBLFVBQVUsU0FBU0EsVUFBVSxTQUFTO1FBQzNELE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsUUFDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWxpc3QtaXRlbS1pbmRlbnQuanM/NDNjNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2xpc3RJdGVtSW5kZW50J10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tMaXN0SXRlbUluZGVudChzdGF0ZSkge1xuICBjb25zdCBzdHlsZSA9IHN0YXRlLm9wdGlvbnMubGlzdEl0ZW1JbmRlbnQgfHwgJ29uZSdcblxuICBpZiAoc3R5bGUgIT09ICd0YWInICYmIHN0eWxlICE9PSAnb25lJyAmJiBzdHlsZSAhPT0gJ21peGVkJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBzdHlsZSArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5saXN0SXRlbUluZGVudGAsIGV4cGVjdGVkIGB0YWJgLCBgb25lYCwgb3IgYG1peGVkYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gc3R5bGVcbn1cbiJdLCJuYW1lcyI6WyJjaGVja0xpc3RJdGVtSW5kZW50Iiwic3RhdGUiLCJzdHlsZSIsIm9wdGlvbnMiLCJsaXN0SXRlbUluZGVudCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */ function checkQuote(state) {\n    const marker = state.options.quote || '\"';\n    if (marker !== '\"' && marker !== \"'\") {\n        throw new Error(\"Cannot serialize title with `\" + marker + \"` for `options.quote`, expected `\\\"`, or `'`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxXQUFXQyxLQUFLO0lBQzlCLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJO0lBRXRDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLXF1b3RlLmpzPzM1YjciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydxdW90ZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUXVvdGUoc3RhdGUpIHtcbiAgY29uc3QgbWFya2VyID0gc3RhdGUub3B0aW9ucy5xdW90ZSB8fCAnXCInXG5cbiAgaWYgKG1hcmtlciAhPT0gJ1wiJyAmJiBtYXJrZXIgIT09IFwiJ1wiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgdGl0bGUgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5xdW90ZWAsIGV4cGVjdGVkIGBcImAsIG9yIGBcXCdgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja1F1b3RlIiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwicXVvdGUiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */ function checkRuleRepetition(state) {\n    const repetition = state.options.ruleRepetition || 3;\n    if (repetition < 3) {\n        throw new Error(\"Cannot serialize rules with repetition `\" + repetition + \"` for `options.ruleRepetition`, expected `3` or more\");\n    }\n    return repetition;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0Esb0JBQW9CQyxLQUFLO0lBQ3ZDLE1BQU1DLGFBQWFELE1BQU1FLE9BQU8sQ0FBQ0MsY0FBYyxJQUFJO0lBRW5ELElBQUlGLGFBQWEsR0FBRztRQUNsQixNQUFNLElBQUlHLE1BQ1IsNkNBQ0VILGFBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanM/M2UyMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3J1bGVSZXBldGl0aW9uJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tSdWxlUmVwZXRpdGlvbihzdGF0ZSkge1xuICBjb25zdCByZXBldGl0aW9uID0gc3RhdGUub3B0aW9ucy5ydWxlUmVwZXRpdGlvbiB8fCAzXG5cbiAgaWYgKHJlcGV0aXRpb24gPCAzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgcnVsZXMgd2l0aCByZXBldGl0aW9uIGAnICtcbiAgICAgICAgcmVwZXRpdGlvbiArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5ydWxlUmVwZXRpdGlvbmAsIGV4cGVjdGVkIGAzYCBvciBtb3JlJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiByZXBldGl0aW9uXG59XG4iXSwibmFtZXMiOlsiY2hlY2tSdWxlUmVwZXRpdGlvbiIsInN0YXRlIiwicmVwZXRpdGlvbiIsIm9wdGlvbnMiLCJydWxlUmVwZXRpdGlvbiIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */ function checkRule(state) {\n    const marker = state.options.rule || \"*\";\n    if (marker !== \"*\" && marker !== \"-\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize rules with `\" + marker + \"` for `options.rule`, expected `*`, `-`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBLFVBQVVDLEtBQUs7SUFDN0IsTUFBTUMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxJQUFJLElBQUk7SUFFckMsSUFBSUYsV0FBVyxPQUFPQSxXQUFXLE9BQU9BLFdBQVcsS0FBSztRQUN0RCxNQUFNLElBQUlHLE1BQ1Isa0NBQ0VILFNBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzP2I1MDYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydydWxlJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tSdWxlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMucnVsZSB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnLScgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBydWxlcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnJ1bGVgLCBleHBlY3RlZCBgKmAsIGAtYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja1J1bGUiLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJydWxlIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */ function checkStrong(state) {\n    const marker = state.options.strong || \"*\";\n    if (marker !== \"*\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize strong with `\" + marker + \"` for `options.strong`, expected `*`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0EsWUFBWUMsS0FBSztJQUMvQixNQUFNQyxTQUFTRCxNQUFNRSxPQUFPLENBQUNDLE1BQU0sSUFBSTtJQUV2QyxJQUFJRixXQUFXLE9BQU9BLFdBQVcsS0FBSztRQUNwQyxNQUFNLElBQUlHLE1BQ1IsbUNBQ0VILFNBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanM/YzBjNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ3N0cm9uZyddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrU3Ryb25nKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuc3Ryb25nIHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICdfJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHN0cm9uZyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLnN0cm9uZ2AsIGV4cGVjdGVkIGAqYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja1N0cm9uZyIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsInN0cm9uZyIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)\n/* harmony export */ });\n/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */ function encodeCharacterReference(code) {\n    return \"&#x\" + code.toString(16).toUpperCase() + \";\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Ozs7Q0FPQyxHQUNNLFNBQVNBLHlCQUF5QkMsSUFBSTtJQUMzQyxPQUFPLFFBQVFBLEtBQUtDLFFBQVEsQ0FBQyxJQUFJQyxXQUFXLEtBQUs7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcz9jNTIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRW5jb2RlIGEgY29kZSBwb2ludCBhcyBhIGNoYXJhY3RlciByZWZlcmVuY2UuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IGNvZGVcbiAqICAgQ29kZSBwb2ludCB0byBlbmNvZGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBFbmNvZGVkIGNoYXJhY3RlciByZWZlcmVuY2UuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbmNvZGVDaGFyYWN0ZXJSZWZlcmVuY2UoY29kZSkge1xuICByZXR1cm4gJyYjeCcgKyBjb2RlLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpICsgJzsnXG59XG4iXSwibmFtZXMiOlsiZW5jb2RlQ2hhcmFjdGVyUmVmZXJlbmNlIiwiY29kZSIsInRvU3RyaW5nIiwidG9VcHBlckNhc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)\n/* harmony export */ });\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/**\n * @import {EncodeSides} from '../types.js'\n */ \n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */ // Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nfunction encodeInfo(outside, inside, marker) {\n    const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside);\n    const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside);\n    // Letter outside:\n    if (outsideKind === undefined) {\n        return insideKind === undefined ? // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === \"_\" ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: false\n        } : insideKind === 1 ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: true\n        };\n    }\n    // Whitespace outside:\n    if (outsideKind === 1) {\n        return insideKind === undefined ? {\n            inside: false,\n            outside: false\n        } : insideKind === 1 ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: false\n        };\n    }\n    // Punctuation outside:\n    return insideKind === undefined ? {\n        inside: false,\n        outside: false\n    } : insideKind === 1 ? {\n        inside: true,\n        outside: false\n    } : {\n        inside: false,\n        outside: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */ /**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */ function formatCodeAsIndented(node, state) {\n    return Boolean(state.options.fences === false && node.value && // If there’s no info…\n    !node.lang && // And there’s a non-whitespace character…\n    /[^ \\r\\n]/.test(node.value) && // And the value doesn’t start or end in a blank…\n    !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBRUQ7Ozs7Q0FJQyxHQUNNLFNBQVNBLHFCQUFxQkMsSUFBSSxFQUFFQyxLQUFLO0lBQzlDLE9BQU9DLFFBQ0xELE1BQU1FLE9BQU8sQ0FBQ0MsTUFBTSxLQUFLLFNBQ3ZCSixLQUFLSyxLQUFLLElBQ1Ysc0JBQXNCO0lBQ3RCLENBQUNMLEtBQUtNLElBQUksSUFDViwwQ0FBMEM7SUFDMUMsV0FBV0MsSUFBSSxDQUFDUCxLQUFLSyxLQUFLLEtBQzFCLGlEQUFpRDtJQUNqRCxDQUFDLDBDQUEwQ0UsSUFBSSxDQUFDUCxLQUFLSyxLQUFLO0FBRWhFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvZm9ybWF0LWNvZGUtYXMtaW5kZW50ZWQuanM/MWQ2YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1N0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICogQGltcG9ydCB7Q29kZX0gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge0NvZGV9IG5vZGVcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdENvZGVBc0luZGVudGVkKG5vZGUsIHN0YXRlKSB7XG4gIHJldHVybiBCb29sZWFuKFxuICAgIHN0YXRlLm9wdGlvbnMuZmVuY2VzID09PSBmYWxzZSAmJlxuICAgICAgbm9kZS52YWx1ZSAmJlxuICAgICAgLy8gSWYgdGhlcmXigJlzIG5vIGluZm/igKZcbiAgICAgICFub2RlLmxhbmcgJiZcbiAgICAgIC8vIEFuZCB0aGVyZeKAmXMgYSBub24td2hpdGVzcGFjZSBjaGFyYWN0ZXLigKZcbiAgICAgIC9bXiBcXHJcXG5dLy50ZXN0KG5vZGUudmFsdWUpICYmXG4gICAgICAvLyBBbmQgdGhlIHZhbHVlIGRvZXNu4oCZdCBzdGFydCBvciBlbmQgaW4gYSBibGFua+KAplxuICAgICAgIS9eW1xcdCBdKig/OltcXHJcXG5dfCQpfCg/Ol58W1xcclxcbl0pW1xcdCBdKiQvLnRlc3Qobm9kZS52YWx1ZSlcbiAgKVxufVxuIl0sIm5hbWVzIjpbImZvcm1hdENvZGVBc0luZGVudGVkIiwibm9kZSIsInN0YXRlIiwiQm9vbGVhbiIsIm9wdGlvbnMiLCJmZW5jZXMiLCJ2YWx1ZSIsImxhbmciLCJ0ZXN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */ \n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */ function formatHeadingAsSetext(node, state) {\n    let literalWithBreak = false;\n    // Look for literals with a line break.\n    // Note that this also\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function(node) {\n        if (\"value\" in node && /\\r?\\n|\\r/.test(node.value) || node.type === \"break\") {\n            literalWithBreak = true;\n            return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT;\n        }\n    });\n    return Boolean((!node.depth || node.depth < 3) && (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) && (state.options.setext || literalWithBreak));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */ \n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */ function formatLinkAsAutolink(node, state) {\n    const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node);\n    return Boolean(!state.options.resourceLink && // If there’s a url…\n    node.url && // And there’s a no title…\n    !node.title && // And the content of `node` is a single text node…\n    node.children && node.children.length === 1 && node.children[0].type === \"text\" && // And if the url is the same as the content…\n    (raw === node.url || \"mailto:\" + raw === node.url) && // And that starts w/ a protocol…\n    /^[a-z][a-z+.-]+:/i.test(node.url) && // And that doesn’t contain ASCII control codes (character escapes and\n    // references don’t work), space, or angle brackets…\n    !/[\\0- <>\\u007F]/.test(node.url));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */ /**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */ function patternInScope(stack, pattern) {\n    return listInScope(stack, pattern.inConstruct, true) && !listInScope(stack, pattern.notInConstruct, false);\n}\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */ function listInScope(stack, list, none) {\n    if (typeof list === \"string\") {\n        list = [\n            list\n        ];\n    }\n    if (!list || list.length === 0) {\n        return none;\n    }\n    let index = -1;\n    while(++index < list.length){\n        if (stack.includes(list[index])) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ })

};
;