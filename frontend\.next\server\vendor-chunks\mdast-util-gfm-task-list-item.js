"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-task-list-item";
exports.ids = ["vendor-chunks/mdast-util-gfm-task-list-item"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-gfm-task-list-item/lib/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTaskListItemFromMarkdown: () => (/* binding */ gfmTaskListItemFromMarkdown),\n/* harmony export */   gfmTaskListItemToMarkdown: () => (/* binding */ gfmTaskListItemToMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\");\n/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */ \n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM task\n * list items in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM task list items.\n */ function gfmTaskListItemFromMarkdown() {\n    return {\n        exit: {\n            taskListCheckValueChecked: exitCheck,\n            taskListCheckValueUnchecked: exitCheck,\n            paragraph: exitParagraphWithTaskListItem\n        }\n    };\n}\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM task list\n * items in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM task list items.\n */ function gfmTaskListItemToMarkdown() {\n    return {\n        unsafe: [\n            {\n                atBreak: true,\n                character: \"-\",\n                after: \"[:|-]\"\n            }\n        ],\n        handlers: {\n            listItem: listItemWithTaskListItem\n        }\n    };\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitCheck(token) {\n    // We’re always in a paragraph, in a list item.\n    const node = this.stack[this.stack.length - 2];\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"listItem\");\n    node.checked = token.type === \"taskListCheckValueChecked\";\n}\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */ function exitParagraphWithTaskListItem(token) {\n    const parent = this.stack[this.stack.length - 2];\n    if (parent && parent.type === \"listItem\" && typeof parent.checked === \"boolean\") {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === \"paragraph\");\n        const head = node.children[0];\n        if (head && head.type === \"text\") {\n            const siblings = parent.children;\n            let index = -1;\n            /** @type {Paragraph | undefined} */ let firstParaghraph;\n            while(++index < siblings.length){\n                const sibling = siblings[index];\n                if (sibling.type === \"paragraph\") {\n                    firstParaghraph = sibling;\n                    break;\n                }\n            }\n            if (firstParaghraph === node) {\n                // Must start with a space or a tab.\n                head.value = head.value.slice(1);\n                if (head.value.length === 0) {\n                    node.children.shift();\n                } else if (node.position && head.position && typeof head.position.start.offset === \"number\") {\n                    head.position.start.column++;\n                    head.position.start.offset++;\n                    node.position.start = Object.assign({}, head.position.start);\n                }\n            }\n        }\n    }\n    this.exit(token);\n}\n/**\n * @type {ToMarkdownHandle}\n * @param {ListItem} node\n */ function listItemWithTaskListItem(node, parent, state, info) {\n    const head = node.children[0];\n    const checkable = typeof node.checked === \"boolean\" && head && head.type === \"paragraph\";\n    const checkbox = \"[\" + (node.checked ? \"x\" : \" \") + \"] \";\n    const tracker = state.createTracker(info);\n    if (checkable) {\n        tracker.move(checkbox);\n    }\n    let value = mdast_util_to_markdown__WEBPACK_IMPORTED_MODULE_1__.handle.listItem(node, parent, state, {\n        ...info,\n        ...tracker.current()\n    });\n    if (checkable) {\n        value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check);\n    }\n    return value;\n    /**\n   * @param {string} $0\n   * @returns {string}\n   */ function check($0) {\n        return $0 + checkbox;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1nZm0tdGFzay1saXN0LWl0ZW0vbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Q0FRQyxHQUVrQztBQUNtQjtBQUV0RDs7Ozs7O0NBTUMsR0FDTSxTQUFTRztJQUNkLE9BQU87UUFDTEMsTUFBTTtZQUNKQywyQkFBMkJDO1lBQzNCQyw2QkFBNkJEO1lBQzdCRSxXQUFXQztRQUNiO0lBQ0Y7QUFDRjtBQUVBOzs7Ozs7Q0FNQyxHQUNNLFNBQVNDO0lBQ2QsT0FBTztRQUNMQyxRQUFRO1lBQUM7Z0JBQUNDLFNBQVM7Z0JBQU1DLFdBQVc7Z0JBQUtDLE9BQU87WUFBTztTQUFFO1FBQ3pEQyxVQUFVO1lBQUNDLFVBQVVDO1FBQXdCO0lBQy9DO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDRCxTQUFTWCxVQUFVWSxLQUFLO0lBQ3RCLCtDQUErQztJQUMvQyxNQUFNQyxPQUFPLElBQUksQ0FBQ0MsS0FBSyxDQUFDLElBQUksQ0FBQ0EsS0FBSyxDQUFDQyxNQUFNLEdBQUcsRUFBRTtJQUM5Q3BCLDBDQUFNQSxDQUFDa0IsS0FBS0csSUFBSSxLQUFLO0lBQ3JCSCxLQUFLSSxPQUFPLEdBQUdMLE1BQU1JLElBQUksS0FBSztBQUNoQztBQUVBOzs7Q0FHQyxHQUNELFNBQVNiLDhCQUE4QlMsS0FBSztJQUMxQyxNQUFNTSxTQUFTLElBQUksQ0FBQ0osS0FBSyxDQUFDLElBQUksQ0FBQ0EsS0FBSyxDQUFDQyxNQUFNLEdBQUcsRUFBRTtJQUVoRCxJQUNFRyxVQUNBQSxPQUFPRixJQUFJLEtBQUssY0FDaEIsT0FBT0UsT0FBT0QsT0FBTyxLQUFLLFdBQzFCO1FBQ0EsTUFBTUosT0FBTyxJQUFJLENBQUNDLEtBQUssQ0FBQyxJQUFJLENBQUNBLEtBQUssQ0FBQ0MsTUFBTSxHQUFHLEVBQUU7UUFDOUNwQiwwQ0FBTUEsQ0FBQ2tCLEtBQUtHLElBQUksS0FBSztRQUNyQixNQUFNRyxPQUFPTixLQUFLTyxRQUFRLENBQUMsRUFBRTtRQUU3QixJQUFJRCxRQUFRQSxLQUFLSCxJQUFJLEtBQUssUUFBUTtZQUNoQyxNQUFNSyxXQUFXSCxPQUFPRSxRQUFRO1lBQ2hDLElBQUlFLFFBQVEsQ0FBQztZQUNiLGtDQUFrQyxHQUNsQyxJQUFJQztZQUVKLE1BQU8sRUFBRUQsUUFBUUQsU0FBU04sTUFBTSxDQUFFO2dCQUNoQyxNQUFNUyxVQUFVSCxRQUFRLENBQUNDLE1BQU07Z0JBQy9CLElBQUlFLFFBQVFSLElBQUksS0FBSyxhQUFhO29CQUNoQ08sa0JBQWtCQztvQkFDbEI7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlELG9CQUFvQlYsTUFBTTtnQkFDNUIsb0NBQW9DO2dCQUNwQ00sS0FBS00sS0FBSyxHQUFHTixLQUFLTSxLQUFLLENBQUNDLEtBQUssQ0FBQztnQkFFOUIsSUFBSVAsS0FBS00sS0FBSyxDQUFDVixNQUFNLEtBQUssR0FBRztvQkFDM0JGLEtBQUtPLFFBQVEsQ0FBQ08sS0FBSztnQkFDckIsT0FBTyxJQUNMZCxLQUFLZSxRQUFRLElBQ2JULEtBQUtTLFFBQVEsSUFDYixPQUFPVCxLQUFLUyxRQUFRLENBQUNDLEtBQUssQ0FBQ0MsTUFBTSxLQUFLLFVBQ3RDO29CQUNBWCxLQUFLUyxRQUFRLENBQUNDLEtBQUssQ0FBQ0UsTUFBTTtvQkFDMUJaLEtBQUtTLFFBQVEsQ0FBQ0MsS0FBSyxDQUFDQyxNQUFNO29CQUMxQmpCLEtBQUtlLFFBQVEsQ0FBQ0MsS0FBSyxHQUFHRyxPQUFPQyxNQUFNLENBQUMsQ0FBQyxHQUFHZCxLQUFLUyxRQUFRLENBQUNDLEtBQUs7Z0JBQzdEO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsSUFBSSxDQUFDL0IsSUFBSSxDQUFDYztBQUNaO0FBRUE7OztDQUdDLEdBQ0QsU0FBU0QseUJBQXlCRSxJQUFJLEVBQUVLLE1BQU0sRUFBRWdCLEtBQUssRUFBRUMsSUFBSTtJQUN6RCxNQUFNaEIsT0FBT04sS0FBS08sUUFBUSxDQUFDLEVBQUU7SUFDN0IsTUFBTWdCLFlBQ0osT0FBT3ZCLEtBQUtJLE9BQU8sS0FBSyxhQUFhRSxRQUFRQSxLQUFLSCxJQUFJLEtBQUs7SUFDN0QsTUFBTXFCLFdBQVcsTUFBT3hCLENBQUFBLEtBQUtJLE9BQU8sR0FBRyxNQUFNLEdBQUUsSUFBSztJQUNwRCxNQUFNcUIsVUFBVUosTUFBTUssYUFBYSxDQUFDSjtJQUVwQyxJQUFJQyxXQUFXO1FBQ2JFLFFBQVFFLElBQUksQ0FBQ0g7SUFDZjtJQUVBLElBQUlaLFFBQVE3QiwwREFBZUEsQ0FBQ2MsUUFBUSxDQUFDRyxNQUFNSyxRQUFRZ0IsT0FBTztRQUN4RCxHQUFHQyxJQUFJO1FBQ1AsR0FBR0csUUFBUUcsT0FBTyxFQUFFO0lBQ3RCO0lBRUEsSUFBSUwsV0FBVztRQUNiWCxRQUFRQSxNQUFNaUIsT0FBTyxDQUFDLG1DQUFtQ0M7SUFDM0Q7SUFFQSxPQUFPbEI7SUFFUDs7O0dBR0MsR0FDRCxTQUFTa0IsTUFBTUMsRUFBRTtRQUNmLE9BQU9BLEtBQUtQO0lBQ2Q7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLWdmbS10YXNrLWxpc3QtaXRlbS9saWIvaW5kZXguanM/MjUzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuTGlzdEl0ZW19IExpc3RJdGVtXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlBhcmFncmFwaH0gUGFyYWdyYXBoXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdC11dGlsLWZyb20tbWFya2Rvd24nKS5Db21waWxlQ29udGV4dH0gQ29tcGlsZUNvbnRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0LXV0aWwtZnJvbS1tYXJrZG93bicpLkV4dGVuc2lvbn0gRnJvbU1hcmtkb3duRXh0ZW5zaW9uXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdC11dGlsLWZyb20tbWFya2Rvd24nKS5IYW5kbGV9IEZyb21NYXJrZG93bkhhbmRsZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QtdXRpbC10by1tYXJrZG93bicpLk9wdGlvbnN9IFRvTWFya2Rvd25FeHRlbnNpb25cbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nKS5IYW5kbGV9IFRvTWFya2Rvd25IYW5kbGVcbiAqL1xuXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAnZGV2bG9wJ1xuaW1wb3J0IHtkZWZhdWx0SGFuZGxlcnN9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG5cbi8qKlxuICogQ3JlYXRlIGFuIGV4dGVuc2lvbiBmb3IgYG1kYXN0LXV0aWwtZnJvbS1tYXJrZG93bmAgdG8gZW5hYmxlIEdGTSB0YXNrXG4gKiBsaXN0IGl0ZW1zIGluIG1hcmtkb3duLlxuICpcbiAqIEByZXR1cm5zIHtGcm9tTWFya2Rvd25FeHRlbnNpb259XG4gKiAgIEV4dGVuc2lvbiBmb3IgYG1kYXN0LXV0aWwtZnJvbS1tYXJrZG93bmAgdG8gZW5hYmxlIEdGTSB0YXNrIGxpc3QgaXRlbXMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZm1UYXNrTGlzdEl0ZW1Gcm9tTWFya2Rvd24oKSB7XG4gIHJldHVybiB7XG4gICAgZXhpdDoge1xuICAgICAgdGFza0xpc3RDaGVja1ZhbHVlQ2hlY2tlZDogZXhpdENoZWNrLFxuICAgICAgdGFza0xpc3RDaGVja1ZhbHVlVW5jaGVja2VkOiBleGl0Q2hlY2ssXG4gICAgICBwYXJhZ3JhcGg6IGV4aXRQYXJhZ3JhcGhXaXRoVGFza0xpc3RJdGVtXG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQ3JlYXRlIGFuIGV4dGVuc2lvbiBmb3IgYG1kYXN0LXV0aWwtdG8tbWFya2Rvd25gIHRvIGVuYWJsZSBHRk0gdGFzayBsaXN0XG4gKiBpdGVtcyBpbiBtYXJrZG93bi5cbiAqXG4gKiBAcmV0dXJucyB7VG9NYXJrZG93bkV4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWRhc3QtdXRpbC10by1tYXJrZG93bmAgdG8gZW5hYmxlIEdGTSB0YXNrIGxpc3QgaXRlbXMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZm1UYXNrTGlzdEl0ZW1Ub01hcmtkb3duKCkge1xuICByZXR1cm4ge1xuICAgIHVuc2FmZTogW3thdEJyZWFrOiB0cnVlLCBjaGFyYWN0ZXI6ICctJywgYWZ0ZXI6ICdbOnwtXSd9XSxcbiAgICBoYW5kbGVyczoge2xpc3RJdGVtOiBsaXN0SXRlbVdpdGhUYXNrTGlzdEl0ZW19XG4gIH1cbn1cblxuLyoqXG4gKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gKiBAdHlwZSB7RnJvbU1hcmtkb3duSGFuZGxlfVxuICovXG5mdW5jdGlvbiBleGl0Q2hlY2sodG9rZW4pIHtcbiAgLy8gV2XigJlyZSBhbHdheXMgaW4gYSBwYXJhZ3JhcGgsIGluIGEgbGlzdCBpdGVtLlxuICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDJdXG4gIGFzc2VydChub2RlLnR5cGUgPT09ICdsaXN0SXRlbScpXG4gIG5vZGUuY2hlY2tlZCA9IHRva2VuLnR5cGUgPT09ICd0YXNrTGlzdENoZWNrVmFsdWVDaGVja2VkJ1xufVxuXG4vKipcbiAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAqIEB0eXBlIHtGcm9tTWFya2Rvd25IYW5kbGV9XG4gKi9cbmZ1bmN0aW9uIGV4aXRQYXJhZ3JhcGhXaXRoVGFza0xpc3RJdGVtKHRva2VuKSB7XG4gIGNvbnN0IHBhcmVudCA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAyXVxuXG4gIGlmIChcbiAgICBwYXJlbnQgJiZcbiAgICBwYXJlbnQudHlwZSA9PT0gJ2xpc3RJdGVtJyAmJlxuICAgIHR5cGVvZiBwYXJlbnQuY2hlY2tlZCA9PT0gJ2Jvb2xlYW4nXG4gICkge1xuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZS50eXBlID09PSAncGFyYWdyYXBoJylcbiAgICBjb25zdCBoZWFkID0gbm9kZS5jaGlsZHJlblswXVxuXG4gICAgaWYgKGhlYWQgJiYgaGVhZC50eXBlID09PSAndGV4dCcpIHtcbiAgICAgIGNvbnN0IHNpYmxpbmdzID0gcGFyZW50LmNoaWxkcmVuXG4gICAgICBsZXQgaW5kZXggPSAtMVxuICAgICAgLyoqIEB0eXBlIHtQYXJhZ3JhcGggfCB1bmRlZmluZWR9ICovXG4gICAgICBsZXQgZmlyc3RQYXJhZ2hyYXBoXG5cbiAgICAgIHdoaWxlICgrK2luZGV4IDwgc2libGluZ3MubGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IHNpYmxpbmcgPSBzaWJsaW5nc1tpbmRleF1cbiAgICAgICAgaWYgKHNpYmxpbmcudHlwZSA9PT0gJ3BhcmFncmFwaCcpIHtcbiAgICAgICAgICBmaXJzdFBhcmFnaHJhcGggPSBzaWJsaW5nXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoZmlyc3RQYXJhZ2hyYXBoID09PSBub2RlKSB7XG4gICAgICAgIC8vIE11c3Qgc3RhcnQgd2l0aCBhIHNwYWNlIG9yIGEgdGFiLlxuICAgICAgICBoZWFkLnZhbHVlID0gaGVhZC52YWx1ZS5zbGljZSgxKVxuXG4gICAgICAgIGlmIChoZWFkLnZhbHVlLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgIG5vZGUuY2hpbGRyZW4uc2hpZnQoKVxuICAgICAgICB9IGVsc2UgaWYgKFxuICAgICAgICAgIG5vZGUucG9zaXRpb24gJiZcbiAgICAgICAgICBoZWFkLnBvc2l0aW9uICYmXG4gICAgICAgICAgdHlwZW9mIGhlYWQucG9zaXRpb24uc3RhcnQub2Zmc2V0ID09PSAnbnVtYmVyJ1xuICAgICAgICApIHtcbiAgICAgICAgICBoZWFkLnBvc2l0aW9uLnN0YXJ0LmNvbHVtbisrXG4gICAgICAgICAgaGVhZC5wb3NpdGlvbi5zdGFydC5vZmZzZXQrK1xuICAgICAgICAgIG5vZGUucG9zaXRpb24uc3RhcnQgPSBPYmplY3QuYXNzaWduKHt9LCBoZWFkLnBvc2l0aW9uLnN0YXJ0KVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgdGhpcy5leGl0KHRva2VuKVxufVxuXG4vKipcbiAqIEB0eXBlIHtUb01hcmtkb3duSGFuZGxlfVxuICogQHBhcmFtIHtMaXN0SXRlbX0gbm9kZVxuICovXG5mdW5jdGlvbiBsaXN0SXRlbVdpdGhUYXNrTGlzdEl0ZW0obm9kZSwgcGFyZW50LCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBoZWFkID0gbm9kZS5jaGlsZHJlblswXVxuICBjb25zdCBjaGVja2FibGUgPVxuICAgIHR5cGVvZiBub2RlLmNoZWNrZWQgPT09ICdib29sZWFuJyAmJiBoZWFkICYmIGhlYWQudHlwZSA9PT0gJ3BhcmFncmFwaCdcbiAgY29uc3QgY2hlY2tib3ggPSAnWycgKyAobm9kZS5jaGVja2VkID8gJ3gnIDogJyAnKSArICddICdcbiAgY29uc3QgdHJhY2tlciA9IHN0YXRlLmNyZWF0ZVRyYWNrZXIoaW5mbylcblxuICBpZiAoY2hlY2thYmxlKSB7XG4gICAgdHJhY2tlci5tb3ZlKGNoZWNrYm94KVxuICB9XG5cbiAgbGV0IHZhbHVlID0gZGVmYXVsdEhhbmRsZXJzLmxpc3RJdGVtKG5vZGUsIHBhcmVudCwgc3RhdGUsIHtcbiAgICAuLi5pbmZvLFxuICAgIC4uLnRyYWNrZXIuY3VycmVudCgpXG4gIH0pXG5cbiAgaWYgKGNoZWNrYWJsZSkge1xuICAgIHZhbHVlID0gdmFsdWUucmVwbGFjZSgvXig/OlsqKy1dfFxcZCtcXC4pKFtcXHJcXG5dfCB7MSwzfSkvLCBjaGVjaylcbiAgfVxuXG4gIHJldHVybiB2YWx1ZVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge3N0cmluZ30gJDBcbiAgICogQHJldHVybnMge3N0cmluZ31cbiAgICovXG4gIGZ1bmN0aW9uIGNoZWNrKCQwKSB7XG4gICAgcmV0dXJuICQwICsgY2hlY2tib3hcbiAgfVxufVxuIl0sIm5hbWVzIjpbIm9rIiwiYXNzZXJ0IiwiZGVmYXVsdEhhbmRsZXJzIiwiZ2ZtVGFza0xpc3RJdGVtRnJvbU1hcmtkb3duIiwiZXhpdCIsInRhc2tMaXN0Q2hlY2tWYWx1ZUNoZWNrZWQiLCJleGl0Q2hlY2siLCJ0YXNrTGlzdENoZWNrVmFsdWVVbmNoZWNrZWQiLCJwYXJhZ3JhcGgiLCJleGl0UGFyYWdyYXBoV2l0aFRhc2tMaXN0SXRlbSIsImdmbVRhc2tMaXN0SXRlbVRvTWFya2Rvd24iLCJ1bnNhZmUiLCJhdEJyZWFrIiwiY2hhcmFjdGVyIiwiYWZ0ZXIiLCJoYW5kbGVycyIsImxpc3RJdGVtIiwibGlzdEl0ZW1XaXRoVGFza0xpc3RJdGVtIiwidG9rZW4iLCJub2RlIiwic3RhY2siLCJsZW5ndGgiLCJ0eXBlIiwiY2hlY2tlZCIsInBhcmVudCIsImhlYWQiLCJjaGlsZHJlbiIsInNpYmxpbmdzIiwiaW5kZXgiLCJmaXJzdFBhcmFnaHJhcGgiLCJzaWJsaW5nIiwidmFsdWUiLCJzbGljZSIsInNoaWZ0IiwicG9zaXRpb24iLCJzdGFydCIsIm9mZnNldCIsImNvbHVtbiIsIk9iamVjdCIsImFzc2lnbiIsInN0YXRlIiwiaW5mbyIsImNoZWNrYWJsZSIsImNoZWNrYm94IiwidHJhY2tlciIsImNyZWF0ZVRyYWNrZXIiLCJtb3ZlIiwiY3VycmVudCIsInJlcGxhY2UiLCJjaGVjayIsIiQwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js\n");

/***/ })

};
;