"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-arborist";
exports.ids = ["vendor-chunks/react-arborist"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/cursor.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/cursor.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cursor: () => (/* binding */ Cursor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n\n\nfunction Cursor() {\n    var _a, _b;\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useTreeApi)();\n    const state = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useDndContext)();\n    const cursor = state.cursor;\n    if (!cursor || cursor.type !== \"line\") return null;\n    const indent = tree.indent;\n    const top = tree.rowHeight * cursor.index + ((_b = (_a = tree.props.padding) !== null && _a !== void 0 ? _a : tree.props.paddingTop) !== null && _b !== void 0 ? _b : 0);\n    const left = indent * cursor.level;\n    const Cursor = tree.renderCursor;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cursor, {\n        top,\n        left,\n        indent\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/cursor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/default-container.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/default-container.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultContainer: () => (/* binding */ DefaultContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_window__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-window */ \"(ssr)/./node_modules/react-window/dist/index.esm.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-arborist/dist/module/utils.js\");\n/* harmony import */ var _list_outer_element__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./list-outer-element */ \"(ssr)/./node_modules/react-arborist/dist/module/components/list-outer-element.js\");\n/* harmony import */ var _list_inner_element__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./list-inner-element */ \"(ssr)/./node_modules/react-arborist/dist/module/components/list-inner-element.js\");\n/* harmony import */ var _row_container__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./row-container */ \"(ssr)/./node_modules/react-arborist/dist/module/components/row-container.js\");\n\n\n\n\n\n\n\nlet focusSearchTerm = \"\";\nlet timeoutId = null;\n/**\n * All these keyboard shortcuts seem like they should be configurable.\n * Each operation should be a given a name and separated from\n * the event handler. Future clean up welcome.\n */ function DefaultContainer() {\n    (0,_context__WEBPACK_IMPORTED_MODULE_1__.useDataUpdates)();\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useTreeApi)();\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        role: \"tree\",\n        style: {\n            height: tree.height,\n            width: tree.width,\n            minHeight: 0,\n            minWidth: 0\n        },\n        onContextMenu: tree.props.onContextMenu,\n        onClick: tree.props.onClick,\n        tabIndex: 0,\n        onFocus: (e)=>{\n            if (!e.currentTarget.contains(e.relatedTarget)) {\n                tree.onFocus();\n            }\n        },\n        onBlur: (e)=>{\n            if (!e.currentTarget.contains(e.relatedTarget)) {\n                tree.onBlur();\n            }\n        },\n        onKeyDown: (e)=>{\n            var _a;\n            if (tree.isEditing) {\n                return;\n            }\n            if (e.key === \"Backspace\") {\n                if (!tree.props.onDelete) return;\n                const ids = Array.from(tree.selectedIds);\n                if (ids.length > 1) {\n                    let nextFocus = tree.mostRecentNode;\n                    while(nextFocus && nextFocus.isSelected){\n                        nextFocus = nextFocus.nextSibling;\n                    }\n                    if (!nextFocus) nextFocus = tree.lastNode;\n                    tree.focus(nextFocus, {\n                        scroll: false\n                    });\n                    tree.delete(Array.from(ids));\n                } else {\n                    const node = tree.focusedNode;\n                    if (node) {\n                        const sib = node.nextSibling;\n                        const parent = node.parent;\n                        tree.focus(sib || parent, {\n                            scroll: false\n                        });\n                        tree.delete(node);\n                    }\n                }\n                return;\n            }\n            if (e.key === \"Tab\" && !e.shiftKey) {\n                e.preventDefault();\n                (0,_utils__WEBPACK_IMPORTED_MODULE_2__.focusNextElement)(e.currentTarget);\n                return;\n            }\n            if (e.key === \"Tab\" && e.shiftKey) {\n                e.preventDefault();\n                (0,_utils__WEBPACK_IMPORTED_MODULE_2__.focusPrevElement)(e.currentTarget);\n                return;\n            }\n            if (e.key === \"ArrowDown\") {\n                e.preventDefault();\n                const next = tree.nextNode;\n                if (e.metaKey) {\n                    tree.select(tree.focusedNode);\n                    tree.activate(tree.focusedNode);\n                    return;\n                } else if (!e.shiftKey || tree.props.disableMultiSelection) {\n                    tree.focus(next);\n                    return;\n                } else {\n                    if (!next) return;\n                    const current = tree.focusedNode;\n                    if (!current) {\n                        tree.focus(tree.firstNode);\n                    } else if (current.isSelected) {\n                        tree.selectContiguous(next);\n                    } else {\n                        tree.selectMulti(next);\n                    }\n                    return;\n                }\n            }\n            if (e.key === \"ArrowUp\") {\n                e.preventDefault();\n                const prev = tree.prevNode;\n                if (!e.shiftKey || tree.props.disableMultiSelection) {\n                    tree.focus(prev);\n                    return;\n                } else {\n                    if (!prev) return;\n                    const current = tree.focusedNode;\n                    if (!current) {\n                        tree.focus(tree.lastNode); // ?\n                    } else if (current.isSelected) {\n                        tree.selectContiguous(prev);\n                    } else {\n                        tree.selectMulti(prev);\n                    }\n                    return;\n                }\n            }\n            if (e.key === \"ArrowRight\") {\n                const node = tree.focusedNode;\n                if (!node) return;\n                if (node.isInternal && node.isOpen) {\n                    tree.focus(tree.nextNode);\n                } else if (node.isInternal) tree.open(node.id);\n                return;\n            }\n            if (e.key === \"ArrowLeft\") {\n                const node = tree.focusedNode;\n                if (!node || node.isRoot) return;\n                if (node.isInternal && node.isOpen) tree.close(node.id);\n                else if (!((_a = node.parent) === null || _a === void 0 ? void 0 : _a.isRoot)) {\n                    tree.focus(node.parent);\n                }\n                return;\n            }\n            if (e.key === \"a\" && e.metaKey && !tree.props.disableMultiSelection) {\n                e.preventDefault();\n                tree.selectAll();\n                return;\n            }\n            if (e.key === \"a\" && !e.metaKey && tree.props.onCreate) {\n                tree.createLeaf();\n                return;\n            }\n            if (e.key === \"A\" && !e.metaKey) {\n                if (!tree.props.onCreate) return;\n                tree.createInternal();\n                return;\n            }\n            if (e.key === \"Home\") {\n                // add shift keys\n                e.preventDefault();\n                tree.focus(tree.firstNode);\n                return;\n            }\n            if (e.key === \"End\") {\n                // add shift keys\n                e.preventDefault();\n                tree.focus(tree.lastNode);\n                return;\n            }\n            if (e.key === \"Enter\") {\n                const node = tree.focusedNode;\n                if (!node) return;\n                if (!node.isEditable || !tree.props.onRename) return;\n                setTimeout(()=>{\n                    if (node) tree.edit(node);\n                });\n                return;\n            }\n            if (e.key === \" \") {\n                e.preventDefault();\n                const node = tree.focusedNode;\n                if (!node) return;\n                if (node.isLeaf) {\n                    node.select();\n                    node.activate();\n                } else {\n                    node.toggle();\n                }\n                return;\n            }\n            if (e.key === \"*\") {\n                const node = tree.focusedNode;\n                if (!node) return;\n                tree.openSiblings(node);\n                return;\n            }\n            if (e.key === \"PageUp\") {\n                e.preventDefault();\n                tree.pageUp();\n                return;\n            }\n            if (e.key === \"PageDown\") {\n                e.preventDefault();\n                tree.pageDown();\n            }\n            // If they type a sequence of characters\n            // collect them. Reset them after a timeout.\n            // Use it to search the tree for a node, then focus it.\n            // Clean this up a bit later\n            clearTimeout(timeoutId);\n            focusSearchTerm += e.key;\n            timeoutId = setTimeout(()=>{\n                focusSearchTerm = \"\";\n            }, 600);\n            const node = tree.visibleNodes.find((n)=>{\n                // @ts-ignore\n                const name = n.data.name;\n                if (typeof name === \"string\") {\n                    return name.toLowerCase().startsWith(focusSearchTerm);\n                } else return false;\n            });\n            if (node) tree.focus(node.id);\n        },\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_window__WEBPACK_IMPORTED_MODULE_3__.FixedSizeList, {\n            className: tree.props.className,\n            outerRef: tree.listEl,\n            itemCount: tree.visibleNodes.length,\n            height: tree.height,\n            width: tree.width,\n            itemSize: tree.rowHeight,\n            overscanCount: tree.overscanCount,\n            itemKey: (index)=>{\n                var _a;\n                return ((_a = tree.visibleNodes[index]) === null || _a === void 0 ? void 0 : _a.id) || index;\n            },\n            outerElementType: _list_outer_element__WEBPACK_IMPORTED_MODULE_4__.ListOuterElement,\n            innerElementType: _list_inner_element__WEBPACK_IMPORTED_MODULE_5__.ListInnerElement,\n            onScroll: tree.props.onScroll,\n            onItemsRendered: tree.onItemsRendered.bind(tree),\n            ref: tree.list,\n            children: _row_container__WEBPACK_IMPORTED_MODULE_6__.RowContainer\n        })\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/default-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/default-cursor.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/default-cursor.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultCursor: () => (/* binding */ DefaultCursor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst placeholderStyle = {\n    display: \"flex\",\n    alignItems: \"center\",\n    zIndex: 1\n};\nconst lineStyle = {\n    flex: 1,\n    height: \"2px\",\n    background: \"#4B91E2\",\n    borderRadius: \"1px\"\n};\nconst circleStyle = {\n    width: \"4px\",\n    height: \"4px\",\n    boxShadow: \"0 0 0 3px #4B91E2\",\n    borderRadius: \"50%\"\n};\nconst DefaultCursor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function DefaultCursor({ top, left, indent }) {\n    const style = {\n        position: \"absolute\",\n        pointerEvents: \"none\",\n        top: top - 2 + \"px\",\n        left: left + \"px\",\n        right: indent + \"px\"\n    };\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        style: Object.assign(Object.assign({}, placeholderStyle), style),\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: Object.assign({}, circleStyle)\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: Object.assign({}, lineStyle)\n            })\n        ]\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/default-cursor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/default-drag-preview.js":
/*!************************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/default-drag-preview.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultDragPreview: () => (/* binding */ DefaultDragPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n\n\n\nconst layerStyles = {\n    position: \"fixed\",\n    pointerEvents: \"none\",\n    zIndex: 100,\n    left: 0,\n    top: 0,\n    width: \"100%\",\n    height: \"100%\"\n};\nconst getStyle = (offset)=>{\n    if (!offset) return {\n        display: \"none\"\n    };\n    const { x, y } = offset;\n    return {\n        transform: `translate(${x}px, ${y}px)`\n    };\n};\nconst getCountStyle = (offset)=>{\n    if (!offset) return {\n        display: \"none\"\n    };\n    const { x, y } = offset;\n    return {\n        transform: `translate(${x + 10}px, ${y + 10}px)`\n    };\n};\nfunction DefaultDragPreview({ offset, mouse, id, dragIds, isDragging }) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(Overlay, {\n        isDragging: isDragging,\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Position, {\n                offset: offset,\n                children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PreviewNode, {\n                    id: id,\n                    dragIds: dragIds\n                })\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Count, {\n                mouse: mouse,\n                count: dragIds.length\n            })\n        ]\n    });\n}\nconst Overlay = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function Overlay(props) {\n    if (!props.isDragging) return null;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        style: layerStyles,\n        children: props.children\n    });\n});\nfunction Position(props) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: \"row preview\",\n        style: getStyle(props.offset),\n        children: props.children\n    });\n}\nfunction Count(props) {\n    const { count, mouse } = props;\n    if (count > 1) return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: \"selected-count\",\n        style: getCountStyle(mouse),\n        children: count\n    });\n    else return null;\n}\nconst PreviewNode = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function PreviewNode(props) {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_2__.useTreeApi)();\n    const node = tree.get(props.id);\n    if (!node) return null;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(tree.renderNode, {\n        preview: true,\n        node: node,\n        style: {\n            paddingLeft: node.level * tree.indent,\n            opacity: 0.2,\n            background: \"transparent\"\n        },\n        tree: tree\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/default-drag-preview.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/default-node.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/default-node.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultNode: () => (/* binding */ DefaultNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction DefaultNode(props) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        ref: props.dragHandle,\n        style: props.style,\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    props.node.toggle();\n                },\n                children: props.node.isLeaf ? \"\\uD83C\\uDF33\" : props.node.isOpen ? \"\\uD83D\\uDDC1\" : \"\\uD83D\\uDDC0\"\n            }),\n            \" \",\n            props.node.isEditing ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Edit, Object.assign({}, props)) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Show, Object.assign({}, props))\n        ]\n    });\n}\nfunction Show(props) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n            children: props.node.data.name\n        })\n    });\n}\nfunction Edit({ node }) {\n    const input = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _a, _b;\n        (_a = input.current) === null || _a === void 0 ? void 0 : _a.focus();\n        (_b = input.current) === null || _b === void 0 ? void 0 : _b.select();\n    }, []);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"input\", {\n        ref: input,\n        // @ts-ignore\n        defaultValue: node.data.name,\n        onBlur: ()=>node.reset(),\n        onKeyDown: (e)=>{\n            var _a;\n            if (e.key === \"Escape\") node.reset();\n            if (e.key === \"Enter\") node.submit(((_a = input.current) === null || _a === void 0 ? void 0 : _a.value) || \"\");\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/default-node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/default-row.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/default-row.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultRow: () => (/* binding */ DefaultRow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction DefaultRow({ node, attrs, innerRef, children }) {\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({}, attrs, {\n        ref: innerRef,\n        onFocus: (e)=>e.stopPropagation(),\n        onClick: node.handleClick,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvY29tcG9uZW50cy9kZWZhdWx0LXJvdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7QUFDekMsU0FBU0UsV0FBVyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFQyxRQUFRLEVBQUc7SUFDM0QsT0FBUUwsc0RBQUlBLENBQUMsT0FBT00sT0FBT0MsTUFBTSxDQUFDLENBQUMsR0FBR0osT0FBTztRQUFFSyxLQUFLSjtRQUFVSyxTQUFTLENBQUNDLElBQU1BLEVBQUVDLGVBQWU7UUFBSUMsU0FBU1YsS0FBS1csV0FBVztRQUFFUixVQUFVQTtJQUFTO0FBQ3JKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWFyYm9yaXN0L2Rpc3QvbW9kdWxlL2NvbXBvbmVudHMvZGVmYXVsdC1yb3cuanM/Yjg1NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZXhwb3J0IGZ1bmN0aW9uIERlZmF1bHRSb3coeyBub2RlLCBhdHRycywgaW5uZXJSZWYsIGNoaWxkcmVuLCB9KSB7XG4gICAgcmV0dXJuIChfanN4KFwiZGl2XCIsIE9iamVjdC5hc3NpZ24oe30sIGF0dHJzLCB7IHJlZjogaW5uZXJSZWYsIG9uRm9jdXM6IChlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpLCBvbkNsaWNrOiBub2RlLmhhbmRsZUNsaWNrLCBjaGlsZHJlbjogY2hpbGRyZW4gfSkpKTtcbn1cbiJdLCJuYW1lcyI6WyJqc3giLCJfanN4IiwiRGVmYXVsdFJvdyIsIm5vZGUiLCJhdHRycyIsImlubmVyUmVmIiwiY2hpbGRyZW4iLCJPYmplY3QiLCJhc3NpZ24iLCJyZWYiLCJvbkZvY3VzIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsIm9uQ2xpY2siLCJoYW5kbGVDbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/default-row.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/drag-preview-container.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/drag-preview-container.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragPreviewContainer: () => (/* binding */ DragPreviewContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDragLayer.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _default_drag_preview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./default-drag-preview */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-drag-preview.js\");\n\n\n\n\nfunction DragPreviewContainer() {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useTreeApi)();\n    const { offset, mouse, item, isDragging } = (0,react_dnd__WEBPACK_IMPORTED_MODULE_2__.useDragLayer)((m)=>{\n        return {\n            offset: m.getSourceClientOffset(),\n            mouse: m.getClientOffset(),\n            item: m.getItem(),\n            isDragging: m.isDragging()\n        };\n    });\n    const DragPreview = tree.props.renderDragPreview || _default_drag_preview__WEBPACK_IMPORTED_MODULE_3__.DefaultDragPreview;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DragPreview, {\n        offset: offset,\n        mouse: mouse,\n        id: (item === null || item === void 0 ? void 0 : item.id) || null,\n        dragIds: (item === null || item === void 0 ? void 0 : item.dragIds) || [],\n        isDragging: isDragging\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/drag-preview-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/list-inner-element.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/list-inner-element.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListInnerElement: () => (/* binding */ ListInnerElement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\nconst ListInnerElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function InnerElement(_a, ref) {\n    var _b, _c, _d, _e;\n    var { style } = _a, rest = __rest(_a, [\n        \"style\"\n    ]);\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_2__.useTreeApi)();\n    const paddingTop = (_c = (_b = tree.props.padding) !== null && _b !== void 0 ? _b : tree.props.paddingTop) !== null && _c !== void 0 ? _c : 0;\n    const paddingBottom = (_e = (_d = tree.props.padding) !== null && _d !== void 0 ? _d : tree.props.paddingBottom) !== null && _e !== void 0 ? _e : 0;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", Object.assign({\n        ref: ref,\n        style: Object.assign(Object.assign({}, style), {\n            height: `${parseFloat(style.height) + paddingTop + paddingBottom}px`\n        })\n    }, rest));\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/list-inner-element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/list-outer-element.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/list-outer-element.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListOuterElement: () => (/* binding */ ListOuterElement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _cursor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cursor */ \"(ssr)/./node_modules/react-arborist/dist/module/components/cursor.js\");\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\nconst ListOuterElement = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function Outer(props, ref) {\n    const { children } = props, rest = __rest(props, [\n        \"children\"\n    ]);\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_2__.useTreeApi)();\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", Object.assign({\n        // @ts-ignore\n        ref: ref\n    }, rest, {\n        onClick: (e)=>{\n            if (e.currentTarget === e.target) tree.deselectAll();\n        },\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DropContainer, {}),\n            children\n        ]\n    }));\n});\nconst DropContainer = ()=>{\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_2__.useTreeApi)();\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        style: {\n            height: tree.visibleNodes.length * tree.rowHeight,\n            width: \"100%\",\n            position: \"absolute\",\n            left: \"0\",\n            right: \"0\"\n        },\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cursor__WEBPACK_IMPORTED_MODULE_3__.Cursor, {})\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/list-outer-element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/outer-drop.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/outer-drop.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OuterDrop: () => (/* binding */ OuterDrop)\n/* harmony export */ });\n/* harmony import */ var _dnd_outer_drop_hook__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dnd/outer-drop-hook */ \"(ssr)/./node_modules/react-arborist/dist/module/dnd/outer-drop-hook.js\");\n\nfunction OuterDrop(props) {\n    (0,_dnd_outer_drop_hook__WEBPACK_IMPORTED_MODULE_0__.useOuterDrop)();\n    return props.children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvY29tcG9uZW50cy9vdXRlci1kcm9wLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEO0FBQy9DLFNBQVNDLFVBQVVDLEtBQUs7SUFDM0JGLGtFQUFZQTtJQUNaLE9BQU9FLE1BQU1DLFFBQVE7QUFDekIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvY29tcG9uZW50cy9vdXRlci1kcm9wLmpzP2YxMmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlT3V0ZXJEcm9wIH0gZnJvbSBcIi4uL2RuZC9vdXRlci1kcm9wLWhvb2tcIjtcbmV4cG9ydCBmdW5jdGlvbiBPdXRlckRyb3AocHJvcHMpIHtcbiAgICB1c2VPdXRlckRyb3AoKTtcbiAgICByZXR1cm4gcHJvcHMuY2hpbGRyZW47XG59XG4iXSwibmFtZXMiOlsidXNlT3V0ZXJEcm9wIiwiT3V0ZXJEcm9wIiwicHJvcHMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/outer-drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/provider.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/provider.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeProvider: () => (/* binding */ TreeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _interfaces_tree_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../interfaces/tree-api */ \"(ssr)/./node_modules/react-arborist/dist/module/interfaces/tree-api.js\");\n/* harmony import */ var _state_initial__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../state/initial */ \"(ssr)/./node_modules/react-arborist/dist/module/state/initial.js\");\n/* harmony import */ var _state_root_reducer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../state/root-reducer */ \"(ssr)/./node_modules/react-arborist/dist/module/state/root-reducer.js\");\n/* harmony import */ var react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-dnd-html5-backend */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/index.js\");\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/esm/core/DndProvider.js\");\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var _state_open_slice__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/open-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/open-slice.js\");\n\n\n\n\n\n\n\n\n\n\n\nconst SERVER_STATE = (0,_state_initial__WEBPACK_IMPORTED_MODULE_3__.initialState)();\nfunction TreeProvider({ treeProps, imperativeHandle, children }) {\n    const list = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const listEl = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(// @ts-ignore\n    (0,redux__WEBPACK_IMPORTED_MODULE_4__.createStore)(_state_root_reducer__WEBPACK_IMPORTED_MODULE_5__.rootReducer, (0,_state_initial__WEBPACK_IMPORTED_MODULE_3__.initialState)(treeProps)));\n    const state = (0,use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore)(store.current.subscribe, store.current.getState, ()=>SERVER_STATE);\n    /* The tree api object is stable. */ const api = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return new _interfaces_tree_api__WEBPACK_IMPORTED_MODULE_6__.TreeApi(store.current, treeProps, list, listEl);\n    }, []);\n    /* Make sure the tree instance stays in sync */ const updateCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        updateCount.current += 1;\n        api.update(treeProps);\n    }, [\n        ...Object.values(treeProps),\n        state.nodes.open\n    ]);\n    /* Expose the tree api */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(imperativeHandle, ()=>api);\n    /* Change selection based on props */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (api.props.selection) {\n            api.select(api.props.selection, {\n                focus: false\n            });\n        } else {\n            api.deselectAll();\n        }\n    }, [\n        api.props.selection\n    ]);\n    /* Clear visability for filtered nodes */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!api.props.searchTerm) {\n            store.current.dispatch(_state_open_slice__WEBPACK_IMPORTED_MODULE_7__.actions.clear(true));\n        }\n    }, [\n        api.props.searchTerm\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context__WEBPACK_IMPORTED_MODULE_8__.TreeApiContext.Provider, {\n        value: api,\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context__WEBPACK_IMPORTED_MODULE_8__.DataUpdatesContext.Provider, {\n            value: updateCount.current,\n            children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context__WEBPACK_IMPORTED_MODULE_8__.NodesContext.Provider, {\n                value: state.nodes,\n                children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context__WEBPACK_IMPORTED_MODULE_8__.DndContext.Provider, {\n                    value: state.dnd,\n                    children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_dnd__WEBPACK_IMPORTED_MODULE_9__.DndProvider, Object.assign({\n                        backend: react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_10__.HTML5Backend,\n                        options: {\n                            rootElement: api.props.dndRootElement || undefined\n                        }\n                    }, treeProps.dndManager && {\n                        manager: treeProps.dndManager\n                    }, {\n                        children: children\n                    }))\n                })\n            })\n        })\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/row-container.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/row-container.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RowContainer: () => (/* binding */ RowContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _dnd_drag_hook__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dnd/drag-hook */ \"(ssr)/./node_modules/react-arborist/dist/module/dnd/drag-hook.js\");\n/* harmony import */ var _dnd_drop_hook__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dnd/drop-hook */ \"(ssr)/./node_modules/react-arborist/dist/module/dnd/drop-hook.js\");\n/* harmony import */ var _hooks_use_fresh_node__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/use-fresh-node */ \"(ssr)/./node_modules/react-arborist/dist/module/hooks/use-fresh-node.js\");\n\n\n\n\n\n\nconst RowContainer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(function RowContainer({ index, style }) {\n    /* When will the <Row> will re-render.\n     *\n     * The row component is memo'd so it will only render\n     * when a new instance of the NodeApi class is passed\n     * to it.\n     *\n     * The TreeApi instance is stable. It does not\n     * change when the internal state changes.\n     *\n     * The TreeApi has all the references to the nodes.\n     * We need to clone the nodes when their state\n     * changes. The node class contains no state itself,\n     * It always checks the tree for state. The tree's\n     * state will always be up to date.\n     */ (0,_context__WEBPACK_IMPORTED_MODULE_2__.useDataUpdates)(); // Re-render when tree props or visability changes\n    const _ = (0,_context__WEBPACK_IMPORTED_MODULE_2__.useNodesContext)(); // So that we re-render appropriately\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_2__.useTreeApi)(); // Tree already has the fresh state\n    const node = (0,_hooks_use_fresh_node__WEBPACK_IMPORTED_MODULE_3__.useFreshNode)(index);\n    const el = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragRef = (0,_dnd_drag_hook__WEBPACK_IMPORTED_MODULE_4__.useDragHook)(node);\n    const dropRef = (0,_dnd_drop_hook__WEBPACK_IMPORTED_MODULE_5__.useDropHook)(el, node);\n    const innerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((n)=>{\n        el.current = n;\n        dropRef(n);\n    }, [\n        dropRef\n    ]);\n    const indent = tree.indent * node.level;\n    const nodeStyle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            paddingLeft: indent\n        }), [\n        indent\n    ]);\n    const rowStyle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _a, _b;\n        return Object.assign(Object.assign({}, style), {\n            top: parseFloat(style.top) + ((_b = (_a = tree.props.padding) !== null && _a !== void 0 ? _a : tree.props.paddingTop) !== null && _b !== void 0 ? _b : 0)\n        });\n    }, [\n        style,\n        tree.props.padding,\n        tree.props.paddingTop\n    ]);\n    const rowAttrs = {\n        role: \"treeitem\",\n        \"aria-level\": node.level + 1,\n        \"aria-selected\": node.isSelected,\n        \"aria-expanded\": node.isOpen,\n        style: rowStyle,\n        tabIndex: -1,\n        className: tree.props.rowClassName\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _a;\n        if (!node.isEditing && node.isFocused) {\n            (_a = el.current) === null || _a === void 0 ? void 0 : _a.focus({\n                preventScroll: true\n            });\n        }\n    }, [\n        node.isEditing,\n        node.isFocused,\n        el.current\n    ]);\n    const Node = tree.renderNode;\n    const Row = tree.renderRow;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Row, {\n        node: node,\n        innerRef: innerRef,\n        attrs: rowAttrs,\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Node, {\n            node: node,\n            tree: tree,\n            style: nodeStyle,\n            dragHandle: dragRef\n        })\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/row-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/tree-container.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/tree-container.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeContainer: () => (/* binding */ TreeContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _default_container__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./default-container */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-container.js\");\n\n\n\nfunction TreeContainer() {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useTreeApi)();\n    const Container = tree.props.renderContainer || _default_container__WEBPACK_IMPORTED_MODULE_2__.DefaultContainer;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Container, {})\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvY29tcG9uZW50cy90cmVlLWNvbnRhaW5lci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF1RTtBQUMvQjtBQUNlO0FBQ2hELFNBQVNNO0lBQ1osTUFBTUMsT0FBT0gsb0RBQVVBO0lBQ3ZCLE1BQU1JLFlBQVlELEtBQUtFLEtBQUssQ0FBQ0MsZUFBZSxJQUFJTCxnRUFBZ0JBO0lBQ2hFLE9BQVFKLHNEQUFJQSxDQUFDRSx1REFBU0EsRUFBRTtRQUFFUSxVQUFVVixzREFBSUEsQ0FBQ08sV0FBVyxDQUFDO0lBQUc7QUFDNUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvY29tcG9uZW50cy90cmVlLWNvbnRhaW5lci5qcz81YTAwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4LCBGcmFnbWVudCBhcyBfRnJhZ21lbnQgfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCB7IHVzZVRyZWVBcGkgfSBmcm9tIFwiLi4vY29udGV4dFwiO1xuaW1wb3J0IHsgRGVmYXVsdENvbnRhaW5lciB9IGZyb20gXCIuL2RlZmF1bHQtY29udGFpbmVyXCI7XG5leHBvcnQgZnVuY3Rpb24gVHJlZUNvbnRhaW5lcigpIHtcbiAgICBjb25zdCB0cmVlID0gdXNlVHJlZUFwaSgpO1xuICAgIGNvbnN0IENvbnRhaW5lciA9IHRyZWUucHJvcHMucmVuZGVyQ29udGFpbmVyIHx8IERlZmF1bHRDb250YWluZXI7XG4gICAgcmV0dXJuIChfanN4KF9GcmFnbWVudCwgeyBjaGlsZHJlbjogX2pzeChDb250YWluZXIsIHt9KSB9KSk7XG59XG4iXSwibmFtZXMiOlsianN4IiwiX2pzeCIsIkZyYWdtZW50IiwiX0ZyYWdtZW50IiwidXNlVHJlZUFwaSIsIkRlZmF1bHRDb250YWluZXIiLCJUcmVlQ29udGFpbmVyIiwidHJlZSIsIkNvbnRhaW5lciIsInByb3BzIiwicmVuZGVyQ29udGFpbmVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/tree-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/components/tree.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/components/tree.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tree: () => (/* binding */ Tree)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./provider */ \"(ssr)/./node_modules/react-arborist/dist/module/components/provider.js\");\n/* harmony import */ var _outer_drop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./outer-drop */ \"(ssr)/./node_modules/react-arborist/dist/module/components/outer-drop.js\");\n/* harmony import */ var _tree_container__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tree-container */ \"(ssr)/./node_modules/react-arborist/dist/module/components/tree-container.js\");\n/* harmony import */ var _drag_preview_container__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./drag-preview-container */ \"(ssr)/./node_modules/react-arborist/dist/module/components/drag-preview-container.js\");\n/* harmony import */ var _hooks_use_validated_props__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/use-validated-props */ \"(ssr)/./node_modules/react-arborist/dist/module/hooks/use-validated-props.js\");\n\n\n\n\n\n\n\nfunction TreeComponent(props, ref) {\n    const treeProps = (0,_hooks_use_validated_props__WEBPACK_IMPORTED_MODULE_2__.useValidatedProps)(props);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_provider__WEBPACK_IMPORTED_MODULE_3__.TreeProvider, {\n        treeProps: treeProps,\n        imperativeHandle: ref,\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_outer_drop__WEBPACK_IMPORTED_MODULE_4__.OuterDrop, {\n                children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tree_container__WEBPACK_IMPORTED_MODULE_5__.TreeContainer, {})\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_drag_preview_container__WEBPACK_IMPORTED_MODULE_6__.DragPreviewContainer, {})\n        ]\n    });\n}\nconst Tree = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(TreeComponent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/components/tree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/context.js":
/*!************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/context.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataUpdatesContext: () => (/* binding */ DataUpdatesContext),\n/* harmony export */   DndContext: () => (/* binding */ DndContext),\n/* harmony export */   NodesContext: () => (/* binding */ NodesContext),\n/* harmony export */   TreeApiContext: () => (/* binding */ TreeApiContext),\n/* harmony export */   useDataUpdates: () => (/* binding */ useDataUpdates),\n/* harmony export */   useDndContext: () => (/* binding */ useDndContext),\n/* harmony export */   useNodesContext: () => (/* binding */ useNodesContext),\n/* harmony export */   useTreeApi: () => (/* binding */ useTreeApi)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TreeApiContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nfunction useTreeApi() {\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TreeApiContext);\n    if (value === null) throw new Error(\"No Tree Api Provided\");\n    return value;\n}\nconst NodesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nfunction useNodesContext() {\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(NodesContext);\n    if (value === null) throw new Error(\"Provide a NodesContext\");\n    return value;\n}\nconst DndContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nfunction useDndContext() {\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DndContext);\n    if (value === null) throw new Error(\"Provide a DnDContext\");\n    return value;\n}\nconst DataUpdatesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(0);\nfunction useDataUpdates() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DataUpdatesContext);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/data/create-index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/data/create-index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createIndex: () => (/* binding */ createIndex)\n/* harmony export */ });\nconst createIndex = (nodes)=>{\n    return nodes.reduce((map, node, index)=>{\n        map[node.id] = index;\n        return map;\n    }, {});\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvZGF0YS9jcmVhdGUtaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLGNBQWMsQ0FBQ0M7SUFDeEIsT0FBT0EsTUFBTUMsTUFBTSxDQUFDLENBQUNDLEtBQUtDLE1BQU1DO1FBQzVCRixHQUFHLENBQUNDLEtBQUtFLEVBQUUsQ0FBQyxHQUFHRDtRQUNmLE9BQU9GO0lBQ1gsR0FBRyxDQUFDO0FBQ1IsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1hcmJvcmlzdC9kaXN0L21vZHVsZS9kYXRhL2NyZWF0ZS1pbmRleC5qcz9lMjkyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjcmVhdGVJbmRleCA9IChub2RlcykgPT4ge1xuICAgIHJldHVybiBub2Rlcy5yZWR1Y2UoKG1hcCwgbm9kZSwgaW5kZXgpID0+IHtcbiAgICAgICAgbWFwW25vZGUuaWRdID0gaW5kZXg7XG4gICAgICAgIHJldHVybiBtYXA7XG4gICAgfSwge30pO1xufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVJbmRleCIsIm5vZGVzIiwicmVkdWNlIiwibWFwIiwibm9kZSIsImluZGV4IiwiaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/data/create-index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/data/create-list.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/data/create-list.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createList: () => (/* binding */ createList)\n/* harmony export */ });\nfunction createList(tree) {\n    if (tree.isFiltered) {\n        return flattenAndFilterTree(tree.root, tree.isMatch.bind(tree));\n    } else {\n        return flattenTree(tree.root);\n    }\n}\nfunction flattenTree(root) {\n    const list = [];\n    function collect(node) {\n        var _a;\n        if (node.level >= 0) {\n            list.push(node);\n        }\n        if (node.isOpen) {\n            (_a = node.children) === null || _a === void 0 ? void 0 : _a.forEach(collect);\n        }\n    }\n    collect(root);\n    list.forEach(assignRowIndex);\n    return list;\n}\nfunction flattenAndFilterTree(root, isMatch) {\n    const matches = {};\n    const list = [];\n    function markMatch(node) {\n        const yes = !node.isRoot && isMatch(node);\n        if (yes) {\n            matches[node.id] = true;\n            let parent = node.parent;\n            while(parent){\n                matches[parent.id] = true;\n                parent = parent.parent;\n            }\n        }\n        if (node.children) {\n            for (let child of node.children)markMatch(child);\n        }\n    }\n    function collect(node) {\n        var _a;\n        if (node.level >= 0 && matches[node.id]) {\n            list.push(node);\n        }\n        if (node.isOpen) {\n            (_a = node.children) === null || _a === void 0 ? void 0 : _a.forEach(collect);\n        }\n    }\n    markMatch(root);\n    collect(root);\n    list.forEach(assignRowIndex);\n    return list;\n}\nfunction assignRowIndex(node, index) {\n    node.rowIndex = index;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/data/create-list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/data/create-root.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/data/create-root.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ROOT_ID: () => (/* binding */ ROOT_ID),\n/* harmony export */   createRoot: () => (/* binding */ createRoot)\n/* harmony export */ });\n/* harmony import */ var _interfaces_node_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../interfaces/node-api */ \"(ssr)/./node_modules/react-arborist/dist/module/interfaces/node-api.js\");\n\nconst ROOT_ID = \"__REACT_ARBORIST_INTERNAL_ROOT__\";\nfunction createRoot(tree) {\n    var _a;\n    function visitSelfAndChildren(data, level, parent) {\n        const id = tree.accessId(data);\n        const node = new _interfaces_node_api__WEBPACK_IMPORTED_MODULE_0__.NodeApi({\n            tree,\n            data,\n            level,\n            parent,\n            id,\n            children: null,\n            isDraggable: tree.isDraggable(data),\n            rowIndex: null\n        });\n        const children = tree.accessChildren(data);\n        if (children) {\n            node.children = children.map((child)=>visitSelfAndChildren(child, level + 1, node));\n        }\n        return node;\n    }\n    const root = new _interfaces_node_api__WEBPACK_IMPORTED_MODULE_0__.NodeApi({\n        tree,\n        id: ROOT_ID,\n        // @ts-ignore\n        data: {\n            id: ROOT_ID\n        },\n        level: -1,\n        parent: null,\n        children: null,\n        isDraggable: true,\n        rowIndex: null\n    });\n    const data = (_a = tree.props.data) !== null && _a !== void 0 ? _a : [];\n    root.children = data.map((child)=>{\n        return visitSelfAndChildren(child, 0, root);\n    });\n    return root;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/data/create-root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/data/simple-tree.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/data/simple-tree.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleTree: () => (/* binding */ SimpleTree)\n/* harmony export */ });\nclass SimpleTree {\n    constructor(data){\n        this.root = createRoot(data);\n    }\n    get data() {\n        var _a, _b;\n        return (_b = (_a = this.root.children) === null || _a === void 0 ? void 0 : _a.map((node)=>node.data)) !== null && _b !== void 0 ? _b : [];\n    }\n    create(args) {\n        const parent = args.parentId ? this.find(args.parentId) : this.root;\n        if (!parent) return null;\n        parent.addChild(args.data, args.index);\n    }\n    move(args) {\n        const src = this.find(args.id);\n        const parent = args.parentId ? this.find(args.parentId) : this.root;\n        if (!src || !parent) return;\n        parent.addChild(src.data, args.index);\n        src.drop();\n    }\n    update(args) {\n        const node = this.find(args.id);\n        if (node) node.update(args.changes);\n    }\n    drop(args) {\n        const node = this.find(args.id);\n        if (node) node.drop();\n    }\n    find(id, node = this.root) {\n        if (!node) return null;\n        if (node.id === id) return node;\n        if (node.children) {\n            for (let child of node.children){\n                const found = this.find(id, child);\n                if (found) return found;\n            }\n            return null;\n        }\n        return null;\n    }\n}\nfunction createRoot(data) {\n    const root = new SimpleNode({\n        id: \"ROOT\"\n    }, null);\n    root.children = data.map((d)=>createNode(d, root));\n    return root;\n}\nfunction createNode(data, parent) {\n    const node = new SimpleNode(data, parent);\n    if (data.children) node.children = data.children.map((d)=>createNode(d, node));\n    return node;\n}\nclass SimpleNode {\n    constructor(data, parent){\n        this.data = data;\n        this.parent = parent;\n        this.id = data.id;\n    }\n    hasParent() {\n        return !!this.parent;\n    }\n    get childIndex() {\n        return this.hasParent() ? this.parent.children.indexOf(this) : -1;\n    }\n    addChild(data, index) {\n        var _a, _b;\n        const node = createNode(data, this);\n        this.children = (_a = this.children) !== null && _a !== void 0 ? _a : [];\n        this.children.splice(index, 0, node);\n        this.data.children = (_b = this.data.children) !== null && _b !== void 0 ? _b : [];\n        this.data.children.splice(index, 0, data);\n    }\n    removeChild(index) {\n        var _a, _b;\n        (_a = this.children) === null || _a === void 0 ? void 0 : _a.splice(index, 1);\n        (_b = this.data.children) === null || _b === void 0 ? void 0 : _b.splice(index, 1);\n    }\n    update(changes) {\n        if (this.hasParent()) {\n            const i = this.childIndex;\n            this.parent.addChild(Object.assign(Object.assign({}, this.data), changes), i);\n            this.drop();\n        }\n    }\n    drop() {\n        if (this.hasParent()) this.parent.removeChild(this.childIndex);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/data/simple-tree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/dnd/compute-drop.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/dnd/compute-drop.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   computeDrop: () => (/* binding */ computeDrop)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-arborist/dist/module/utils.js\");\n\nfunction measureHover(el, offset) {\n    const rect = el.getBoundingClientRect();\n    const x = offset.x - Math.round(rect.x);\n    const y = offset.y - Math.round(rect.y);\n    const height = rect.height;\n    const inTopHalf = y < height / 2;\n    const inBottomHalf = !inTopHalf;\n    const pad = height / 4;\n    const inMiddle = y > pad && y < height - pad;\n    const atTop = !inMiddle && inTopHalf;\n    const atBottom = !inMiddle && inBottomHalf;\n    return {\n        x,\n        inTopHalf,\n        inBottomHalf,\n        inMiddle,\n        atTop,\n        atBottom\n    };\n}\nfunction getNodesAroundCursor(node, prev, next, hover) {\n    if (!node) {\n        // We're hovering over the empty part of the list, not over an item,\n        // Put the cursor below the last item which is \"prev\"\n        return [\n            prev,\n            null\n        ];\n    }\n    if (node.isInternal) {\n        if (hover.atTop) {\n            return [\n                prev,\n                node\n            ];\n        } else if (hover.inMiddle) {\n            return [\n                node,\n                node\n            ];\n        } else {\n            return [\n                node,\n                next\n            ];\n        }\n    } else {\n        if (hover.inTopHalf) {\n            return [\n                prev,\n                node\n            ];\n        } else {\n            return [\n                node,\n                next\n            ];\n        }\n    }\n}\nfunction dropAt(parentId, index) {\n    return {\n        parentId: parentId || null,\n        index\n    };\n}\nfunction lineCursor(index, level) {\n    return {\n        type: \"line\",\n        index,\n        level\n    };\n}\nfunction noCursor() {\n    return {\n        type: \"none\"\n    };\n}\nfunction highlightCursor(id) {\n    return {\n        type: \"highlight\",\n        id\n    };\n}\nfunction walkUpFrom(node, level) {\n    var _a;\n    let drop = node;\n    while(drop.parent && drop.level > level){\n        drop = drop.parent;\n    }\n    const parentId = ((_a = drop.parent) === null || _a === void 0 ? void 0 : _a.id) || null;\n    const index = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.indexOf)(drop) + 1;\n    return {\n        parentId,\n        index\n    };\n}\n/**\n * This is the most complex, tricky function in the whole repo.\n */ function computeDrop(args) {\n    var _a;\n    const hover = measureHover(args.element, args.offset);\n    const indent = args.indent;\n    const hoverLevel = Math.round(Math.max(0, hover.x - indent) / indent);\n    const { node, nextNode, prevNode } = args;\n    const [above, below] = getNodesAroundCursor(node, prevNode, nextNode, hover);\n    /* Hovering over the middle of a folder */ if (node && node.isInternal && hover.inMiddle) {\n        return {\n            drop: dropAt(node.id, null),\n            cursor: highlightCursor(node.id)\n        };\n    }\n    /*\n     * Now we only need to care about the node above the cursor\n     * -----------                            -------\n     */ /* There is no node above the cursor line */ if (!above) {\n        return {\n            drop: dropAt((_a = below === null || below === void 0 ? void 0 : below.parent) === null || _a === void 0 ? void 0 : _a.id, 0),\n            cursor: lineCursor(0, 0)\n        };\n    }\n    /* The node above the cursor line is an item */ if ((0,_utils__WEBPACK_IMPORTED_MODULE_0__.isItem)(above)) {\n        const level = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.bound)(hoverLevel, (below === null || below === void 0 ? void 0 : below.level) || 0, above.level);\n        return {\n            drop: walkUpFrom(above, level),\n            cursor: lineCursor(above.rowIndex + 1, level)\n        };\n    }\n    /* The node above the cursor line is a closed folder */ if ((0,_utils__WEBPACK_IMPORTED_MODULE_0__.isClosed)(above)) {\n        const level = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.bound)(hoverLevel, (below === null || below === void 0 ? void 0 : below.level) || 0, above.level);\n        return {\n            drop: walkUpFrom(above, level),\n            cursor: lineCursor(above.rowIndex + 1, level)\n        };\n    }\n    /* The node above the cursor line is an open folder with no children */ if ((0,_utils__WEBPACK_IMPORTED_MODULE_0__.isOpenWithEmptyChildren)(above)) {\n        const level = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.bound)(hoverLevel, 0, above.level + 1);\n        if (level > above.level) {\n            /* Will be the first child of the empty folder */ return {\n                drop: dropAt(above.id, 0),\n                cursor: lineCursor(above.rowIndex + 1, level)\n            };\n        } else {\n            /* Will be a sibling or grandsibling of the empty folder */ return {\n                drop: walkUpFrom(above, level),\n                cursor: lineCursor(above.rowIndex + 1, level)\n            };\n        }\n    }\n    /* The node above the cursor is a an open folder with children */ return {\n        drop: dropAt(above === null || above === void 0 ? void 0 : above.id, 0),\n        cursor: lineCursor(above.rowIndex + 1, above.level + 1)\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/dnd/compute-drop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/dnd/drag-hook.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/dnd/drag-hook.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDragHook: () => (/* binding */ useDragHook)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrag/useDrag.js\");\n/* harmony import */ var react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dnd-html5-backend */ \"(ssr)/./node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../state/dnd-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js\");\n\n\n\n\n\nfunction useDragHook(node) {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useTreeApi)();\n    const ids = tree.selectedIds;\n    const [_, ref, preview] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_2__.useDrag)(()=>({\n            canDrag: ()=>node.isDraggable,\n            type: \"NODE\",\n            item: ()=>{\n                // This is fired once at the begging of a drag operation\n                const dragIds = tree.isSelected(node.id) ? Array.from(ids) : [\n                    node.id\n                ];\n                tree.dispatch(_state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__.actions.dragStart(node.id, dragIds));\n                return {\n                    id: node.id,\n                    dragIds\n                };\n            },\n            end: ()=>{\n                tree.hideCursor();\n                tree.dispatch(_state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__.actions.dragEnd());\n            }\n        }), [\n        ids,\n        node\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        preview((0,react_dnd_html5_backend__WEBPACK_IMPORTED_MODULE_4__.getEmptyImage)());\n    }, [\n        preview\n    ]);\n    return ref;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/dnd/drag-hook.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/dnd/drop-hook.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/dnd/drop-hook.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropHook: () => (/* binding */ useDropHook)\n/* harmony export */ });\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDrop.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _compute_drop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./compute-drop */ \"(ssr)/./node_modules/react-arborist/dist/module/dnd/compute-drop.js\");\n/* harmony import */ var _state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../state/dnd-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-arborist/dist/module/utils.js\");\n/* harmony import */ var _data_create_root__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data/create-root */ \"(ssr)/./node_modules/react-arborist/dist/module/data/create-root.js\");\n\n\n\n\n\n\nfunction useDropHook(el, node) {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_0__.useTreeApi)();\n    const [_, dropRef] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_1__.useDrop)(()=>({\n            accept: \"NODE\",\n            canDrop: ()=>tree.canDrop(),\n            hover: (_item, m)=>{\n                const offset = m.getClientOffset();\n                if (!el.current || !offset) return;\n                const { cursor, drop } = (0,_compute_drop__WEBPACK_IMPORTED_MODULE_2__.computeDrop)({\n                    element: el.current,\n                    offset: offset,\n                    indent: tree.indent,\n                    node: node,\n                    prevNode: node.prev,\n                    nextNode: node.next\n                });\n                if (drop) tree.dispatch(_state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__.actions.hovering(drop.parentId, drop.index));\n                if (m.canDrop()) {\n                    if (cursor) tree.showCursor(cursor);\n                } else {\n                    tree.hideCursor();\n                }\n            },\n            drop: (_, m)=>{\n                if (!m.canDrop()) return null;\n                let { parentId, index, dragIds } = tree.state.dnd;\n                (0,_utils__WEBPACK_IMPORTED_MODULE_4__.safeRun)(tree.props.onMove, {\n                    dragIds,\n                    parentId: parentId === _data_create_root__WEBPACK_IMPORTED_MODULE_5__.ROOT_ID ? null : parentId,\n                    index: index === null ? 0 : index,\n                    dragNodes: tree.dragNodes,\n                    parentNode: tree.get(parentId)\n                });\n                tree.open(parentId);\n            }\n        }), [\n        node,\n        el.current,\n        tree.props\n    ]);\n    return dropRef;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/dnd/drop-hook.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/dnd/outer-drop-hook.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/dnd/outer-drop-hook.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOuterDrop: () => (/* binding */ useOuterDrop)\n/* harmony export */ });\n/* harmony import */ var react_dnd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dnd */ \"(ssr)/./node_modules/react-dnd/dist/esm/hooks/useDrop/useDrop.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n/* harmony import */ var _compute_drop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./compute-drop */ \"(ssr)/./node_modules/react-arborist/dist/module/dnd/compute-drop.js\");\n/* harmony import */ var _state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../state/dnd-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js\");\n\n\n\n\nfunction useOuterDrop() {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_0__.useTreeApi)();\n    // In case we drop an item at the bottom of the list\n    const [, drop] = (0,react_dnd__WEBPACK_IMPORTED_MODULE_1__.useDrop)(()=>({\n            accept: \"NODE\",\n            canDrop: (_item, m)=>{\n                if (!m.isOver({\n                    shallow: true\n                })) return false;\n                return tree.canDrop();\n            },\n            hover: (_item, m)=>{\n                if (!m.isOver({\n                    shallow: true\n                })) return;\n                const offset = m.getClientOffset();\n                if (!tree.listEl.current || !offset) return;\n                const { cursor, drop } = (0,_compute_drop__WEBPACK_IMPORTED_MODULE_2__.computeDrop)({\n                    element: tree.listEl.current,\n                    offset: offset,\n                    indent: tree.indent,\n                    node: null,\n                    prevNode: tree.visibleNodes[tree.visibleNodes.length - 1],\n                    nextNode: null\n                });\n                if (drop) tree.dispatch(_state_dnd_slice__WEBPACK_IMPORTED_MODULE_3__.actions.hovering(drop.parentId, drop.index));\n                if (m.canDrop()) {\n                    if (cursor) tree.showCursor(cursor);\n                } else {\n                    tree.hideCursor();\n                }\n            }\n        }), [\n        tree\n    ]);\n    drop(tree.listEl);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/dnd/outer-drop-hook.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/hooks/use-fresh-node.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/hooks/use-fresh-node.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFreshNode: () => (/* binding */ useFreshNode)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(ssr)/./node_modules/react-arborist/dist/module/context.js\");\n\n\nfunction useFreshNode(index) {\n    const tree = (0,_context__WEBPACK_IMPORTED_MODULE_1__.useTreeApi)();\n    const original = tree.at(index);\n    if (!original) throw new Error(`Could not find node for index: ${index}`);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const fresh = original.clone();\n        tree.visibleNodes[index] = fresh; // sneaky\n        return fresh;\n    // Return a fresh instance if the state values change\n    }, [\n        ...Object.values(original.state),\n        original\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvaG9va3MvdXNlLWZyZXNoLW5vZGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFnQztBQUNRO0FBQ2pDLFNBQVNFLGFBQWFDLEtBQUs7SUFDOUIsTUFBTUMsT0FBT0gsb0RBQVVBO0lBQ3ZCLE1BQU1JLFdBQVdELEtBQUtFLEVBQUUsQ0FBQ0g7SUFDekIsSUFBSSxDQUFDRSxVQUNELE1BQU0sSUFBSUUsTUFBTSxDQUFDLCtCQUErQixFQUFFSixNQUFNLENBQUM7SUFDN0QsT0FBT0gsOENBQU9BLENBQUM7UUFDWCxNQUFNUSxRQUFRSCxTQUFTSSxLQUFLO1FBQzVCTCxLQUFLTSxZQUFZLENBQUNQLE1BQU0sR0FBR0ssT0FBTyxTQUFTO1FBQzNDLE9BQU9BO0lBQ1AscURBQXFEO0lBQ3pELEdBQUc7V0FBSUcsT0FBT0MsTUFBTSxDQUFDUCxTQUFTUSxLQUFLO1FBQUdSO0tBQVM7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvaG9va3MvdXNlLWZyZXNoLW5vZGUuanM/NDVkYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VUcmVlQXBpIH0gZnJvbSBcIi4uL2NvbnRleHRcIjtcbmV4cG9ydCBmdW5jdGlvbiB1c2VGcmVzaE5vZGUoaW5kZXgpIHtcbiAgICBjb25zdCB0cmVlID0gdXNlVHJlZUFwaSgpO1xuICAgIGNvbnN0IG9yaWdpbmFsID0gdHJlZS5hdChpbmRleCk7XG4gICAgaWYgKCFvcmlnaW5hbClcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDb3VsZCBub3QgZmluZCBub2RlIGZvciBpbmRleDogJHtpbmRleH1gKTtcbiAgICByZXR1cm4gdXNlTWVtbygoKSA9PiB7XG4gICAgICAgIGNvbnN0IGZyZXNoID0gb3JpZ2luYWwuY2xvbmUoKTtcbiAgICAgICAgdHJlZS52aXNpYmxlTm9kZXNbaW5kZXhdID0gZnJlc2g7IC8vIHNuZWFreVxuICAgICAgICByZXR1cm4gZnJlc2g7XG4gICAgICAgIC8vIFJldHVybiBhIGZyZXNoIGluc3RhbmNlIGlmIHRoZSBzdGF0ZSB2YWx1ZXMgY2hhbmdlXG4gICAgfSwgWy4uLk9iamVjdC52YWx1ZXMob3JpZ2luYWwuc3RhdGUpLCBvcmlnaW5hbF0pO1xufVxuIl0sIm5hbWVzIjpbInVzZU1lbW8iLCJ1c2VUcmVlQXBpIiwidXNlRnJlc2hOb2RlIiwiaW5kZXgiLCJ0cmVlIiwib3JpZ2luYWwiLCJhdCIsIkVycm9yIiwiZnJlc2giLCJjbG9uZSIsInZpc2libGVOb2RlcyIsIk9iamVjdCIsInZhbHVlcyIsInN0YXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/hooks/use-fresh-node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/hooks/use-simple-tree.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/hooks/use-simple-tree.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleTree: () => (/* binding */ useSimpleTree)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _data_simple_tree__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/simple-tree */ \"(ssr)/./node_modules/react-arborist/dist/module/data/simple-tree.js\");\n\n\nlet nextId = 0;\nfunction useSimpleTree(initialData) {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialData);\n    const tree = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new _data_simple_tree__WEBPACK_IMPORTED_MODULE_1__.SimpleTree(data), [\n        data\n    ]);\n    const onMove = (args)=>{\n        for (const id of args.dragIds){\n            tree.move({\n                id,\n                parentId: args.parentId,\n                index: args.index\n            });\n        }\n        setData(tree.data);\n    };\n    const onRename = ({ name, id })=>{\n        tree.update({\n            id,\n            changes: {\n                name\n            }\n        });\n        setData(tree.data);\n    };\n    const onCreate = ({ parentId, index, type })=>{\n        const data = {\n            id: `simple-tree-id-${nextId++}`,\n            name: \"\"\n        };\n        if (type === \"internal\") data.children = [];\n        tree.create({\n            parentId,\n            index,\n            data\n        });\n        setData(tree.data);\n        return data;\n    };\n    const onDelete = (args)=>{\n        args.ids.forEach((id)=>tree.drop({\n                id\n            }));\n        setData(tree.data);\n    };\n    const controller = {\n        onMove,\n        onRename,\n        onCreate,\n        onDelete\n    };\n    return [\n        data,\n        controller\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/hooks/use-simple-tree.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/hooks/use-validated-props.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/hooks/use-validated-props.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useValidatedProps: () => (/* binding */ useValidatedProps)\n/* harmony export */ });\n/* harmony import */ var _use_simple_tree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-simple-tree */ \"(ssr)/./node_modules/react-arborist/dist/module/hooks/use-simple-tree.js\");\n\nfunction useValidatedProps(props) {\n    if (props.initialData && props.data) {\n        throw new Error(`React Arborist Tree => Provide either a data or initialData prop, but not both.`);\n    }\n    if (props.initialData && (props.onCreate || props.onDelete || props.onMove || props.onRename)) {\n        throw new Error(`React Arborist Tree => You passed the initialData prop along with a data handler.\nUse the data prop if you want to provide your own handlers.`);\n    }\n    if (props.initialData) {\n        /**\n         * Let's break the rules of hooks here. If the initialData prop\n         * is provided, we will assume it will not change for the life of\n         * the component.\n         *\n         * We will provide the real data and the handlers to update it.\n         *   */ const [data, controller] = (0,_use_simple_tree__WEBPACK_IMPORTED_MODULE_0__.useSimpleTree)(props.initialData);\n        return Object.assign(Object.assign(Object.assign({}, props), controller), {\n            data\n        });\n    } else {\n        return props;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/hooks/use-validated-props.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/interfaces/node-api.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/interfaces/node-api.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeApi: () => (/* binding */ NodeApi)\n/* harmony export */ });\n/* harmony import */ var _data_create_root__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/create-root */ \"(ssr)/./node_modules/react-arborist/dist/module/data/create-root.js\");\n\nclass NodeApi {\n    constructor(params){\n        this.handleClick = (e)=>{\n            if (e.metaKey && !this.tree.props.disableMultiSelection) {\n                this.isSelected ? this.deselect() : this.selectMulti();\n            } else if (e.shiftKey && !this.tree.props.disableMultiSelection) {\n                this.selectContiguous();\n            } else {\n                this.select();\n                this.activate();\n            }\n        };\n        this.tree = params.tree;\n        this.id = params.id;\n        this.data = params.data;\n        this.level = params.level;\n        this.children = params.children;\n        this.parent = params.parent;\n        this.isDraggable = params.isDraggable;\n        this.rowIndex = params.rowIndex;\n    }\n    get isRoot() {\n        return this.id === _data_create_root__WEBPACK_IMPORTED_MODULE_0__.ROOT_ID;\n    }\n    get isLeaf() {\n        return !Array.isArray(this.children);\n    }\n    get isInternal() {\n        return !this.isLeaf;\n    }\n    get isOpen() {\n        return this.isLeaf ? false : this.tree.isOpen(this.id);\n    }\n    get isClosed() {\n        return this.isLeaf ? false : !this.tree.isOpen(this.id);\n    }\n    get isEditable() {\n        return this.tree.isEditable(this.data);\n    }\n    get isEditing() {\n        return this.tree.editingId === this.id;\n    }\n    get isSelected() {\n        return this.tree.isSelected(this.id);\n    }\n    get isOnlySelection() {\n        return this.isSelected && this.tree.hasOneSelection;\n    }\n    get isSelectedStart() {\n        var _a;\n        return this.isSelected && !((_a = this.prev) === null || _a === void 0 ? void 0 : _a.isSelected);\n    }\n    get isSelectedEnd() {\n        var _a;\n        return this.isSelected && !((_a = this.next) === null || _a === void 0 ? void 0 : _a.isSelected);\n    }\n    get isFocused() {\n        return this.tree.isFocused(this.id);\n    }\n    get isDragging() {\n        return this.tree.isDragging(this.id);\n    }\n    get willReceiveDrop() {\n        return this.tree.willReceiveDrop(this.id);\n    }\n    get state() {\n        return {\n            isClosed: this.isClosed,\n            isDragging: this.isDragging,\n            isEditing: this.isEditing,\n            isFocused: this.isFocused,\n            isInternal: this.isInternal,\n            isLeaf: this.isLeaf,\n            isOpen: this.isOpen,\n            isSelected: this.isSelected,\n            isSelectedEnd: this.isSelectedEnd,\n            isSelectedStart: this.isSelectedStart,\n            willReceiveDrop: this.willReceiveDrop\n        };\n    }\n    get childIndex() {\n        if (this.parent && this.parent.children) {\n            return this.parent.children.findIndex((child)=>child.id === this.id);\n        } else {\n            return -1;\n        }\n    }\n    get next() {\n        if (this.rowIndex === null) return null;\n        return this.tree.at(this.rowIndex + 1);\n    }\n    get prev() {\n        if (this.rowIndex === null) return null;\n        return this.tree.at(this.rowIndex - 1);\n    }\n    get nextSibling() {\n        var _a, _b;\n        const i = this.childIndex;\n        return (_b = (_a = this.parent) === null || _a === void 0 ? void 0 : _a.children[i + 1]) !== null && _b !== void 0 ? _b : null;\n    }\n    isAncestorOf(node) {\n        if (!node) return false;\n        let ancestor = node;\n        while(ancestor){\n            if (ancestor.id === this.id) return true;\n            ancestor = ancestor.parent;\n        }\n        return false;\n    }\n    select() {\n        this.tree.select(this);\n    }\n    deselect() {\n        this.tree.deselect(this);\n    }\n    selectMulti() {\n        this.tree.selectMulti(this);\n    }\n    selectContiguous() {\n        this.tree.selectContiguous(this);\n    }\n    activate() {\n        this.tree.activate(this);\n    }\n    focus() {\n        this.tree.focus(this);\n    }\n    toggle() {\n        this.tree.toggle(this);\n    }\n    open() {\n        this.tree.open(this);\n    }\n    openParents() {\n        this.tree.openParents(this);\n    }\n    close() {\n        this.tree.close(this);\n    }\n    submit(value) {\n        this.tree.submit(this, value);\n    }\n    reset() {\n        this.tree.reset();\n    }\n    clone() {\n        return new NodeApi(Object.assign({}, this));\n    }\n    edit() {\n        return this.tree.edit(this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/interfaces/node-api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/interfaces/tree-api.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/interfaces/tree-api.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeApi: () => (/* binding */ TreeApi)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-arborist/dist/module/utils.js\");\n/* harmony import */ var _components_default_cursor__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../components/default-cursor */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-cursor.js\");\n/* harmony import */ var _components_default_row__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/default-row */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-row.js\");\n/* harmony import */ var _components_default_node__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/default-node */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-node.js\");\n/* harmony import */ var _state_edit_slice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../state/edit-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/edit-slice.js\");\n/* harmony import */ var _state_focus_slice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../state/focus-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/focus-slice.js\");\n/* harmony import */ var _data_create_root__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../data/create-root */ \"(ssr)/./node_modules/react-arborist/dist/module/data/create-root.js\");\n/* harmony import */ var _state_open_slice__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../state/open-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/open-slice.js\");\n/* harmony import */ var _state_selection_slice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../state/selection-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/selection-slice.js\");\n/* harmony import */ var _state_dnd_slice__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../state/dnd-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js\");\n/* harmony import */ var _components_default_drag_preview__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/default-drag-preview */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-drag-preview.js\");\n/* harmony import */ var _components_default_container__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/default-container */ \"(ssr)/./node_modules/react-arborist/dist/module/components/default-container.js\");\n/* harmony import */ var _data_create_list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../data/create-list */ \"(ssr)/./node_modules/react-arborist/dist/module/data/create-list.js\");\n/* harmony import */ var _data_create_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../data/create-index */ \"(ssr)/./node_modules/react-arborist/dist/module/data/create-index.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { safeRun, identify, identifyNull } = _utils__WEBPACK_IMPORTED_MODULE_0__;\nclass TreeApi {\n    constructor(store, props, list, listEl){\n        this.store = store;\n        this.props = props;\n        this.list = list;\n        this.listEl = listEl;\n        this.visibleStartIndex = 0;\n        this.visibleStopIndex = 0;\n        /* Changes here must also be made in update() */ this.root = (0,_data_create_root__WEBPACK_IMPORTED_MODULE_1__.createRoot)(this);\n        this.visibleNodes = (0,_data_create_list__WEBPACK_IMPORTED_MODULE_2__.createList)(this);\n        this.idToIndex = (0,_data_create_index__WEBPACK_IMPORTED_MODULE_3__.createIndex)(this.visibleNodes);\n    }\n    /* Changes here must also be made in constructor() */ update(props) {\n        this.props = props;\n        this.root = (0,_data_create_root__WEBPACK_IMPORTED_MODULE_1__.createRoot)(this);\n        this.visibleNodes = (0,_data_create_list__WEBPACK_IMPORTED_MODULE_2__.createList)(this);\n        this.idToIndex = (0,_data_create_index__WEBPACK_IMPORTED_MODULE_3__.createIndex)(this.visibleNodes);\n    }\n    /* Store helpers */ dispatch(action) {\n        return this.store.dispatch(action);\n    }\n    get state() {\n        return this.store.getState();\n    }\n    get openState() {\n        return this.state.nodes.open.unfiltered;\n    }\n    /* Tree Props */ get width() {\n        var _a;\n        return (_a = this.props.width) !== null && _a !== void 0 ? _a : 300;\n    }\n    get height() {\n        var _a;\n        return (_a = this.props.height) !== null && _a !== void 0 ? _a : 500;\n    }\n    get indent() {\n        var _a;\n        return (_a = this.props.indent) !== null && _a !== void 0 ? _a : 24;\n    }\n    get rowHeight() {\n        var _a;\n        return (_a = this.props.rowHeight) !== null && _a !== void 0 ? _a : 24;\n    }\n    get overscanCount() {\n        var _a;\n        return (_a = this.props.overscanCount) !== null && _a !== void 0 ? _a : 1;\n    }\n    get searchTerm() {\n        return (this.props.searchTerm || \"\").trim();\n    }\n    get matchFn() {\n        var _a;\n        const match = (_a = this.props.searchMatch) !== null && _a !== void 0 ? _a : (node, term)=>{\n            const string = JSON.stringify(Object.values(node.data));\n            return string.toLocaleLowerCase().includes(term.toLocaleLowerCase());\n        };\n        return (node)=>match(node, this.searchTerm);\n    }\n    accessChildren(data) {\n        var _a;\n        const get = this.props.childrenAccessor || \"children\";\n        return (_a = _utils__WEBPACK_IMPORTED_MODULE_0__.access(data, get)) !== null && _a !== void 0 ? _a : null;\n    }\n    accessId(data) {\n        const get = this.props.idAccessor || \"id\";\n        const id = _utils__WEBPACK_IMPORTED_MODULE_0__.access(data, get);\n        if (!id) throw new Error(\"Data must contain an 'id' property or props.idAccessor must return a string\");\n        return id;\n    }\n    /* Node Access */ get firstNode() {\n        var _a;\n        return (_a = this.visibleNodes[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    get lastNode() {\n        var _a;\n        return (_a = this.visibleNodes[this.visibleNodes.length - 1]) !== null && _a !== void 0 ? _a : null;\n    }\n    get focusedNode() {\n        var _a;\n        return (_a = this.get(this.state.nodes.focus.id)) !== null && _a !== void 0 ? _a : null;\n    }\n    get mostRecentNode() {\n        var _a;\n        return (_a = this.get(this.state.nodes.selection.mostRecent)) !== null && _a !== void 0 ? _a : null;\n    }\n    get nextNode() {\n        const index = this.indexOf(this.focusedNode);\n        if (index === null) return null;\n        else return this.at(index + 1);\n    }\n    get prevNode() {\n        const index = this.indexOf(this.focusedNode);\n        if (index === null) return null;\n        else return this.at(index - 1);\n    }\n    get(id) {\n        if (!id) return null;\n        if (id in this.idToIndex) return this.visibleNodes[this.idToIndex[id]] || null;\n        else return null;\n    }\n    at(index) {\n        return this.visibleNodes[index] || null;\n    }\n    nodesBetween(startId, endId) {\n        var _a;\n        if (startId === null || endId === null) return [];\n        const index1 = (_a = this.indexOf(startId)) !== null && _a !== void 0 ? _a : 0;\n        const index2 = this.indexOf(endId);\n        if (index2 === null) return [];\n        const start = Math.min(index1, index2);\n        const end = Math.max(index1, index2);\n        return this.visibleNodes.slice(start, end + 1);\n    }\n    indexOf(id) {\n        const key = _utils__WEBPACK_IMPORTED_MODULE_0__.identifyNull(id);\n        if (!key) return null;\n        return this.idToIndex[key];\n    }\n    /* Data Operations */ get editingId() {\n        return this.state.nodes.edit.id;\n    }\n    createInternal() {\n        return this.create({\n            type: \"internal\"\n        });\n    }\n    createLeaf() {\n        return this.create({\n            type: \"leaf\"\n        });\n    }\n    create() {\n        return __awaiter(this, arguments, void 0, function*(opts = {}) {\n            var _a, _b;\n            const parentId = opts.parentId === undefined ? _utils__WEBPACK_IMPORTED_MODULE_0__.getInsertParentId(this) : opts.parentId;\n            const index = (_a = opts.index) !== null && _a !== void 0 ? _a : _utils__WEBPACK_IMPORTED_MODULE_0__.getInsertIndex(this);\n            const type = (_b = opts.type) !== null && _b !== void 0 ? _b : \"leaf\";\n            const data = yield safeRun(this.props.onCreate, {\n                type,\n                parentId,\n                index,\n                parentNode: this.get(parentId)\n            });\n            if (data) {\n                this.focus(data);\n                setTimeout(()=>{\n                    this.edit(data).then(()=>{\n                        this.select(data);\n                        this.activate(data);\n                    });\n                });\n            }\n        });\n    }\n    delete(node) {\n        return __awaiter(this, void 0, void 0, function*() {\n            if (!node) return;\n            const idents = Array.isArray(node) ? node : [\n                node\n            ];\n            const ids = idents.map(identify);\n            const nodes = ids.map((id)=>this.get(id)).filter((n)=>!!n);\n            yield safeRun(this.props.onDelete, {\n                nodes,\n                ids\n            });\n        });\n    }\n    edit(node) {\n        const id = identify(node);\n        this.resolveEdit({\n            cancelled: true\n        });\n        this.scrollTo(id);\n        this.dispatch((0,_state_edit_slice__WEBPACK_IMPORTED_MODULE_4__.edit)(id));\n        return new Promise((resolve)=>{\n            TreeApi.editPromise = resolve;\n        });\n    }\n    submit(identity, value) {\n        return __awaiter(this, void 0, void 0, function*() {\n            if (!identity) return;\n            const id = identify(identity);\n            yield safeRun(this.props.onRename, {\n                id,\n                name: value,\n                node: this.get(id)\n            });\n            this.dispatch((0,_state_edit_slice__WEBPACK_IMPORTED_MODULE_4__.edit)(null));\n            this.resolveEdit({\n                cancelled: false,\n                value\n            });\n            setTimeout(()=>this.onFocus()); // Return focus to element;\n        });\n    }\n    reset() {\n        this.dispatch((0,_state_edit_slice__WEBPACK_IMPORTED_MODULE_4__.edit)(null));\n        this.resolveEdit({\n            cancelled: true\n        });\n        setTimeout(()=>this.onFocus()); // Return focus to element;\n    }\n    activate(id) {\n        const node = this.get(identifyNull(id));\n        if (!node) return;\n        safeRun(this.props.onActivate, node);\n    }\n    resolveEdit(value) {\n        const resolve = TreeApi.editPromise;\n        if (resolve) resolve(value);\n        TreeApi.editPromise = null;\n    }\n    /* Focus and Selection */ get selectedIds() {\n        return this.state.nodes.selection.ids;\n    }\n    get selectedNodes() {\n        let nodes = [];\n        for (let id of Array.from(this.selectedIds)){\n            const node = this.get(id);\n            if (node) nodes.push(node);\n        }\n        return nodes;\n    }\n    focus(node, opts = {}) {\n        if (!node) return;\n        /* Focus is responsible for scrolling, while selection is\n         * responsible for focus. If selectionFollowsFocus, then\n         * just select it. */ if (this.props.selectionFollowsFocus) {\n            this.select(node);\n        } else {\n            this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.focus)(identify(node)));\n            if (opts.scroll !== false) this.scrollTo(node);\n            if (this.focusedNode) safeRun(this.props.onFocus, this.focusedNode);\n        }\n    }\n    pageUp() {\n        var _a, _b;\n        const start = this.visibleStartIndex;\n        const stop = this.visibleStopIndex;\n        const page = stop - start;\n        let index = (_b = (_a = this.focusedNode) === null || _a === void 0 ? void 0 : _a.rowIndex) !== null && _b !== void 0 ? _b : 0;\n        if (index > start) {\n            index = start;\n        } else {\n            index = Math.max(start - page, 0);\n        }\n        this.focus(this.at(index));\n    }\n    pageDown() {\n        var _a, _b;\n        const start = this.visibleStartIndex;\n        const stop = this.visibleStopIndex;\n        const page = stop - start;\n        let index = (_b = (_a = this.focusedNode) === null || _a === void 0 ? void 0 : _a.rowIndex) !== null && _b !== void 0 ? _b : 0;\n        if (index < stop) {\n            index = stop;\n        } else {\n            index = Math.min(index + page, this.visibleNodes.length - 1);\n        }\n        this.focus(this.at(index));\n    }\n    select(node, opts = {}) {\n        if (!node) return;\n        const changeFocus = opts.focus !== false;\n        const id = identify(node);\n        if (changeFocus) this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.focus)(id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.only(id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.anchor(id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.mostRecent(id));\n        this.scrollTo(id, opts.align);\n        if (this.focusedNode && changeFocus) {\n            safeRun(this.props.onFocus, this.focusedNode);\n        }\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    deselect(node) {\n        if (!node) return;\n        const id = identify(node);\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.remove(id));\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    selectMulti(identity) {\n        const node = this.get(identifyNull(identity));\n        if (!node) return;\n        this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.focus)(node.id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.add(node.id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.anchor(node.id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.mostRecent(node.id));\n        this.scrollTo(node);\n        if (this.focusedNode) safeRun(this.props.onFocus, this.focusedNode);\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    selectContiguous(identity) {\n        if (!identity) return;\n        const id = identify(identity);\n        const { anchor, mostRecent } = this.state.nodes.selection;\n        this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.focus)(id));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.remove(this.nodesBetween(anchor, mostRecent)));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.add(this.nodesBetween(anchor, identifyNull(id))));\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.mostRecent(id));\n        this.scrollTo(id);\n        if (this.focusedNode) safeRun(this.props.onFocus, this.focusedNode);\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    deselectAll() {\n        this.setSelection({\n            ids: [],\n            anchor: null,\n            mostRecent: null\n        });\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    selectAll() {\n        var _a;\n        this.setSelection({\n            ids: Object.keys(this.idToIndex),\n            anchor: this.firstNode,\n            mostRecent: this.lastNode\n        });\n        this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.focus)((_a = this.lastNode) === null || _a === void 0 ? void 0 : _a.id));\n        if (this.focusedNode) safeRun(this.props.onFocus, this.focusedNode);\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    setSelection(args) {\n        var _a;\n        const ids = new Set((_a = args.ids) === null || _a === void 0 ? void 0 : _a.map(identify));\n        const anchor = identifyNull(args.anchor);\n        const mostRecent = identifyNull(args.mostRecent);\n        this.dispatch(_state_selection_slice__WEBPACK_IMPORTED_MODULE_6__.actions.set({\n            ids,\n            anchor,\n            mostRecent\n        }));\n        safeRun(this.props.onSelect, this.selectedNodes);\n    }\n    /* Drag and Drop */ get cursorParentId() {\n        const { cursor } = this.state.dnd;\n        switch(cursor.type){\n            case \"highlight\":\n                return cursor.id;\n            default:\n                return null;\n        }\n    }\n    get cursorOverFolder() {\n        return this.state.dnd.cursor.type === \"highlight\";\n    }\n    get dragNodes() {\n        return this.state.dnd.dragIds.map((id)=>this.get(id)).filter((n)=>!!n);\n    }\n    get dragNode() {\n        return this.get(this.state.nodes.drag.id);\n    }\n    get dragDestinationParent() {\n        return this.get(this.state.nodes.drag.destinationParentId);\n    }\n    get dragDestinationIndex() {\n        return this.state.nodes.drag.destinationIndex;\n    }\n    canDrop() {\n        var _a;\n        if (this.isFiltered) return false;\n        const parentNode = (_a = this.get(this.state.dnd.parentId)) !== null && _a !== void 0 ? _a : this.root;\n        const dragNodes = this.dragNodes;\n        const isDisabled = this.props.disableDrop;\n        for (const drag of dragNodes){\n            if (!drag) return false;\n            if (!parentNode) return false;\n            if (drag.isInternal && _utils__WEBPACK_IMPORTED_MODULE_0__.isDescendant(parentNode, drag)) return false;\n        }\n        // Allow the user to insert their own logic\n        if (typeof isDisabled == \"function\") {\n            return !isDisabled({\n                parentNode,\n                dragNodes: this.dragNodes,\n                index: this.state.dnd.index || 0\n            });\n        } else if (typeof isDisabled == \"string\") {\n            // @ts-ignore\n            return !parentNode.data[isDisabled];\n        } else if (typeof isDisabled === \"boolean\") {\n            return !isDisabled;\n        } else {\n            return true;\n        }\n    }\n    hideCursor() {\n        this.dispatch(_state_dnd_slice__WEBPACK_IMPORTED_MODULE_7__.actions.cursor({\n            type: \"none\"\n        }));\n    }\n    showCursor(cursor) {\n        this.dispatch(_state_dnd_slice__WEBPACK_IMPORTED_MODULE_7__.actions.cursor(cursor));\n    }\n    /* Visibility */ open(identity) {\n        const id = identifyNull(identity);\n        if (!id) return;\n        if (this.isOpen(id)) return;\n        this.dispatch(_state_open_slice__WEBPACK_IMPORTED_MODULE_8__.actions.open(id, this.isFiltered));\n        safeRun(this.props.onToggle, id);\n    }\n    close(identity) {\n        const id = identifyNull(identity);\n        if (!id) return;\n        if (!this.isOpen(id)) return;\n        this.dispatch(_state_open_slice__WEBPACK_IMPORTED_MODULE_8__.actions.close(id, this.isFiltered));\n        safeRun(this.props.onToggle, id);\n    }\n    toggle(identity) {\n        const id = identifyNull(identity);\n        if (!id) return;\n        return this.isOpen(id) ? this.close(id) : this.open(id);\n    }\n    openParents(identity) {\n        const id = identifyNull(identity);\n        if (!id) return;\n        const node = _utils__WEBPACK_IMPORTED_MODULE_0__.dfs(this.root, id);\n        let parent = node === null || node === void 0 ? void 0 : node.parent;\n        while(parent){\n            this.open(parent.id);\n            parent = parent.parent;\n        }\n    }\n    openSiblings(node) {\n        const parent = node.parent;\n        if (!parent) {\n            this.toggle(node.id);\n        } else if (parent.children) {\n            const isOpen = node.isOpen;\n            for (let sibling of parent.children){\n                if (sibling.isInternal) {\n                    isOpen ? this.close(sibling.id) : this.open(sibling.id);\n                }\n            }\n            this.scrollTo(this.focusedNode);\n        }\n    }\n    openAll() {\n        _utils__WEBPACK_IMPORTED_MODULE_0__.walk(this.root, (node)=>{\n            if (node.isInternal) node.open();\n        });\n    }\n    closeAll() {\n        _utils__WEBPACK_IMPORTED_MODULE_0__.walk(this.root, (node)=>{\n            if (node.isInternal) node.close();\n        });\n    }\n    /* Scrolling */ scrollTo(identity, align = \"smart\") {\n        if (!identity) return;\n        const id = identify(identity);\n        this.openParents(id);\n        return _utils__WEBPACK_IMPORTED_MODULE_0__.waitFor(()=>id in this.idToIndex).then(()=>{\n            var _a;\n            const index = this.idToIndex[id];\n            if (index === undefined) return;\n            (_a = this.list.current) === null || _a === void 0 ? void 0 : _a.scrollToItem(index, align);\n        }).catch(()=>{\n        // Id: ${id} never appeared in the list.\n        });\n    }\n    /* State Checks */ get isEditing() {\n        return this.state.nodes.edit.id !== null;\n    }\n    get isFiltered() {\n        var _a;\n        return !!((_a = this.props.searchTerm) === null || _a === void 0 ? void 0 : _a.trim());\n    }\n    get hasFocus() {\n        return this.state.nodes.focus.treeFocused;\n    }\n    get hasNoSelection() {\n        return this.state.nodes.selection.ids.size === 0;\n    }\n    get hasOneSelection() {\n        return this.state.nodes.selection.ids.size === 1;\n    }\n    get hasMultipleSelections() {\n        return this.state.nodes.selection.ids.size > 1;\n    }\n    isSelected(id) {\n        if (!id) return false;\n        return this.state.nodes.selection.ids.has(id);\n    }\n    isOpen(id) {\n        var _a, _b, _c;\n        if (!id) return false;\n        if (id === _data_create_root__WEBPACK_IMPORTED_MODULE_1__.ROOT_ID) return true;\n        const def = (_a = this.props.openByDefault) !== null && _a !== void 0 ? _a : true;\n        if (this.isFiltered) {\n            return (_b = this.state.nodes.open.filtered[id]) !== null && _b !== void 0 ? _b : true; // Filtered folders are always opened by default\n        } else {\n            return (_c = this.state.nodes.open.unfiltered[id]) !== null && _c !== void 0 ? _c : def;\n        }\n    }\n    isEditable(data) {\n        const check = this.props.disableEdit || (()=>false);\n        return !_utils__WEBPACK_IMPORTED_MODULE_0__.access(data, check);\n    }\n    isDraggable(data) {\n        const check = this.props.disableDrag || (()=>false);\n        return !_utils__WEBPACK_IMPORTED_MODULE_0__.access(data, check);\n    }\n    isDragging(node) {\n        const id = identifyNull(node);\n        if (!id) return false;\n        return this.state.nodes.drag.id === id;\n    }\n    isFocused(id) {\n        return this.hasFocus && this.state.nodes.focus.id === id;\n    }\n    isMatch(node) {\n        return this.matchFn(node);\n    }\n    willReceiveDrop(node) {\n        const id = identifyNull(node);\n        if (!id) return false;\n        const { destinationParentId, destinationIndex } = this.state.nodes.drag;\n        return id === destinationParentId && destinationIndex === null;\n    }\n    /* Tree Event Handlers */ onFocus() {\n        const node = this.focusedNode || this.firstNode;\n        if (node) this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.focus)(node.id));\n    }\n    onBlur() {\n        this.dispatch((0,_state_focus_slice__WEBPACK_IMPORTED_MODULE_5__.treeBlur)());\n    }\n    onItemsRendered(args) {\n        this.visibleStartIndex = args.visibleStartIndex;\n        this.visibleStopIndex = args.visibleStopIndex;\n    }\n    /* Get Renderers */ get renderContainer() {\n        return this.props.renderContainer || _components_default_container__WEBPACK_IMPORTED_MODULE_9__.DefaultContainer;\n    }\n    get renderRow() {\n        return this.props.renderRow || _components_default_row__WEBPACK_IMPORTED_MODULE_10__.DefaultRow;\n    }\n    get renderNode() {\n        return this.props.children || _components_default_node__WEBPACK_IMPORTED_MODULE_11__.DefaultNode;\n    }\n    get renderDragPreview() {\n        return this.props.renderDragPreview || _components_default_drag_preview__WEBPACK_IMPORTED_MODULE_12__.DefaultDragPreview;\n    }\n    get renderCursor() {\n        return this.props.renderCursor || _components_default_cursor__WEBPACK_IMPORTED_MODULE_13__.DefaultCursor;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvaW50ZXJmYWNlcy90cmVlLWFwaS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxJQUFJQSxZQUFZLFNBQUssSUFBSSxTQUFJLENBQUNBLFNBQVMsSUFBSyxTQUFVQyxPQUFPLEVBQUVDLFVBQVUsRUFBRUMsQ0FBQyxFQUFFQyxTQUFTO0lBQ25GLFNBQVNDLE1BQU1DLEtBQUs7UUFBSSxPQUFPQSxpQkFBaUJILElBQUlHLFFBQVEsSUFBSUgsRUFBRSxTQUFVSSxPQUFPO1lBQUlBLFFBQVFEO1FBQVE7SUFBSTtJQUMzRyxPQUFPLElBQUtILENBQUFBLEtBQU1BLENBQUFBLElBQUlLLE9BQU0sQ0FBQyxFQUFHLFNBQVVELE9BQU8sRUFBRUUsTUFBTTtRQUNyRCxTQUFTQyxVQUFVSixLQUFLO1lBQUksSUFBSTtnQkFBRUssS0FBS1AsVUFBVVEsSUFBSSxDQUFDTjtZQUFTLEVBQUUsT0FBT08sR0FBRztnQkFBRUosT0FBT0k7WUFBSTtRQUFFO1FBQzFGLFNBQVNDLFNBQVNSLEtBQUs7WUFBSSxJQUFJO2dCQUFFSyxLQUFLUCxTQUFTLENBQUMsUUFBUSxDQUFDRTtZQUFTLEVBQUUsT0FBT08sR0FBRztnQkFBRUosT0FBT0k7WUFBSTtRQUFFO1FBQzdGLFNBQVNGLEtBQUtJLE1BQU07WUFBSUEsT0FBT0MsSUFBSSxHQUFHVCxRQUFRUSxPQUFPVCxLQUFLLElBQUlELE1BQU1VLE9BQU9ULEtBQUssRUFBRVcsSUFBSSxDQUFDUCxXQUFXSTtRQUFXO1FBQzdHSCxLQUFLLENBQUNQLFlBQVlBLFVBQVVjLEtBQUssQ0FBQ2pCLFNBQVNDLGNBQWMsRUFBRSxHQUFHVSxJQUFJO0lBQ3RFO0FBQ0o7QUFDa0M7QUFDMkI7QUFDTjtBQUNFO0FBQ2Q7QUFDWTtBQUNHO0FBQ0U7QUFDSTtBQUNaO0FBQ29CO0FBQ0w7QUFDbEI7QUFDRTtBQUNuRCxNQUFNLEVBQUV3QixPQUFPLEVBQUVDLFFBQVEsRUFBRUMsWUFBWSxFQUFFLEdBQUduQixtQ0FBS0E7QUFDMUMsTUFBTW9CO0lBQ1RDLFlBQVlDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sQ0FBRTtRQUNwQyxJQUFJLENBQUNILEtBQUssR0FBR0E7UUFDYixJQUFJLENBQUNDLEtBQUssR0FBR0E7UUFDYixJQUFJLENBQUNDLElBQUksR0FBR0E7UUFDWixJQUFJLENBQUNDLE1BQU0sR0FBR0E7UUFDZCxJQUFJLENBQUNDLGlCQUFpQixHQUFHO1FBQ3pCLElBQUksQ0FBQ0MsZ0JBQWdCLEdBQUc7UUFDeEIsOENBQThDLEdBQzlDLElBQUksQ0FBQ0MsSUFBSSxHQUFHckIsNkRBQVVBLENBQUMsSUFBSTtRQUMzQixJQUFJLENBQUNzQixZQUFZLEdBQUdkLDZEQUFVQSxDQUFDLElBQUk7UUFDbkMsSUFBSSxDQUFDZSxTQUFTLEdBQUdkLCtEQUFXQSxDQUFDLElBQUksQ0FBQ2EsWUFBWTtJQUNsRDtJQUNBLG1EQUFtRCxHQUNuREUsT0FBT1IsS0FBSyxFQUFFO1FBQ1YsSUFBSSxDQUFDQSxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDSyxJQUFJLEdBQUdyQiw2REFBVUEsQ0FBQyxJQUFJO1FBQzNCLElBQUksQ0FBQ3NCLFlBQVksR0FBR2QsNkRBQVVBLENBQUMsSUFBSTtRQUNuQyxJQUFJLENBQUNlLFNBQVMsR0FBR2QsK0RBQVdBLENBQUMsSUFBSSxDQUFDYSxZQUFZO0lBQ2xEO0lBQ0EsaUJBQWlCLEdBQ2pCRyxTQUFTQyxNQUFNLEVBQUU7UUFDYixPQUFPLElBQUksQ0FBQ1gsS0FBSyxDQUFDVSxRQUFRLENBQUNDO0lBQy9CO0lBQ0EsSUFBSUMsUUFBUTtRQUNSLE9BQU8sSUFBSSxDQUFDWixLQUFLLENBQUNhLFFBQVE7SUFDOUI7SUFDQSxJQUFJQyxZQUFZO1FBQ1osT0FBTyxJQUFJLENBQUNGLEtBQUssQ0FBQ0csS0FBSyxDQUFDQyxJQUFJLENBQUNDLFVBQVU7SUFDM0M7SUFDQSxjQUFjLEdBQ2QsSUFBSUMsUUFBUTtRQUNSLElBQUlDO1FBQ0osT0FBTyxDQUFDQSxLQUFLLElBQUksQ0FBQ2xCLEtBQUssQ0FBQ2lCLEtBQUssTUFBTSxRQUFRQyxPQUFPLEtBQUssSUFBSUEsS0FBSztJQUNwRTtJQUNBLElBQUlDLFNBQVM7UUFDVCxJQUFJRDtRQUNKLE9BQU8sQ0FBQ0EsS0FBSyxJQUFJLENBQUNsQixLQUFLLENBQUNtQixNQUFNLE1BQU0sUUFBUUQsT0FBTyxLQUFLLElBQUlBLEtBQUs7SUFDckU7SUFDQSxJQUFJRSxTQUFTO1FBQ1QsSUFBSUY7UUFDSixPQUFPLENBQUNBLEtBQUssSUFBSSxDQUFDbEIsS0FBSyxDQUFDb0IsTUFBTSxNQUFNLFFBQVFGLE9BQU8sS0FBSyxJQUFJQSxLQUFLO0lBQ3JFO0lBQ0EsSUFBSUcsWUFBWTtRQUNaLElBQUlIO1FBQ0osT0FBTyxDQUFDQSxLQUFLLElBQUksQ0FBQ2xCLEtBQUssQ0FBQ3FCLFNBQVMsTUFBTSxRQUFRSCxPQUFPLEtBQUssSUFBSUEsS0FBSztJQUN4RTtJQUNBLElBQUlJLGdCQUFnQjtRQUNoQixJQUFJSjtRQUNKLE9BQU8sQ0FBQ0EsS0FBSyxJQUFJLENBQUNsQixLQUFLLENBQUNzQixhQUFhLE1BQU0sUUFBUUosT0FBTyxLQUFLLElBQUlBLEtBQUs7SUFDNUU7SUFDQSxJQUFJSyxhQUFhO1FBQ2IsT0FBTyxDQUFDLElBQUksQ0FBQ3ZCLEtBQUssQ0FBQ3VCLFVBQVUsSUFBSSxFQUFDLEVBQUdDLElBQUk7SUFDN0M7SUFDQSxJQUFJQyxVQUFVO1FBQ1YsSUFBSVA7UUFDSixNQUFNUSxRQUFRLENBQUNSLEtBQUssSUFBSSxDQUFDbEIsS0FBSyxDQUFDMkIsV0FBVyxNQUFNLFFBQVFULE9BQU8sS0FBSyxJQUFJQSxLQUFNLENBQUNVLE1BQU1DO1lBQ2pGLE1BQU1DLFNBQVNDLEtBQUtDLFNBQVMsQ0FBQ0MsT0FBT0MsTUFBTSxDQUFDTixLQUFLTyxJQUFJO1lBQ3JELE9BQU9MLE9BQU9NLGlCQUFpQixHQUFHQyxRQUFRLENBQUNSLEtBQUtPLGlCQUFpQjtRQUNyRTtRQUNBLE9BQU8sQ0FBQ1IsT0FBU0YsTUFBTUUsTUFBTSxJQUFJLENBQUNMLFVBQVU7SUFDaEQ7SUFDQWUsZUFBZUgsSUFBSSxFQUFFO1FBQ2pCLElBQUlqQjtRQUNKLE1BQU1xQixNQUFNLElBQUksQ0FBQ3ZDLEtBQUssQ0FBQ3dDLGdCQUFnQixJQUFJO1FBQzNDLE9BQU8sQ0FBQ3RCLEtBQUt6QywwQ0FBWSxDQUFDMEQsTUFBTUksSUFBRyxNQUFPLFFBQVFyQixPQUFPLEtBQUssSUFBSUEsS0FBSztJQUMzRTtJQUNBd0IsU0FBU1AsSUFBSSxFQUFFO1FBQ1gsTUFBTUksTUFBTSxJQUFJLENBQUN2QyxLQUFLLENBQUMyQyxVQUFVLElBQUk7UUFDckMsTUFBTUMsS0FBS25FLDBDQUFZLENBQUMwRCxNQUFNSTtRQUM5QixJQUFJLENBQUNLLElBQ0QsTUFBTSxJQUFJQyxNQUFNO1FBQ3BCLE9BQU9EO0lBQ1g7SUFDQSxlQUFlLEdBQ2YsSUFBSUUsWUFBWTtRQUNaLElBQUk1QjtRQUNKLE9BQU8sQ0FBQ0EsS0FBSyxJQUFJLENBQUNaLFlBQVksQ0FBQyxFQUFFLE1BQU0sUUFBUVksT0FBTyxLQUFLLElBQUlBLEtBQUs7SUFDeEU7SUFDQSxJQUFJNkIsV0FBVztRQUNYLElBQUk3QjtRQUNKLE9BQU8sQ0FBQ0EsS0FBSyxJQUFJLENBQUNaLFlBQVksQ0FBQyxJQUFJLENBQUNBLFlBQVksQ0FBQzBDLE1BQU0sR0FBRyxFQUFFLE1BQU0sUUFBUTlCLE9BQU8sS0FBSyxJQUFJQSxLQUFLO0lBQ25HO0lBQ0EsSUFBSStCLGNBQWM7UUFDZCxJQUFJL0I7UUFDSixPQUFPLENBQUNBLEtBQUssSUFBSSxDQUFDcUIsR0FBRyxDQUFDLElBQUksQ0FBQzVCLEtBQUssQ0FBQ0csS0FBSyxDQUFDaEMsS0FBSyxDQUFDOEQsRUFBRSxPQUFPLFFBQVExQixPQUFPLEtBQUssSUFBSUEsS0FBSztJQUN2RjtJQUNBLElBQUlnQyxpQkFBaUI7UUFDakIsSUFBSWhDO1FBQ0osT0FBTyxDQUFDQSxLQUFLLElBQUksQ0FBQ3FCLEdBQUcsQ0FBQyxJQUFJLENBQUM1QixLQUFLLENBQUNHLEtBQUssQ0FBQzFCLFNBQVMsQ0FBQytELFVBQVUsT0FBTyxRQUFRakMsT0FBTyxLQUFLLElBQUlBLEtBQUs7SUFDbkc7SUFDQSxJQUFJa0MsV0FBVztRQUNYLE1BQU1DLFFBQVEsSUFBSSxDQUFDQyxPQUFPLENBQUMsSUFBSSxDQUFDTCxXQUFXO1FBQzNDLElBQUlJLFVBQVUsTUFDVixPQUFPO2FBRVAsT0FBTyxJQUFJLENBQUNFLEVBQUUsQ0FBQ0YsUUFBUTtJQUMvQjtJQUNBLElBQUlHLFdBQVc7UUFDWCxNQUFNSCxRQUFRLElBQUksQ0FBQ0MsT0FBTyxDQUFDLElBQUksQ0FBQ0wsV0FBVztRQUMzQyxJQUFJSSxVQUFVLE1BQ1YsT0FBTzthQUVQLE9BQU8sSUFBSSxDQUFDRSxFQUFFLENBQUNGLFFBQVE7SUFDL0I7SUFDQWQsSUFBSUssRUFBRSxFQUFFO1FBQ0osSUFBSSxDQUFDQSxJQUNELE9BQU87UUFDWCxJQUFJQSxNQUFNLElBQUksQ0FBQ3JDLFNBQVMsRUFDcEIsT0FBTyxJQUFJLENBQUNELFlBQVksQ0FBQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ3FDLEdBQUcsQ0FBQyxJQUFJO2FBRWhELE9BQU87SUFDZjtJQUNBVyxHQUFHRixLQUFLLEVBQUU7UUFDTixPQUFPLElBQUksQ0FBQy9DLFlBQVksQ0FBQytDLE1BQU0sSUFBSTtJQUN2QztJQUNBSSxhQUFhQyxPQUFPLEVBQUVDLEtBQUssRUFBRTtRQUN6QixJQUFJekM7UUFDSixJQUFJd0MsWUFBWSxRQUFRQyxVQUFVLE1BQzlCLE9BQU8sRUFBRTtRQUNiLE1BQU1DLFNBQVMsQ0FBQzFDLEtBQUssSUFBSSxDQUFDb0MsT0FBTyxDQUFDSSxRQUFPLE1BQU8sUUFBUXhDLE9BQU8sS0FBSyxJQUFJQSxLQUFLO1FBQzdFLE1BQU0yQyxTQUFTLElBQUksQ0FBQ1AsT0FBTyxDQUFDSztRQUM1QixJQUFJRSxXQUFXLE1BQ1gsT0FBTyxFQUFFO1FBQ2IsTUFBTUMsUUFBUUMsS0FBS0MsR0FBRyxDQUFDSixRQUFRQztRQUMvQixNQUFNSSxNQUFNRixLQUFLRyxHQUFHLENBQUNOLFFBQVFDO1FBQzdCLE9BQU8sSUFBSSxDQUFDdkQsWUFBWSxDQUFDNkQsS0FBSyxDQUFDTCxPQUFPRyxNQUFNO0lBQ2hEO0lBQ0FYLFFBQVFWLEVBQUUsRUFBRTtRQUNSLE1BQU13QixNQUFNM0YsZ0RBQWtCLENBQUNtRTtRQUMvQixJQUFJLENBQUN3QixLQUNELE9BQU87UUFDWCxPQUFPLElBQUksQ0FBQzdELFNBQVMsQ0FBQzZELElBQUk7SUFDOUI7SUFDQSxtQkFBbUIsR0FDbkIsSUFBSUMsWUFBWTtRQUNaLE9BQU8sSUFBSSxDQUFDMUQsS0FBSyxDQUFDRyxLQUFLLENBQUNqQyxJQUFJLENBQUMrRCxFQUFFO0lBQ25DO0lBQ0EwQixpQkFBaUI7UUFDYixPQUFPLElBQUksQ0FBQ0MsTUFBTSxDQUFDO1lBQUVDLE1BQU07UUFBVztJQUMxQztJQUNBQyxhQUFhO1FBQ1QsT0FBTyxJQUFJLENBQUNGLE1BQU0sQ0FBQztZQUFFQyxNQUFNO1FBQU87SUFDdEM7SUFDQUQsU0FBUztRQUNMLE9BQU9qSCxVQUFVLElBQUksRUFBRW9ILFdBQVcsS0FBSyxHQUFHLFVBQVdDLE9BQU8sQ0FBQyxDQUFDO1lBQzFELElBQUl6RCxJQUFJMEQ7WUFDUixNQUFNQyxXQUFXRixLQUFLRSxRQUFRLEtBQUtDLFlBQzdCckcscURBQXVCLENBQUMsSUFBSSxJQUM1QmtHLEtBQUtFLFFBQVE7WUFDbkIsTUFBTXhCLFFBQVEsQ0FBQ25DLEtBQUt5RCxLQUFLdEIsS0FBSyxNQUFNLFFBQVFuQyxPQUFPLEtBQUssSUFBSUEsS0FBS3pDLGtEQUFvQixDQUFDLElBQUk7WUFDMUYsTUFBTStGLE9BQU8sQ0FBQ0ksS0FBS0QsS0FBS0gsSUFBSSxNQUFNLFFBQVFJLE9BQU8sS0FBSyxJQUFJQSxLQUFLO1lBQy9ELE1BQU16QyxPQUFPLE1BQU16QyxRQUFRLElBQUksQ0FBQ00sS0FBSyxDQUFDaUYsUUFBUSxFQUFFO2dCQUM1Q1Q7Z0JBQ0FLO2dCQUNBeEI7Z0JBQ0E2QixZQUFZLElBQUksQ0FBQzNDLEdBQUcsQ0FBQ3NDO1lBQ3pCO1lBQ0EsSUFBSTFDLE1BQU07Z0JBQ04sSUFBSSxDQUFDckQsS0FBSyxDQUFDcUQ7Z0JBQ1hnRCxXQUFXO29CQUNQLElBQUksQ0FBQ3RHLElBQUksQ0FBQ3NELE1BQU01RCxJQUFJLENBQUM7d0JBQ2pCLElBQUksQ0FBQzZHLE1BQU0sQ0FBQ2pEO3dCQUNaLElBQUksQ0FBQ2tELFFBQVEsQ0FBQ2xEO29CQUNsQjtnQkFDSjtZQUNKO1FBQ0o7SUFDSjtJQUNBbUQsT0FBTzFELElBQUksRUFBRTtRQUNULE9BQU90RSxVQUFVLElBQUksRUFBRSxLQUFLLEdBQUcsS0FBSyxHQUFHO1lBQ25DLElBQUksQ0FBQ3NFLE1BQ0Q7WUFDSixNQUFNMkQsU0FBU0MsTUFBTUMsT0FBTyxDQUFDN0QsUUFBUUEsT0FBTztnQkFBQ0E7YUFBSztZQUNsRCxNQUFNOEQsTUFBTUgsT0FBT0ksR0FBRyxDQUFDaEc7WUFDdkIsTUFBTW1CLFFBQVE0RSxJQUFJQyxHQUFHLENBQUMsQ0FBQy9DLEtBQU8sSUFBSSxDQUFDTCxHQUFHLENBQUNLLEtBQUtnRCxNQUFNLENBQUMsQ0FBQ0MsSUFBTSxDQUFDLENBQUNBO1lBQzVELE1BQU1uRyxRQUFRLElBQUksQ0FBQ00sS0FBSyxDQUFDOEYsUUFBUSxFQUFFO2dCQUFFaEY7Z0JBQU80RTtZQUFJO1FBQ3BEO0lBQ0o7SUFDQTdHLEtBQUsrQyxJQUFJLEVBQUU7UUFDUCxNQUFNZ0IsS0FBS2pELFNBQVNpQztRQUNwQixJQUFJLENBQUNtRSxXQUFXLENBQUM7WUFBRUMsV0FBVztRQUFLO1FBQ25DLElBQUksQ0FBQ0MsUUFBUSxDQUFDckQ7UUFDZCxJQUFJLENBQUNuQyxRQUFRLENBQUM1Qix1REFBSUEsQ0FBQytEO1FBQ25CLE9BQU8sSUFBSTlFLFFBQVEsQ0FBQ0Q7WUFDaEJnQyxRQUFRcUcsV0FBVyxHQUFHckk7UUFDMUI7SUFDSjtJQUNBc0ksT0FBT0MsUUFBUSxFQUFFeEksS0FBSyxFQUFFO1FBQ3BCLE9BQU9OLFVBQVUsSUFBSSxFQUFFLEtBQUssR0FBRyxLQUFLLEdBQUc7WUFDbkMsSUFBSSxDQUFDOEksVUFDRDtZQUNKLE1BQU14RCxLQUFLakQsU0FBU3lHO1lBQ3BCLE1BQU0xRyxRQUFRLElBQUksQ0FBQ00sS0FBSyxDQUFDcUcsUUFBUSxFQUFFO2dCQUMvQnpEO2dCQUNBMEQsTUFBTTFJO2dCQUNOZ0UsTUFBTSxJQUFJLENBQUNXLEdBQUcsQ0FBQ0s7WUFDbkI7WUFDQSxJQUFJLENBQUNuQyxRQUFRLENBQUM1Qix1REFBSUEsQ0FBQztZQUNuQixJQUFJLENBQUNrSCxXQUFXLENBQUM7Z0JBQUVDLFdBQVc7Z0JBQU9wSTtZQUFNO1lBQzNDdUgsV0FBVyxJQUFNLElBQUksQ0FBQ29CLE9BQU8sS0FBSywyQkFBMkI7UUFDakU7SUFDSjtJQUNBQyxRQUFRO1FBQ0osSUFBSSxDQUFDL0YsUUFBUSxDQUFDNUIsdURBQUlBLENBQUM7UUFDbkIsSUFBSSxDQUFDa0gsV0FBVyxDQUFDO1lBQUVDLFdBQVc7UUFBSztRQUNuQ2IsV0FBVyxJQUFNLElBQUksQ0FBQ29CLE9BQU8sS0FBSywyQkFBMkI7SUFDakU7SUFDQWxCLFNBQVN6QyxFQUFFLEVBQUU7UUFDVCxNQUFNaEIsT0FBTyxJQUFJLENBQUNXLEdBQUcsQ0FBQzNDLGFBQWFnRDtRQUNuQyxJQUFJLENBQUNoQixNQUNEO1FBQ0psQyxRQUFRLElBQUksQ0FBQ00sS0FBSyxDQUFDeUcsVUFBVSxFQUFFN0U7SUFDbkM7SUFDQW1FLFlBQVluSSxLQUFLLEVBQUU7UUFDZixNQUFNQyxVQUFVZ0MsUUFBUXFHLFdBQVc7UUFDbkMsSUFBSXJJLFNBQ0FBLFFBQVFEO1FBQ1ppQyxRQUFRcUcsV0FBVyxHQUFHO0lBQzFCO0lBQ0EsdUJBQXVCLEdBQ3ZCLElBQUlRLGNBQWM7UUFDZCxPQUFPLElBQUksQ0FBQy9GLEtBQUssQ0FBQ0csS0FBSyxDQUFDMUIsU0FBUyxDQUFDc0csR0FBRztJQUN6QztJQUNBLElBQUlpQixnQkFBZ0I7UUFDaEIsSUFBSTdGLFFBQVEsRUFBRTtRQUNkLEtBQUssSUFBSThCLE1BQU00QyxNQUFNb0IsSUFBSSxDQUFDLElBQUksQ0FBQ0YsV0FBVyxFQUFHO1lBQ3pDLE1BQU05RSxPQUFPLElBQUksQ0FBQ1csR0FBRyxDQUFDSztZQUN0QixJQUFJaEIsTUFDQWQsTUFBTStGLElBQUksQ0FBQ2pGO1FBQ25CO1FBQ0EsT0FBT2Q7SUFDWDtJQUNBaEMsTUFBTThDLElBQUksRUFBRStDLE9BQU8sQ0FBQyxDQUFDLEVBQUU7UUFDbkIsSUFBSSxDQUFDL0MsTUFDRDtRQUNKOzsyQkFFbUIsR0FDbkIsSUFBSSxJQUFJLENBQUM1QixLQUFLLENBQUM4RyxxQkFBcUIsRUFBRTtZQUNsQyxJQUFJLENBQUMxQixNQUFNLENBQUN4RDtRQUNoQixPQUNLO1lBQ0QsSUFBSSxDQUFDbkIsUUFBUSxDQUFDM0IseURBQUtBLENBQUNhLFNBQVNpQztZQUM3QixJQUFJK0MsS0FBS29DLE1BQU0sS0FBSyxPQUNoQixJQUFJLENBQUNkLFFBQVEsQ0FBQ3JFO1lBQ2xCLElBQUksSUFBSSxDQUFDcUIsV0FBVyxFQUNoQnZELFFBQVEsSUFBSSxDQUFDTSxLQUFLLENBQUN1RyxPQUFPLEVBQUUsSUFBSSxDQUFDdEQsV0FBVztRQUNwRDtJQUNKO0lBQ0ErRCxTQUFTO1FBQ0wsSUFBSTlGLElBQUkwRDtRQUNSLE1BQU1kLFFBQVEsSUFBSSxDQUFDM0QsaUJBQWlCO1FBQ3BDLE1BQU04RyxPQUFPLElBQUksQ0FBQzdHLGdCQUFnQjtRQUNsQyxNQUFNOEcsT0FBT0QsT0FBT25EO1FBQ3BCLElBQUlULFFBQVEsQ0FBQ3VCLEtBQUssQ0FBQzFELEtBQUssSUFBSSxDQUFDK0IsV0FBVyxNQUFNLFFBQVEvQixPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUdpRyxRQUFRLE1BQU0sUUFBUXZDLE9BQU8sS0FBSyxJQUFJQSxLQUFLO1FBQzdILElBQUl2QixRQUFRUyxPQUFPO1lBQ2ZULFFBQVFTO1FBQ1osT0FDSztZQUNEVCxRQUFRVSxLQUFLRyxHQUFHLENBQUNKLFFBQVFvRCxNQUFNO1FBQ25DO1FBQ0EsSUFBSSxDQUFDcEksS0FBSyxDQUFDLElBQUksQ0FBQ3lFLEVBQUUsQ0FBQ0Y7SUFDdkI7SUFDQStELFdBQVc7UUFDUCxJQUFJbEcsSUFBSTBEO1FBQ1IsTUFBTWQsUUFBUSxJQUFJLENBQUMzRCxpQkFBaUI7UUFDcEMsTUFBTThHLE9BQU8sSUFBSSxDQUFDN0csZ0JBQWdCO1FBQ2xDLE1BQU04RyxPQUFPRCxPQUFPbkQ7UUFDcEIsSUFBSVQsUUFBUSxDQUFDdUIsS0FBSyxDQUFDMUQsS0FBSyxJQUFJLENBQUMrQixXQUFXLE1BQU0sUUFBUS9CLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR2lHLFFBQVEsTUFBTSxRQUFRdkMsT0FBTyxLQUFLLElBQUlBLEtBQUs7UUFDN0gsSUFBSXZCLFFBQVE0RCxNQUFNO1lBQ2Q1RCxRQUFRNEQ7UUFDWixPQUNLO1lBQ0Q1RCxRQUFRVSxLQUFLQyxHQUFHLENBQUNYLFFBQVE2RCxNQUFNLElBQUksQ0FBQzVHLFlBQVksQ0FBQzBDLE1BQU0sR0FBRztRQUM5RDtRQUNBLElBQUksQ0FBQ2xFLEtBQUssQ0FBQyxJQUFJLENBQUN5RSxFQUFFLENBQUNGO0lBQ3ZCO0lBQ0ErQixPQUFPeEQsSUFBSSxFQUFFK0MsT0FBTyxDQUFDLENBQUMsRUFBRTtRQUNwQixJQUFJLENBQUMvQyxNQUNEO1FBQ0osTUFBTXlGLGNBQWMxQyxLQUFLN0YsS0FBSyxLQUFLO1FBQ25DLE1BQU04RCxLQUFLakQsU0FBU2lDO1FBQ3BCLElBQUl5RixhQUNBLElBQUksQ0FBQzVHLFFBQVEsQ0FBQzNCLHlEQUFLQSxDQUFDOEQ7UUFDeEIsSUFBSSxDQUFDbkMsUUFBUSxDQUFDckIsMkRBQVNBLENBQUNrSSxJQUFJLENBQUMxRTtRQUM3QixJQUFJLENBQUNuQyxRQUFRLENBQUNyQiwyREFBU0EsQ0FBQ21JLE1BQU0sQ0FBQzNFO1FBQy9CLElBQUksQ0FBQ25DLFFBQVEsQ0FBQ3JCLDJEQUFTQSxDQUFDK0QsVUFBVSxDQUFDUDtRQUNuQyxJQUFJLENBQUNxRCxRQUFRLENBQUNyRCxJQUFJK0IsS0FBSzZDLEtBQUs7UUFDNUIsSUFBSSxJQUFJLENBQUN2RSxXQUFXLElBQUlvRSxhQUFhO1lBQ2pDM0gsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3VHLE9BQU8sRUFBRSxJQUFJLENBQUN0RCxXQUFXO1FBQ2hEO1FBQ0F2RCxRQUFRLElBQUksQ0FBQ00sS0FBSyxDQUFDeUgsUUFBUSxFQUFFLElBQUksQ0FBQ2QsYUFBYTtJQUNuRDtJQUNBZSxTQUFTOUYsSUFBSSxFQUFFO1FBQ1gsSUFBSSxDQUFDQSxNQUNEO1FBQ0osTUFBTWdCLEtBQUtqRCxTQUFTaUM7UUFDcEIsSUFBSSxDQUFDbkIsUUFBUSxDQUFDckIsMkRBQVNBLENBQUN1SSxNQUFNLENBQUMvRTtRQUMvQmxELFFBQVEsSUFBSSxDQUFDTSxLQUFLLENBQUN5SCxRQUFRLEVBQUUsSUFBSSxDQUFDZCxhQUFhO0lBQ25EO0lBQ0FpQixZQUFZeEIsUUFBUSxFQUFFO1FBQ2xCLE1BQU14RSxPQUFPLElBQUksQ0FBQ1csR0FBRyxDQUFDM0MsYUFBYXdHO1FBQ25DLElBQUksQ0FBQ3hFLE1BQ0Q7UUFDSixJQUFJLENBQUNuQixRQUFRLENBQUMzQix5REFBS0EsQ0FBQzhDLEtBQUtnQixFQUFFO1FBQzNCLElBQUksQ0FBQ25DLFFBQVEsQ0FBQ3JCLDJEQUFTQSxDQUFDeUksR0FBRyxDQUFDakcsS0FBS2dCLEVBQUU7UUFDbkMsSUFBSSxDQUFDbkMsUUFBUSxDQUFDckIsMkRBQVNBLENBQUNtSSxNQUFNLENBQUMzRixLQUFLZ0IsRUFBRTtRQUN0QyxJQUFJLENBQUNuQyxRQUFRLENBQUNyQiwyREFBU0EsQ0FBQytELFVBQVUsQ0FBQ3ZCLEtBQUtnQixFQUFFO1FBQzFDLElBQUksQ0FBQ3FELFFBQVEsQ0FBQ3JFO1FBQ2QsSUFBSSxJQUFJLENBQUNxQixXQUFXLEVBQ2hCdkQsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3VHLE9BQU8sRUFBRSxJQUFJLENBQUN0RCxXQUFXO1FBQ2hEdkQsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3lILFFBQVEsRUFBRSxJQUFJLENBQUNkLGFBQWE7SUFDbkQ7SUFDQW1CLGlCQUFpQjFCLFFBQVEsRUFBRTtRQUN2QixJQUFJLENBQUNBLFVBQ0Q7UUFDSixNQUFNeEQsS0FBS2pELFNBQVN5RztRQUNwQixNQUFNLEVBQUVtQixNQUFNLEVBQUVwRSxVQUFVLEVBQUUsR0FBRyxJQUFJLENBQUN4QyxLQUFLLENBQUNHLEtBQUssQ0FBQzFCLFNBQVM7UUFDekQsSUFBSSxDQUFDcUIsUUFBUSxDQUFDM0IseURBQUtBLENBQUM4RDtRQUNwQixJQUFJLENBQUNuQyxRQUFRLENBQUNyQiwyREFBU0EsQ0FBQ3VJLE1BQU0sQ0FBQyxJQUFJLENBQUNsRSxZQUFZLENBQUM4RCxRQUFRcEU7UUFDekQsSUFBSSxDQUFDMUMsUUFBUSxDQUFDckIsMkRBQVNBLENBQUN5SSxHQUFHLENBQUMsSUFBSSxDQUFDcEUsWUFBWSxDQUFDOEQsUUFBUTNILGFBQWFnRDtRQUNuRSxJQUFJLENBQUNuQyxRQUFRLENBQUNyQiwyREFBU0EsQ0FBQytELFVBQVUsQ0FBQ1A7UUFDbkMsSUFBSSxDQUFDcUQsUUFBUSxDQUFDckQ7UUFDZCxJQUFJLElBQUksQ0FBQ0ssV0FBVyxFQUNoQnZELFFBQVEsSUFBSSxDQUFDTSxLQUFLLENBQUN1RyxPQUFPLEVBQUUsSUFBSSxDQUFDdEQsV0FBVztRQUNoRHZELFFBQVEsSUFBSSxDQUFDTSxLQUFLLENBQUN5SCxRQUFRLEVBQUUsSUFBSSxDQUFDZCxhQUFhO0lBQ25EO0lBQ0FvQixjQUFjO1FBQ1YsSUFBSSxDQUFDQyxZQUFZLENBQUM7WUFBRXRDLEtBQUssRUFBRTtZQUFFNkIsUUFBUTtZQUFNcEUsWUFBWTtRQUFLO1FBQzVEekQsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3lILFFBQVEsRUFBRSxJQUFJLENBQUNkLGFBQWE7SUFDbkQ7SUFDQXNCLFlBQVk7UUFDUixJQUFJL0c7UUFDSixJQUFJLENBQUM4RyxZQUFZLENBQUM7WUFDZHRDLEtBQUt6RCxPQUFPaUcsSUFBSSxDQUFDLElBQUksQ0FBQzNILFNBQVM7WUFDL0JnSCxRQUFRLElBQUksQ0FBQ3pFLFNBQVM7WUFDdEJLLFlBQVksSUFBSSxDQUFDSixRQUFRO1FBQzdCO1FBQ0EsSUFBSSxDQUFDdEMsUUFBUSxDQUFDM0IseURBQUtBLENBQUMsQ0FBQ29DLEtBQUssSUFBSSxDQUFDNkIsUUFBUSxNQUFNLFFBQVE3QixPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUcwQixFQUFFO1FBQ25GLElBQUksSUFBSSxDQUFDSyxXQUFXLEVBQ2hCdkQsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3VHLE9BQU8sRUFBRSxJQUFJLENBQUN0RCxXQUFXO1FBQ2hEdkQsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3lILFFBQVEsRUFBRSxJQUFJLENBQUNkLGFBQWE7SUFDbkQ7SUFDQXFCLGFBQWFHLElBQUksRUFBRTtRQUNmLElBQUlqSDtRQUNKLE1BQU13RSxNQUFNLElBQUkwQyxJQUFJLENBQUNsSCxLQUFLaUgsS0FBS3pDLEdBQUcsTUFBTSxRQUFReEUsT0FBTyxLQUFLLElBQUksS0FBSyxJQUFJQSxHQUFHeUUsR0FBRyxDQUFDaEc7UUFDaEYsTUFBTTRILFNBQVMzSCxhQUFhdUksS0FBS1osTUFBTTtRQUN2QyxNQUFNcEUsYUFBYXZELGFBQWF1SSxLQUFLaEYsVUFBVTtRQUMvQyxJQUFJLENBQUMxQyxRQUFRLENBQUNyQiwyREFBU0EsQ0FBQ2lKLEdBQUcsQ0FBQztZQUFFM0M7WUFBSzZCO1lBQVFwRTtRQUFXO1FBQ3REekQsUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQ3lILFFBQVEsRUFBRSxJQUFJLENBQUNkLGFBQWE7SUFDbkQ7SUFDQSxpQkFBaUIsR0FDakIsSUFBSTJCLGlCQUFpQjtRQUNqQixNQUFNLEVBQUVDLE1BQU0sRUFBRSxHQUFHLElBQUksQ0FBQzVILEtBQUssQ0FBQ3RCLEdBQUc7UUFDakMsT0FBUWtKLE9BQU8vRCxJQUFJO1lBQ2YsS0FBSztnQkFDRCxPQUFPK0QsT0FBTzNGLEVBQUU7WUFDcEI7Z0JBQ0ksT0FBTztRQUNmO0lBQ0o7SUFDQSxJQUFJNEYsbUJBQW1CO1FBQ25CLE9BQU8sSUFBSSxDQUFDN0gsS0FBSyxDQUFDdEIsR0FBRyxDQUFDa0osTUFBTSxDQUFDL0QsSUFBSSxLQUFLO0lBQzFDO0lBQ0EsSUFBSWlFLFlBQVk7UUFDWixPQUFPLElBQUksQ0FBQzlILEtBQUssQ0FBQ3RCLEdBQUcsQ0FBQ3FKLE9BQU8sQ0FDeEIvQyxHQUFHLENBQUMsQ0FBQy9DLEtBQU8sSUFBSSxDQUFDTCxHQUFHLENBQUNLLEtBQ3JCZ0QsTUFBTSxDQUFDLENBQUNDLElBQU0sQ0FBQyxDQUFDQTtJQUN6QjtJQUNBLElBQUk4QyxXQUFXO1FBQ1gsT0FBTyxJQUFJLENBQUNwRyxHQUFHLENBQUMsSUFBSSxDQUFDNUIsS0FBSyxDQUFDRyxLQUFLLENBQUM4SCxJQUFJLENBQUNoRyxFQUFFO0lBQzVDO0lBQ0EsSUFBSWlHLHdCQUF3QjtRQUN4QixPQUFPLElBQUksQ0FBQ3RHLEdBQUcsQ0FBQyxJQUFJLENBQUM1QixLQUFLLENBQUNHLEtBQUssQ0FBQzhILElBQUksQ0FBQ0UsbUJBQW1CO0lBQzdEO0lBQ0EsSUFBSUMsdUJBQXVCO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDcEksS0FBSyxDQUFDRyxLQUFLLENBQUM4SCxJQUFJLENBQUNJLGdCQUFnQjtJQUNqRDtJQUNBQyxVQUFVO1FBQ04sSUFBSS9IO1FBQ0osSUFBSSxJQUFJLENBQUNnSSxVQUFVLEVBQ2YsT0FBTztRQUNYLE1BQU1oRSxhQUFhLENBQUNoRSxLQUFLLElBQUksQ0FBQ3FCLEdBQUcsQ0FBQyxJQUFJLENBQUM1QixLQUFLLENBQUN0QixHQUFHLENBQUN3RixRQUFRLE9BQU8sUUFBUTNELE9BQU8sS0FBSyxJQUFJQSxLQUFLLElBQUksQ0FBQ2IsSUFBSTtRQUN0RyxNQUFNb0ksWUFBWSxJQUFJLENBQUNBLFNBQVM7UUFDaEMsTUFBTVUsYUFBYSxJQUFJLENBQUNuSixLQUFLLENBQUNvSixXQUFXO1FBQ3pDLEtBQUssTUFBTVIsUUFBUUgsVUFBVztZQUMxQixJQUFJLENBQUNHLE1BQ0QsT0FBTztZQUNYLElBQUksQ0FBQzFELFlBQ0QsT0FBTztZQUNYLElBQUkwRCxLQUFLUyxVQUFVLElBQUk1SyxnREFBa0IsQ0FBQ3lHLFlBQVkwRCxPQUNsRCxPQUFPO1FBQ2Y7UUFDQSwyQ0FBMkM7UUFDM0MsSUFBSSxPQUFPTyxjQUFjLFlBQVk7WUFDakMsT0FBTyxDQUFDQSxXQUFXO2dCQUNmakU7Z0JBQ0F1RCxXQUFXLElBQUksQ0FBQ0EsU0FBUztnQkFDekJwRixPQUFPLElBQUksQ0FBQzFDLEtBQUssQ0FBQ3RCLEdBQUcsQ0FBQ2dFLEtBQUssSUFBSTtZQUNuQztRQUNKLE9BQ0ssSUFBSSxPQUFPOEYsY0FBYyxVQUFVO1lBQ3BDLGFBQWE7WUFDYixPQUFPLENBQUNqRSxXQUFXL0MsSUFBSSxDQUFDZ0gsV0FBVztRQUN2QyxPQUNLLElBQUksT0FBT0EsZUFBZSxXQUFXO1lBQ3RDLE9BQU8sQ0FBQ0E7UUFDWixPQUNLO1lBQ0QsT0FBTztRQUNYO0lBQ0o7SUFDQUksYUFBYTtRQUNULElBQUksQ0FBQzlJLFFBQVEsQ0FBQ3BCLHFEQUFHQSxDQUFDa0osTUFBTSxDQUFDO1lBQUUvRCxNQUFNO1FBQU87SUFDNUM7SUFDQWdGLFdBQVdqQixNQUFNLEVBQUU7UUFDZixJQUFJLENBQUM5SCxRQUFRLENBQUNwQixxREFBR0EsQ0FBQ2tKLE1BQU0sQ0FBQ0E7SUFDN0I7SUFDQSxjQUFjLEdBQ2R4SCxLQUFLcUYsUUFBUSxFQUFFO1FBQ1gsTUFBTXhELEtBQUtoRCxhQUFhd0c7UUFDeEIsSUFBSSxDQUFDeEQsSUFDRDtRQUNKLElBQUksSUFBSSxDQUFDNkcsTUFBTSxDQUFDN0csS0FDWjtRQUNKLElBQUksQ0FBQ25DLFFBQVEsQ0FBQ3RCLHNEQUFVQSxDQUFDNEIsSUFBSSxDQUFDNkIsSUFBSSxJQUFJLENBQUNzRyxVQUFVO1FBQ2pEeEosUUFBUSxJQUFJLENBQUNNLEtBQUssQ0FBQzBKLFFBQVEsRUFBRTlHO0lBQ2pDO0lBQ0ErRyxNQUFNdkQsUUFBUSxFQUFFO1FBQ1osTUFBTXhELEtBQUtoRCxhQUFhd0c7UUFDeEIsSUFBSSxDQUFDeEQsSUFDRDtRQUNKLElBQUksQ0FBQyxJQUFJLENBQUM2RyxNQUFNLENBQUM3RyxLQUNiO1FBQ0osSUFBSSxDQUFDbkMsUUFBUSxDQUFDdEIsc0RBQVVBLENBQUN3SyxLQUFLLENBQUMvRyxJQUFJLElBQUksQ0FBQ3NHLFVBQVU7UUFDbER4SixRQUFRLElBQUksQ0FBQ00sS0FBSyxDQUFDMEosUUFBUSxFQUFFOUc7SUFDakM7SUFDQWdILE9BQU94RCxRQUFRLEVBQUU7UUFDYixNQUFNeEQsS0FBS2hELGFBQWF3RztRQUN4QixJQUFJLENBQUN4RCxJQUNEO1FBQ0osT0FBTyxJQUFJLENBQUM2RyxNQUFNLENBQUM3RyxNQUFNLElBQUksQ0FBQytHLEtBQUssQ0FBQy9HLE1BQU0sSUFBSSxDQUFDN0IsSUFBSSxDQUFDNkI7SUFDeEQ7SUFDQWlILFlBQVl6RCxRQUFRLEVBQUU7UUFDbEIsTUFBTXhELEtBQUtoRCxhQUFhd0c7UUFDeEIsSUFBSSxDQUFDeEQsSUFDRDtRQUNKLE1BQU1oQixPQUFPbkQsdUNBQVMsQ0FBQyxJQUFJLENBQUM0QixJQUFJLEVBQUV1QztRQUNsQyxJQUFJbUgsU0FBU25JLFNBQVMsUUFBUUEsU0FBUyxLQUFLLElBQUksS0FBSyxJQUFJQSxLQUFLbUksTUFBTTtRQUNwRSxNQUFPQSxPQUFRO1lBQ1gsSUFBSSxDQUFDaEosSUFBSSxDQUFDZ0osT0FBT25ILEVBQUU7WUFDbkJtSCxTQUFTQSxPQUFPQSxNQUFNO1FBQzFCO0lBQ0o7SUFDQUMsYUFBYXBJLElBQUksRUFBRTtRQUNmLE1BQU1tSSxTQUFTbkksS0FBS21JLE1BQU07UUFDMUIsSUFBSSxDQUFDQSxRQUFRO1lBQ1QsSUFBSSxDQUFDSCxNQUFNLENBQUNoSSxLQUFLZ0IsRUFBRTtRQUN2QixPQUNLLElBQUltSCxPQUFPRSxRQUFRLEVBQUU7WUFDdEIsTUFBTVIsU0FBUzdILEtBQUs2SCxNQUFNO1lBQzFCLEtBQUssSUFBSVMsV0FBV0gsT0FBT0UsUUFBUSxDQUFFO2dCQUNqQyxJQUFJQyxRQUFRYixVQUFVLEVBQUU7b0JBQ3BCSSxTQUFTLElBQUksQ0FBQ0UsS0FBSyxDQUFDTyxRQUFRdEgsRUFBRSxJQUFJLElBQUksQ0FBQzdCLElBQUksQ0FBQ21KLFFBQVF0SCxFQUFFO2dCQUMxRDtZQUNKO1lBQ0EsSUFBSSxDQUFDcUQsUUFBUSxDQUFDLElBQUksQ0FBQ2hELFdBQVc7UUFDbEM7SUFDSjtJQUNBa0gsVUFBVTtRQUNOMUwsd0NBQVUsQ0FBQyxJQUFJLENBQUM0QixJQUFJLEVBQUUsQ0FBQ3VCO1lBQ25CLElBQUlBLEtBQUt5SCxVQUFVLEVBQ2Z6SCxLQUFLYixJQUFJO1FBQ2pCO0lBQ0o7SUFDQXNKLFdBQVc7UUFDUDVMLHdDQUFVLENBQUMsSUFBSSxDQUFDNEIsSUFBSSxFQUFFLENBQUN1QjtZQUNuQixJQUFJQSxLQUFLeUgsVUFBVSxFQUNmekgsS0FBSytILEtBQUs7UUFDbEI7SUFDSjtJQUNBLGFBQWEsR0FDYjFELFNBQVNHLFFBQVEsRUFBRW9CLFFBQVEsT0FBTyxFQUFFO1FBQ2hDLElBQUksQ0FBQ3BCLFVBQ0Q7UUFDSixNQUFNeEQsS0FBS2pELFNBQVN5RztRQUNwQixJQUFJLENBQUN5RCxXQUFXLENBQUNqSDtRQUNqQixPQUFPbkUsMkNBQ0ssQ0FBQyxJQUFNbUUsTUFBTSxJQUFJLENBQUNyQyxTQUFTLEVBQ2xDaEMsSUFBSSxDQUFDO1lBQ04sSUFBSTJDO1lBQ0osTUFBTW1DLFFBQVEsSUFBSSxDQUFDOUMsU0FBUyxDQUFDcUMsR0FBRztZQUNoQyxJQUFJUyxVQUFVeUIsV0FDVjtZQUNINUQsQ0FBQUEsS0FBSyxJQUFJLENBQUNqQixJQUFJLENBQUNzSyxPQUFPLE1BQU0sUUFBUXJKLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR3NKLFlBQVksQ0FBQ25ILE9BQU9tRTtRQUN6RixHQUNLaUQsS0FBSyxDQUFDO1FBQ1Asd0NBQXdDO1FBQzVDO0lBQ0o7SUFDQSxnQkFBZ0IsR0FDaEIsSUFBSUMsWUFBWTtRQUNaLE9BQU8sSUFBSSxDQUFDL0osS0FBSyxDQUFDRyxLQUFLLENBQUNqQyxJQUFJLENBQUMrRCxFQUFFLEtBQUs7SUFDeEM7SUFDQSxJQUFJc0csYUFBYTtRQUNiLElBQUloSTtRQUNKLE9BQU8sQ0FBQyxDQUFFLEVBQUNBLEtBQUssSUFBSSxDQUFDbEIsS0FBSyxDQUFDdUIsVUFBVSxNQUFNLFFBQVFMLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR00sSUFBSSxFQUFDO0lBQ3hGO0lBQ0EsSUFBSW1KLFdBQVc7UUFDWCxPQUFPLElBQUksQ0FBQ2hLLEtBQUssQ0FBQ0csS0FBSyxDQUFDaEMsS0FBSyxDQUFDOEwsV0FBVztJQUM3QztJQUNBLElBQUlDLGlCQUFpQjtRQUNqQixPQUFPLElBQUksQ0FBQ2xLLEtBQUssQ0FBQ0csS0FBSyxDQUFDMUIsU0FBUyxDQUFDc0csR0FBRyxDQUFDb0YsSUFBSSxLQUFLO0lBQ25EO0lBQ0EsSUFBSUMsa0JBQWtCO1FBQ2xCLE9BQU8sSUFBSSxDQUFDcEssS0FBSyxDQUFDRyxLQUFLLENBQUMxQixTQUFTLENBQUNzRyxHQUFHLENBQUNvRixJQUFJLEtBQUs7SUFDbkQ7SUFDQSxJQUFJRSx3QkFBd0I7UUFDeEIsT0FBTyxJQUFJLENBQUNySyxLQUFLLENBQUNHLEtBQUssQ0FBQzFCLFNBQVMsQ0FBQ3NHLEdBQUcsQ0FBQ29GLElBQUksR0FBRztJQUNqRDtJQUNBRyxXQUFXckksRUFBRSxFQUFFO1FBQ1gsSUFBSSxDQUFDQSxJQUNELE9BQU87UUFDWCxPQUFPLElBQUksQ0FBQ2pDLEtBQUssQ0FBQ0csS0FBSyxDQUFDMUIsU0FBUyxDQUFDc0csR0FBRyxDQUFDd0YsR0FBRyxDQUFDdEk7SUFDOUM7SUFDQTZHLE9BQU83RyxFQUFFLEVBQUU7UUFDUCxJQUFJMUIsSUFBSTBELElBQUl1RztRQUNaLElBQUksQ0FBQ3ZJLElBQ0QsT0FBTztRQUNYLElBQUlBLE9BQU8zRCxzREFBT0EsRUFDZCxPQUFPO1FBQ1gsTUFBTW1NLE1BQU0sQ0FBQ2xLLEtBQUssSUFBSSxDQUFDbEIsS0FBSyxDQUFDcUwsYUFBYSxNQUFNLFFBQVFuSyxPQUFPLEtBQUssSUFBSUEsS0FBSztRQUM3RSxJQUFJLElBQUksQ0FBQ2dJLFVBQVUsRUFBRTtZQUNqQixPQUFPLENBQUN0RSxLQUFLLElBQUksQ0FBQ2pFLEtBQUssQ0FBQ0csS0FBSyxDQUFDQyxJQUFJLENBQUN1SyxRQUFRLENBQUMxSSxHQUFHLE1BQU0sUUFBUWdDLE9BQU8sS0FBSyxJQUFJQSxLQUFLLE1BQU0sZ0RBQWdEO1FBQzVJLE9BQ0s7WUFDRCxPQUFPLENBQUN1RyxLQUFLLElBQUksQ0FBQ3hLLEtBQUssQ0FBQ0csS0FBSyxDQUFDQyxJQUFJLENBQUNDLFVBQVUsQ0FBQzRCLEdBQUcsTUFBTSxRQUFRdUksT0FBTyxLQUFLLElBQUlBLEtBQUtDO1FBQ3hGO0lBQ0o7SUFDQUcsV0FBV3BKLElBQUksRUFBRTtRQUNiLE1BQU1xSixRQUFRLElBQUksQ0FBQ3hMLEtBQUssQ0FBQ3lMLFdBQVcsSUFBSyxLQUFNLEtBQUk7UUFDbkQsT0FBTyxDQUFDaE4sMENBQVksQ0FBQzBELE1BQU1xSjtJQUMvQjtJQUNBRSxZQUFZdkosSUFBSSxFQUFFO1FBQ2QsTUFBTXFKLFFBQVEsSUFBSSxDQUFDeEwsS0FBSyxDQUFDMkwsV0FBVyxJQUFLLEtBQU0sS0FBSTtRQUNuRCxPQUFPLENBQUNsTiwwQ0FBWSxDQUFDMEQsTUFBTXFKO0lBQy9CO0lBQ0FJLFdBQVdoSyxJQUFJLEVBQUU7UUFDYixNQUFNZ0IsS0FBS2hELGFBQWFnQztRQUN4QixJQUFJLENBQUNnQixJQUNELE9BQU87UUFDWCxPQUFPLElBQUksQ0FBQ2pDLEtBQUssQ0FBQ0csS0FBSyxDQUFDOEgsSUFBSSxDQUFDaEcsRUFBRSxLQUFLQTtJQUN4QztJQUNBaUosVUFBVWpKLEVBQUUsRUFBRTtRQUNWLE9BQU8sSUFBSSxDQUFDK0gsUUFBUSxJQUFJLElBQUksQ0FBQ2hLLEtBQUssQ0FBQ0csS0FBSyxDQUFDaEMsS0FBSyxDQUFDOEQsRUFBRSxLQUFLQTtJQUMxRDtJQUNBa0osUUFBUWxLLElBQUksRUFBRTtRQUNWLE9BQU8sSUFBSSxDQUFDSCxPQUFPLENBQUNHO0lBQ3hCO0lBQ0FtSyxnQkFBZ0JuSyxJQUFJLEVBQUU7UUFDbEIsTUFBTWdCLEtBQUtoRCxhQUFhZ0M7UUFDeEIsSUFBSSxDQUFDZ0IsSUFDRCxPQUFPO1FBQ1gsTUFBTSxFQUFFa0csbUJBQW1CLEVBQUVFLGdCQUFnQixFQUFFLEdBQUcsSUFBSSxDQUFDckksS0FBSyxDQUFDRyxLQUFLLENBQUM4SCxJQUFJO1FBQ3ZFLE9BQU9oRyxPQUFPa0csdUJBQXVCRSxxQkFBcUI7SUFDOUQ7SUFDQSx1QkFBdUIsR0FDdkJ6QyxVQUFVO1FBQ04sTUFBTTNFLE9BQU8sSUFBSSxDQUFDcUIsV0FBVyxJQUFJLElBQUksQ0FBQ0gsU0FBUztRQUMvQyxJQUFJbEIsTUFDQSxJQUFJLENBQUNuQixRQUFRLENBQUMzQix5REFBS0EsQ0FBQzhDLEtBQUtnQixFQUFFO0lBQ25DO0lBQ0FvSixTQUFTO1FBQ0wsSUFBSSxDQUFDdkwsUUFBUSxDQUFDMUIsNERBQVFBO0lBQzFCO0lBQ0FrTixnQkFBZ0I5RCxJQUFJLEVBQUU7UUFDbEIsSUFBSSxDQUFDaEksaUJBQWlCLEdBQUdnSSxLQUFLaEksaUJBQWlCO1FBQy9DLElBQUksQ0FBQ0MsZ0JBQWdCLEdBQUcrSCxLQUFLL0gsZ0JBQWdCO0lBQ2pEO0lBQ0EsaUJBQWlCLEdBQ2pCLElBQUk4TCxrQkFBa0I7UUFDbEIsT0FBTyxJQUFJLENBQUNsTSxLQUFLLENBQUNrTSxlQUFlLElBQUkzTSwyRUFBZ0JBO0lBQ3pEO0lBQ0EsSUFBSTRNLFlBQVk7UUFDWixPQUFPLElBQUksQ0FBQ25NLEtBQUssQ0FBQ21NLFNBQVMsSUFBSXhOLGdFQUFVQTtJQUM3QztJQUNBLElBQUl5TixhQUFhO1FBQ2IsT0FBTyxJQUFJLENBQUNwTSxLQUFLLENBQUNpSyxRQUFRLElBQUlyTCxrRUFBV0E7SUFDN0M7SUFDQSxJQUFJeU4sb0JBQW9CO1FBQ3BCLE9BQU8sSUFBSSxDQUFDck0sS0FBSyxDQUFDcU0saUJBQWlCLElBQUkvTSxpRkFBa0JBO0lBQzdEO0lBQ0EsSUFBSWdOLGVBQWU7UUFDZixPQUFPLElBQUksQ0FBQ3RNLEtBQUssQ0FBQ3NNLFlBQVksSUFBSTVOLHNFQUFhQTtJQUNuRDtBQUNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWFyYm9yaXN0L2Rpc3QvbW9kdWxlL2ludGVyZmFjZXMvdHJlZS1hcGkuanM/ODFkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgX19hd2FpdGVyID0gKHRoaXMgJiYgdGhpcy5fX2F3YWl0ZXIpIHx8IGZ1bmN0aW9uICh0aGlzQXJnLCBfYXJndW1lbnRzLCBQLCBnZW5lcmF0b3IpIHtcbiAgICBmdW5jdGlvbiBhZG9wdCh2YWx1ZSkgeyByZXR1cm4gdmFsdWUgaW5zdGFuY2VvZiBQID8gdmFsdWUgOiBuZXcgUChmdW5jdGlvbiAocmVzb2x2ZSkgeyByZXNvbHZlKHZhbHVlKTsgfSk7IH1cbiAgICByZXR1cm4gbmV3IChQIHx8IChQID0gUHJvbWlzZSkpKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICAgICAgZnVuY3Rpb24gZnVsZmlsbGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yLm5leHQodmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiByZWplY3RlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvcltcInRocm93XCJdKHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgICAgZnVuY3Rpb24gc3RlcChyZXN1bHQpIHsgcmVzdWx0LmRvbmUgPyByZXNvbHZlKHJlc3VsdC52YWx1ZSkgOiBhZG9wdChyZXN1bHQudmFsdWUpLnRoZW4oZnVsZmlsbGVkLCByZWplY3RlZCk7IH1cbiAgICAgICAgc3RlcCgoZ2VuZXJhdG9yID0gZ2VuZXJhdG9yLmFwcGx5KHRoaXNBcmcsIF9hcmd1bWVudHMgfHwgW10pKS5uZXh0KCkpO1xuICAgIH0pO1xufTtcbmltcG9ydCAqIGFzIHV0aWxzIGZyb20gXCIuLi91dGlsc1wiO1xuaW1wb3J0IHsgRGVmYXVsdEN1cnNvciB9IGZyb20gXCIuLi9jb21wb25lbnRzL2RlZmF1bHQtY3Vyc29yXCI7XG5pbXBvcnQgeyBEZWZhdWx0Um93IH0gZnJvbSBcIi4uL2NvbXBvbmVudHMvZGVmYXVsdC1yb3dcIjtcbmltcG9ydCB7IERlZmF1bHROb2RlIH0gZnJvbSBcIi4uL2NvbXBvbmVudHMvZGVmYXVsdC1ub2RlXCI7XG5pbXBvcnQgeyBlZGl0IH0gZnJvbSBcIi4uL3N0YXRlL2VkaXQtc2xpY2VcIjtcbmltcG9ydCB7IGZvY3VzLCB0cmVlQmx1ciB9IGZyb20gXCIuLi9zdGF0ZS9mb2N1cy1zbGljZVwiO1xuaW1wb3J0IHsgY3JlYXRlUm9vdCwgUk9PVF9JRCB9IGZyb20gXCIuLi9kYXRhL2NyZWF0ZS1yb290XCI7XG5pbXBvcnQgeyBhY3Rpb25zIGFzIHZpc2liaWxpdHkgfSBmcm9tIFwiLi4vc3RhdGUvb3Blbi1zbGljZVwiO1xuaW1wb3J0IHsgYWN0aW9ucyBhcyBzZWxlY3Rpb24gfSBmcm9tIFwiLi4vc3RhdGUvc2VsZWN0aW9uLXNsaWNlXCI7XG5pbXBvcnQgeyBhY3Rpb25zIGFzIGRuZCB9IGZyb20gXCIuLi9zdGF0ZS9kbmQtc2xpY2VcIjtcbmltcG9ydCB7IERlZmF1bHREcmFnUHJldmlldyB9IGZyb20gXCIuLi9jb21wb25lbnRzL2RlZmF1bHQtZHJhZy1wcmV2aWV3XCI7XG5pbXBvcnQgeyBEZWZhdWx0Q29udGFpbmVyIH0gZnJvbSBcIi4uL2NvbXBvbmVudHMvZGVmYXVsdC1jb250YWluZXJcIjtcbmltcG9ydCB7IGNyZWF0ZUxpc3QgfSBmcm9tIFwiLi4vZGF0YS9jcmVhdGUtbGlzdFwiO1xuaW1wb3J0IHsgY3JlYXRlSW5kZXggfSBmcm9tIFwiLi4vZGF0YS9jcmVhdGUtaW5kZXhcIjtcbmNvbnN0IHsgc2FmZVJ1biwgaWRlbnRpZnksIGlkZW50aWZ5TnVsbCB9ID0gdXRpbHM7XG5leHBvcnQgY2xhc3MgVHJlZUFwaSB7XG4gICAgY29uc3RydWN0b3Ioc3RvcmUsIHByb3BzLCBsaXN0LCBsaXN0RWwpIHtcbiAgICAgICAgdGhpcy5zdG9yZSA9IHN0b3JlO1xuICAgICAgICB0aGlzLnByb3BzID0gcHJvcHM7XG4gICAgICAgIHRoaXMubGlzdCA9IGxpc3Q7XG4gICAgICAgIHRoaXMubGlzdEVsID0gbGlzdEVsO1xuICAgICAgICB0aGlzLnZpc2libGVTdGFydEluZGV4ID0gMDtcbiAgICAgICAgdGhpcy52aXNpYmxlU3RvcEluZGV4ID0gMDtcbiAgICAgICAgLyogQ2hhbmdlcyBoZXJlIG11c3QgYWxzbyBiZSBtYWRlIGluIHVwZGF0ZSgpICovXG4gICAgICAgIHRoaXMucm9vdCA9IGNyZWF0ZVJvb3QodGhpcyk7XG4gICAgICAgIHRoaXMudmlzaWJsZU5vZGVzID0gY3JlYXRlTGlzdCh0aGlzKTtcbiAgICAgICAgdGhpcy5pZFRvSW5kZXggPSBjcmVhdGVJbmRleCh0aGlzLnZpc2libGVOb2Rlcyk7XG4gICAgfVxuICAgIC8qIENoYW5nZXMgaGVyZSBtdXN0IGFsc28gYmUgbWFkZSBpbiBjb25zdHJ1Y3RvcigpICovXG4gICAgdXBkYXRlKHByb3BzKSB7XG4gICAgICAgIHRoaXMucHJvcHMgPSBwcm9wcztcbiAgICAgICAgdGhpcy5yb290ID0gY3JlYXRlUm9vdCh0aGlzKTtcbiAgICAgICAgdGhpcy52aXNpYmxlTm9kZXMgPSBjcmVhdGVMaXN0KHRoaXMpO1xuICAgICAgICB0aGlzLmlkVG9JbmRleCA9IGNyZWF0ZUluZGV4KHRoaXMudmlzaWJsZU5vZGVzKTtcbiAgICB9XG4gICAgLyogU3RvcmUgaGVscGVycyAqL1xuICAgIGRpc3BhdGNoKGFjdGlvbikge1xuICAgICAgICByZXR1cm4gdGhpcy5zdG9yZS5kaXNwYXRjaChhY3Rpb24pO1xuICAgIH1cbiAgICBnZXQgc3RhdGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0b3JlLmdldFN0YXRlKCk7XG4gICAgfVxuICAgIGdldCBvcGVuU3RhdGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLm5vZGVzLm9wZW4udW5maWx0ZXJlZDtcbiAgICB9XG4gICAgLyogVHJlZSBQcm9wcyAqL1xuICAgIGdldCB3aWR0aCgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKF9hID0gdGhpcy5wcm9wcy53aWR0aCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMzAwO1xuICAgIH1cbiAgICBnZXQgaGVpZ2h0KCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHJldHVybiAoX2EgPSB0aGlzLnByb3BzLmhlaWdodCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogNTAwO1xuICAgIH1cbiAgICBnZXQgaW5kZW50KCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHJldHVybiAoX2EgPSB0aGlzLnByb3BzLmluZGVudCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMjQ7XG4gICAgfVxuICAgIGdldCByb3dIZWlnaHQoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgcmV0dXJuIChfYSA9IHRoaXMucHJvcHMucm93SGVpZ2h0KSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAyNDtcbiAgICB9XG4gICAgZ2V0IG92ZXJzY2FuQ291bnQoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgcmV0dXJuIChfYSA9IHRoaXMucHJvcHMub3ZlcnNjYW5Db3VudCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMTtcbiAgICB9XG4gICAgZ2V0IHNlYXJjaFRlcm0oKSB7XG4gICAgICAgIHJldHVybiAodGhpcy5wcm9wcy5zZWFyY2hUZXJtIHx8IFwiXCIpLnRyaW0oKTtcbiAgICB9XG4gICAgZ2V0IG1hdGNoRm4oKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgbWF0Y2ggPSAoX2EgPSB0aGlzLnByb3BzLnNlYXJjaE1hdGNoKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAoKG5vZGUsIHRlcm0pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHN0cmluZyA9IEpTT04uc3RyaW5naWZ5KE9iamVjdC52YWx1ZXMobm9kZS5kYXRhKSk7XG4gICAgICAgICAgICByZXR1cm4gc3RyaW5nLnRvTG9jYWxlTG93ZXJDYXNlKCkuaW5jbHVkZXModGVybS50b0xvY2FsZUxvd2VyQ2FzZSgpKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiAobm9kZSkgPT4gbWF0Y2gobm9kZSwgdGhpcy5zZWFyY2hUZXJtKTtcbiAgICB9XG4gICAgYWNjZXNzQ2hpbGRyZW4oZGF0YSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGdldCA9IHRoaXMucHJvcHMuY2hpbGRyZW5BY2Nlc3NvciB8fCBcImNoaWxkcmVuXCI7XG4gICAgICAgIHJldHVybiAoX2EgPSB1dGlscy5hY2Nlc3MoZGF0YSwgZ2V0KSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogbnVsbDtcbiAgICB9XG4gICAgYWNjZXNzSWQoZGF0YSkge1xuICAgICAgICBjb25zdCBnZXQgPSB0aGlzLnByb3BzLmlkQWNjZXNzb3IgfHwgXCJpZFwiO1xuICAgICAgICBjb25zdCBpZCA9IHV0aWxzLmFjY2VzcyhkYXRhLCBnZXQpO1xuICAgICAgICBpZiAoIWlkKVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRGF0YSBtdXN0IGNvbnRhaW4gYW4gJ2lkJyBwcm9wZXJ0eSBvciBwcm9wcy5pZEFjY2Vzc29yIG11c3QgcmV0dXJuIGEgc3RyaW5nXCIpO1xuICAgICAgICByZXR1cm4gaWQ7XG4gICAgfVxuICAgIC8qIE5vZGUgQWNjZXNzICovXG4gICAgZ2V0IGZpcnN0Tm9kZSgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKF9hID0gdGhpcy52aXNpYmxlTm9kZXNbMF0pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG51bGw7XG4gICAgfVxuICAgIGdldCBsYXN0Tm9kZSgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKF9hID0gdGhpcy52aXNpYmxlTm9kZXNbdGhpcy52aXNpYmxlTm9kZXMubGVuZ3RoIC0gMV0pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG51bGw7XG4gICAgfVxuICAgIGdldCBmb2N1c2VkTm9kZSgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKF9hID0gdGhpcy5nZXQodGhpcy5zdGF0ZS5ub2Rlcy5mb2N1cy5pZCkpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG51bGw7XG4gICAgfVxuICAgIGdldCBtb3N0UmVjZW50Tm9kZSgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKF9hID0gdGhpcy5nZXQodGhpcy5zdGF0ZS5ub2Rlcy5zZWxlY3Rpb24ubW9zdFJlY2VudCkpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG51bGw7XG4gICAgfVxuICAgIGdldCBuZXh0Tm9kZSgpIHtcbiAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmluZGV4T2YodGhpcy5mb2N1c2VkTm9kZSk7XG4gICAgICAgIGlmIChpbmRleCA9PT0gbnVsbClcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICBlbHNlXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5hdChpbmRleCArIDEpO1xuICAgIH1cbiAgICBnZXQgcHJldk5vZGUoKSB7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5pbmRleE9mKHRoaXMuZm9jdXNlZE5vZGUpO1xuICAgICAgICBpZiAoaW5kZXggPT09IG51bGwpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuYXQoaW5kZXggLSAxKTtcbiAgICB9XG4gICAgZ2V0KGlkKSB7XG4gICAgICAgIGlmICghaWQpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgaWYgKGlkIGluIHRoaXMuaWRUb0luZGV4KVxuICAgICAgICAgICAgcmV0dXJuIHRoaXMudmlzaWJsZU5vZGVzW3RoaXMuaWRUb0luZGV4W2lkXV0gfHwgbnVsbDtcbiAgICAgICAgZWxzZVxuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGF0KGluZGV4KSB7XG4gICAgICAgIHJldHVybiB0aGlzLnZpc2libGVOb2Rlc1tpbmRleF0gfHwgbnVsbDtcbiAgICB9XG4gICAgbm9kZXNCZXR3ZWVuKHN0YXJ0SWQsIGVuZElkKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgaWYgKHN0YXJ0SWQgPT09IG51bGwgfHwgZW5kSWQgPT09IG51bGwpXG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIGNvbnN0IGluZGV4MSA9IChfYSA9IHRoaXMuaW5kZXhPZihzdGFydElkKSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMDtcbiAgICAgICAgY29uc3QgaW5kZXgyID0gdGhpcy5pbmRleE9mKGVuZElkKTtcbiAgICAgICAgaWYgKGluZGV4MiA9PT0gbnVsbClcbiAgICAgICAgICAgIHJldHVybiBbXTtcbiAgICAgICAgY29uc3Qgc3RhcnQgPSBNYXRoLm1pbihpbmRleDEsIGluZGV4Mik7XG4gICAgICAgIGNvbnN0IGVuZCA9IE1hdGgubWF4KGluZGV4MSwgaW5kZXgyKTtcbiAgICAgICAgcmV0dXJuIHRoaXMudmlzaWJsZU5vZGVzLnNsaWNlKHN0YXJ0LCBlbmQgKyAxKTtcbiAgICB9XG4gICAgaW5kZXhPZihpZCkge1xuICAgICAgICBjb25zdCBrZXkgPSB1dGlscy5pZGVudGlmeU51bGwoaWQpO1xuICAgICAgICBpZiAoIWtleSlcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICByZXR1cm4gdGhpcy5pZFRvSW5kZXhba2V5XTtcbiAgICB9XG4gICAgLyogRGF0YSBPcGVyYXRpb25zICovXG4gICAgZ2V0IGVkaXRpbmdJZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUubm9kZXMuZWRpdC5pZDtcbiAgICB9XG4gICAgY3JlYXRlSW50ZXJuYWwoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNyZWF0ZSh7IHR5cGU6IFwiaW50ZXJuYWxcIiB9KTtcbiAgICB9XG4gICAgY3JlYXRlTGVhZigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY3JlYXRlKHsgdHlwZTogXCJsZWFmXCIgfSk7XG4gICAgfVxuICAgIGNyZWF0ZSgpIHtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCBhcmd1bWVudHMsIHZvaWQgMCwgZnVuY3Rpb24qIChvcHRzID0ge30pIHtcbiAgICAgICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgICAgICBjb25zdCBwYXJlbnRJZCA9IG9wdHMucGFyZW50SWQgPT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgID8gdXRpbHMuZ2V0SW5zZXJ0UGFyZW50SWQodGhpcylcbiAgICAgICAgICAgICAgICA6IG9wdHMucGFyZW50SWQ7XG4gICAgICAgICAgICBjb25zdCBpbmRleCA9IChfYSA9IG9wdHMuaW5kZXgpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHV0aWxzLmdldEluc2VydEluZGV4KHRoaXMpO1xuICAgICAgICAgICAgY29uc3QgdHlwZSA9IChfYiA9IG9wdHMudHlwZSkgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogXCJsZWFmXCI7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0geWllbGQgc2FmZVJ1bih0aGlzLnByb3BzLm9uQ3JlYXRlLCB7XG4gICAgICAgICAgICAgICAgdHlwZSxcbiAgICAgICAgICAgICAgICBwYXJlbnRJZCxcbiAgICAgICAgICAgICAgICBpbmRleCxcbiAgICAgICAgICAgICAgICBwYXJlbnROb2RlOiB0aGlzLmdldChwYXJlbnRJZCksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5mb2N1cyhkYXRhKTtcbiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5lZGl0KGRhdGEpLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3QoZGF0YSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmFjdGl2YXRlKGRhdGEpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGRlbGV0ZShub2RlKSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBpZiAoIW5vZGUpXG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgY29uc3QgaWRlbnRzID0gQXJyYXkuaXNBcnJheShub2RlKSA/IG5vZGUgOiBbbm9kZV07XG4gICAgICAgICAgICBjb25zdCBpZHMgPSBpZGVudHMubWFwKGlkZW50aWZ5KTtcbiAgICAgICAgICAgIGNvbnN0IG5vZGVzID0gaWRzLm1hcCgoaWQpID0+IHRoaXMuZ2V0KGlkKSkuZmlsdGVyKChuKSA9PiAhIW4pO1xuICAgICAgICAgICAgeWllbGQgc2FmZVJ1bih0aGlzLnByb3BzLm9uRGVsZXRlLCB7IG5vZGVzLCBpZHMgfSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBlZGl0KG5vZGUpIHtcbiAgICAgICAgY29uc3QgaWQgPSBpZGVudGlmeShub2RlKTtcbiAgICAgICAgdGhpcy5yZXNvbHZlRWRpdCh7IGNhbmNlbGxlZDogdHJ1ZSB9KTtcbiAgICAgICAgdGhpcy5zY3JvbGxUbyhpZCk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goZWRpdChpZCkpO1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHtcbiAgICAgICAgICAgIFRyZWVBcGkuZWRpdFByb21pc2UgPSByZXNvbHZlO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgc3VibWl0KGlkZW50aXR5LCB2YWx1ZSkge1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICAgICAgaWYgKCFpZGVudGl0eSlcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICBjb25zdCBpZCA9IGlkZW50aWZ5KGlkZW50aXR5KTtcbiAgICAgICAgICAgIHlpZWxkIHNhZmVSdW4odGhpcy5wcm9wcy5vblJlbmFtZSwge1xuICAgICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICAgIG5hbWU6IHZhbHVlLFxuICAgICAgICAgICAgICAgIG5vZGU6IHRoaXMuZ2V0KGlkKSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdGhpcy5kaXNwYXRjaChlZGl0KG51bGwpKTtcbiAgICAgICAgICAgIHRoaXMucmVzb2x2ZUVkaXQoeyBjYW5jZWxsZWQ6IGZhbHNlLCB2YWx1ZSB9KTtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gdGhpcy5vbkZvY3VzKCkpOyAvLyBSZXR1cm4gZm9jdXMgdG8gZWxlbWVudDtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJlc2V0KCkge1xuICAgICAgICB0aGlzLmRpc3BhdGNoKGVkaXQobnVsbCkpO1xuICAgICAgICB0aGlzLnJlc29sdmVFZGl0KHsgY2FuY2VsbGVkOiB0cnVlIH0pO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHRoaXMub25Gb2N1cygpKTsgLy8gUmV0dXJuIGZvY3VzIHRvIGVsZW1lbnQ7XG4gICAgfVxuICAgIGFjdGl2YXRlKGlkKSB7XG4gICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmdldChpZGVudGlmeU51bGwoaWQpKTtcbiAgICAgICAgaWYgKCFub2RlKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBzYWZlUnVuKHRoaXMucHJvcHMub25BY3RpdmF0ZSwgbm9kZSk7XG4gICAgfVxuICAgIHJlc29sdmVFZGl0KHZhbHVlKSB7XG4gICAgICAgIGNvbnN0IHJlc29sdmUgPSBUcmVlQXBpLmVkaXRQcm9taXNlO1xuICAgICAgICBpZiAocmVzb2x2ZSlcbiAgICAgICAgICAgIHJlc29sdmUodmFsdWUpO1xuICAgICAgICBUcmVlQXBpLmVkaXRQcm9taXNlID0gbnVsbDtcbiAgICB9XG4gICAgLyogRm9jdXMgYW5kIFNlbGVjdGlvbiAqL1xuICAgIGdldCBzZWxlY3RlZElkcygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUubm9kZXMuc2VsZWN0aW9uLmlkcztcbiAgICB9XG4gICAgZ2V0IHNlbGVjdGVkTm9kZXMoKSB7XG4gICAgICAgIGxldCBub2RlcyA9IFtdO1xuICAgICAgICBmb3IgKGxldCBpZCBvZiBBcnJheS5mcm9tKHRoaXMuc2VsZWN0ZWRJZHMpKSB7XG4gICAgICAgICAgICBjb25zdCBub2RlID0gdGhpcy5nZXQoaWQpO1xuICAgICAgICAgICAgaWYgKG5vZGUpXG4gICAgICAgICAgICAgICAgbm9kZXMucHVzaChub2RlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbm9kZXM7XG4gICAgfVxuICAgIGZvY3VzKG5vZGUsIG9wdHMgPSB7fSkge1xuICAgICAgICBpZiAoIW5vZGUpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIC8qIEZvY3VzIGlzIHJlc3BvbnNpYmxlIGZvciBzY3JvbGxpbmcsIHdoaWxlIHNlbGVjdGlvbiBpc1xuICAgICAgICAgKiByZXNwb25zaWJsZSBmb3IgZm9jdXMuIElmIHNlbGVjdGlvbkZvbGxvd3NGb2N1cywgdGhlblxuICAgICAgICAgKiBqdXN0IHNlbGVjdCBpdC4gKi9cbiAgICAgICAgaWYgKHRoaXMucHJvcHMuc2VsZWN0aW9uRm9sbG93c0ZvY3VzKSB7XG4gICAgICAgICAgICB0aGlzLnNlbGVjdChub2RlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2goZm9jdXMoaWRlbnRpZnkobm9kZSkpKTtcbiAgICAgICAgICAgIGlmIChvcHRzLnNjcm9sbCAhPT0gZmFsc2UpXG4gICAgICAgICAgICAgICAgdGhpcy5zY3JvbGxUbyhub2RlKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmZvY3VzZWROb2RlKVxuICAgICAgICAgICAgICAgIHNhZmVSdW4odGhpcy5wcm9wcy5vbkZvY3VzLCB0aGlzLmZvY3VzZWROb2RlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBwYWdlVXAoKSB7XG4gICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgIGNvbnN0IHN0YXJ0ID0gdGhpcy52aXNpYmxlU3RhcnRJbmRleDtcbiAgICAgICAgY29uc3Qgc3RvcCA9IHRoaXMudmlzaWJsZVN0b3BJbmRleDtcbiAgICAgICAgY29uc3QgcGFnZSA9IHN0b3AgLSBzdGFydDtcbiAgICAgICAgbGV0IGluZGV4ID0gKF9iID0gKF9hID0gdGhpcy5mb2N1c2VkTm9kZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnJvd0luZGV4KSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAwO1xuICAgICAgICBpZiAoaW5kZXggPiBzdGFydCkge1xuICAgICAgICAgICAgaW5kZXggPSBzdGFydDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGluZGV4ID0gTWF0aC5tYXgoc3RhcnQgLSBwYWdlLCAwKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmZvY3VzKHRoaXMuYXQoaW5kZXgpKTtcbiAgICB9XG4gICAgcGFnZURvd24oKSB7XG4gICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgIGNvbnN0IHN0YXJ0ID0gdGhpcy52aXNpYmxlU3RhcnRJbmRleDtcbiAgICAgICAgY29uc3Qgc3RvcCA9IHRoaXMudmlzaWJsZVN0b3BJbmRleDtcbiAgICAgICAgY29uc3QgcGFnZSA9IHN0b3AgLSBzdGFydDtcbiAgICAgICAgbGV0IGluZGV4ID0gKF9iID0gKF9hID0gdGhpcy5mb2N1c2VkTm9kZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnJvd0luZGV4KSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAwO1xuICAgICAgICBpZiAoaW5kZXggPCBzdG9wKSB7XG4gICAgICAgICAgICBpbmRleCA9IHN0b3A7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpbmRleCA9IE1hdGgubWluKGluZGV4ICsgcGFnZSwgdGhpcy52aXNpYmxlTm9kZXMubGVuZ3RoIC0gMSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5mb2N1cyh0aGlzLmF0KGluZGV4KSk7XG4gICAgfVxuICAgIHNlbGVjdChub2RlLCBvcHRzID0ge30pIHtcbiAgICAgICAgaWYgKCFub2RlKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBjaGFuZ2VGb2N1cyA9IG9wdHMuZm9jdXMgIT09IGZhbHNlO1xuICAgICAgICBjb25zdCBpZCA9IGlkZW50aWZ5KG5vZGUpO1xuICAgICAgICBpZiAoY2hhbmdlRm9jdXMpXG4gICAgICAgICAgICB0aGlzLmRpc3BhdGNoKGZvY3VzKGlkKSk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goc2VsZWN0aW9uLm9ubHkoaWQpKTtcbiAgICAgICAgdGhpcy5kaXNwYXRjaChzZWxlY3Rpb24uYW5jaG9yKGlkKSk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goc2VsZWN0aW9uLm1vc3RSZWNlbnQoaWQpKTtcbiAgICAgICAgdGhpcy5zY3JvbGxUbyhpZCwgb3B0cy5hbGlnbik7XG4gICAgICAgIGlmICh0aGlzLmZvY3VzZWROb2RlICYmIGNoYW5nZUZvY3VzKSB7XG4gICAgICAgICAgICBzYWZlUnVuKHRoaXMucHJvcHMub25Gb2N1cywgdGhpcy5mb2N1c2VkTm9kZSk7XG4gICAgICAgIH1cbiAgICAgICAgc2FmZVJ1bih0aGlzLnByb3BzLm9uU2VsZWN0LCB0aGlzLnNlbGVjdGVkTm9kZXMpO1xuICAgIH1cbiAgICBkZXNlbGVjdChub2RlKSB7XG4gICAgICAgIGlmICghbm9kZSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3QgaWQgPSBpZGVudGlmeShub2RlKTtcbiAgICAgICAgdGhpcy5kaXNwYXRjaChzZWxlY3Rpb24ucmVtb3ZlKGlkKSk7XG4gICAgICAgIHNhZmVSdW4odGhpcy5wcm9wcy5vblNlbGVjdCwgdGhpcy5zZWxlY3RlZE5vZGVzKTtcbiAgICB9XG4gICAgc2VsZWN0TXVsdGkoaWRlbnRpdHkpIHtcbiAgICAgICAgY29uc3Qgbm9kZSA9IHRoaXMuZ2V0KGlkZW50aWZ5TnVsbChpZGVudGl0eSkpO1xuICAgICAgICBpZiAoIW5vZGUpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goZm9jdXMobm9kZS5pZCkpO1xuICAgICAgICB0aGlzLmRpc3BhdGNoKHNlbGVjdGlvbi5hZGQobm9kZS5pZCkpO1xuICAgICAgICB0aGlzLmRpc3BhdGNoKHNlbGVjdGlvbi5hbmNob3Iobm9kZS5pZCkpO1xuICAgICAgICB0aGlzLmRpc3BhdGNoKHNlbGVjdGlvbi5tb3N0UmVjZW50KG5vZGUuaWQpKTtcbiAgICAgICAgdGhpcy5zY3JvbGxUbyhub2RlKTtcbiAgICAgICAgaWYgKHRoaXMuZm9jdXNlZE5vZGUpXG4gICAgICAgICAgICBzYWZlUnVuKHRoaXMucHJvcHMub25Gb2N1cywgdGhpcy5mb2N1c2VkTm9kZSk7XG4gICAgICAgIHNhZmVSdW4odGhpcy5wcm9wcy5vblNlbGVjdCwgdGhpcy5zZWxlY3RlZE5vZGVzKTtcbiAgICB9XG4gICAgc2VsZWN0Q29udGlndW91cyhpZGVudGl0eSkge1xuICAgICAgICBpZiAoIWlkZW50aXR5KVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBpZCA9IGlkZW50aWZ5KGlkZW50aXR5KTtcbiAgICAgICAgY29uc3QgeyBhbmNob3IsIG1vc3RSZWNlbnQgfSA9IHRoaXMuc3RhdGUubm9kZXMuc2VsZWN0aW9uO1xuICAgICAgICB0aGlzLmRpc3BhdGNoKGZvY3VzKGlkKSk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goc2VsZWN0aW9uLnJlbW92ZSh0aGlzLm5vZGVzQmV0d2VlbihhbmNob3IsIG1vc3RSZWNlbnQpKSk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goc2VsZWN0aW9uLmFkZCh0aGlzLm5vZGVzQmV0d2VlbihhbmNob3IsIGlkZW50aWZ5TnVsbChpZCkpKSk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goc2VsZWN0aW9uLm1vc3RSZWNlbnQoaWQpKTtcbiAgICAgICAgdGhpcy5zY3JvbGxUbyhpZCk7XG4gICAgICAgIGlmICh0aGlzLmZvY3VzZWROb2RlKVxuICAgICAgICAgICAgc2FmZVJ1bih0aGlzLnByb3BzLm9uRm9jdXMsIHRoaXMuZm9jdXNlZE5vZGUpO1xuICAgICAgICBzYWZlUnVuKHRoaXMucHJvcHMub25TZWxlY3QsIHRoaXMuc2VsZWN0ZWROb2Rlcyk7XG4gICAgfVxuICAgIGRlc2VsZWN0QWxsKCkge1xuICAgICAgICB0aGlzLnNldFNlbGVjdGlvbih7IGlkczogW10sIGFuY2hvcjogbnVsbCwgbW9zdFJlY2VudDogbnVsbCB9KTtcbiAgICAgICAgc2FmZVJ1bih0aGlzLnByb3BzLm9uU2VsZWN0LCB0aGlzLnNlbGVjdGVkTm9kZXMpO1xuICAgIH1cbiAgICBzZWxlY3RBbGwoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgdGhpcy5zZXRTZWxlY3Rpb24oe1xuICAgICAgICAgICAgaWRzOiBPYmplY3Qua2V5cyh0aGlzLmlkVG9JbmRleCksXG4gICAgICAgICAgICBhbmNob3I6IHRoaXMuZmlyc3ROb2RlLFxuICAgICAgICAgICAgbW9zdFJlY2VudDogdGhpcy5sYXN0Tm9kZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goZm9jdXMoKF9hID0gdGhpcy5sYXN0Tm9kZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmlkKSk7XG4gICAgICAgIGlmICh0aGlzLmZvY3VzZWROb2RlKVxuICAgICAgICAgICAgc2FmZVJ1bih0aGlzLnByb3BzLm9uRm9jdXMsIHRoaXMuZm9jdXNlZE5vZGUpO1xuICAgICAgICBzYWZlUnVuKHRoaXMucHJvcHMub25TZWxlY3QsIHRoaXMuc2VsZWN0ZWROb2Rlcyk7XG4gICAgfVxuICAgIHNldFNlbGVjdGlvbihhcmdzKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgaWRzID0gbmV3IFNldCgoX2EgPSBhcmdzLmlkcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLm1hcChpZGVudGlmeSkpO1xuICAgICAgICBjb25zdCBhbmNob3IgPSBpZGVudGlmeU51bGwoYXJncy5hbmNob3IpO1xuICAgICAgICBjb25zdCBtb3N0UmVjZW50ID0gaWRlbnRpZnlOdWxsKGFyZ3MubW9zdFJlY2VudCk7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goc2VsZWN0aW9uLnNldCh7IGlkcywgYW5jaG9yLCBtb3N0UmVjZW50IH0pKTtcbiAgICAgICAgc2FmZVJ1bih0aGlzLnByb3BzLm9uU2VsZWN0LCB0aGlzLnNlbGVjdGVkTm9kZXMpO1xuICAgIH1cbiAgICAvKiBEcmFnIGFuZCBEcm9wICovXG4gICAgZ2V0IGN1cnNvclBhcmVudElkKCkge1xuICAgICAgICBjb25zdCB7IGN1cnNvciB9ID0gdGhpcy5zdGF0ZS5kbmQ7XG4gICAgICAgIHN3aXRjaCAoY3Vyc29yLnR5cGUpIHtcbiAgICAgICAgICAgIGNhc2UgXCJoaWdobGlnaHRcIjpcbiAgICAgICAgICAgICAgICByZXR1cm4gY3Vyc29yLmlkO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQgY3Vyc29yT3ZlckZvbGRlcigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUuZG5kLmN1cnNvci50eXBlID09PSBcImhpZ2hsaWdodFwiO1xuICAgIH1cbiAgICBnZXQgZHJhZ05vZGVzKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zdGF0ZS5kbmQuZHJhZ0lkc1xuICAgICAgICAgICAgLm1hcCgoaWQpID0+IHRoaXMuZ2V0KGlkKSlcbiAgICAgICAgICAgIC5maWx0ZXIoKG4pID0+ICEhbik7XG4gICAgfVxuICAgIGdldCBkcmFnTm9kZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0KHRoaXMuc3RhdGUubm9kZXMuZHJhZy5pZCk7XG4gICAgfVxuICAgIGdldCBkcmFnRGVzdGluYXRpb25QYXJlbnQoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmdldCh0aGlzLnN0YXRlLm5vZGVzLmRyYWcuZGVzdGluYXRpb25QYXJlbnRJZCk7XG4gICAgfVxuICAgIGdldCBkcmFnRGVzdGluYXRpb25JbmRleCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUubm9kZXMuZHJhZy5kZXN0aW5hdGlvbkluZGV4O1xuICAgIH1cbiAgICBjYW5Ecm9wKCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGlmICh0aGlzLmlzRmlsdGVyZWQpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IHBhcmVudE5vZGUgPSAoX2EgPSB0aGlzLmdldCh0aGlzLnN0YXRlLmRuZC5wYXJlbnRJZCkpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHRoaXMucm9vdDtcbiAgICAgICAgY29uc3QgZHJhZ05vZGVzID0gdGhpcy5kcmFnTm9kZXM7XG4gICAgICAgIGNvbnN0IGlzRGlzYWJsZWQgPSB0aGlzLnByb3BzLmRpc2FibGVEcm9wO1xuICAgICAgICBmb3IgKGNvbnN0IGRyYWcgb2YgZHJhZ05vZGVzKSB7XG4gICAgICAgICAgICBpZiAoIWRyYWcpXG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgaWYgKCFwYXJlbnROb2RlKVxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIGlmIChkcmFnLmlzSW50ZXJuYWwgJiYgdXRpbHMuaXNEZXNjZW5kYW50KHBhcmVudE5vZGUsIGRyYWcpKVxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICAvLyBBbGxvdyB0aGUgdXNlciB0byBpbnNlcnQgdGhlaXIgb3duIGxvZ2ljXG4gICAgICAgIGlmICh0eXBlb2YgaXNEaXNhYmxlZCA9PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgIHJldHVybiAhaXNEaXNhYmxlZCh7XG4gICAgICAgICAgICAgICAgcGFyZW50Tm9kZSxcbiAgICAgICAgICAgICAgICBkcmFnTm9kZXM6IHRoaXMuZHJhZ05vZGVzLFxuICAgICAgICAgICAgICAgIGluZGV4OiB0aGlzLnN0YXRlLmRuZC5pbmRleCB8fCAwLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIGlzRGlzYWJsZWQgPT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICAgICAgcmV0dXJuICFwYXJlbnROb2RlLmRhdGFbaXNEaXNhYmxlZF07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIGlzRGlzYWJsZWQgPT09IFwiYm9vbGVhblwiKSB7XG4gICAgICAgICAgICByZXR1cm4gIWlzRGlzYWJsZWQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBoaWRlQ3Vyc29yKCkge1xuICAgICAgICB0aGlzLmRpc3BhdGNoKGRuZC5jdXJzb3IoeyB0eXBlOiBcIm5vbmVcIiB9KSk7XG4gICAgfVxuICAgIHNob3dDdXJzb3IoY3Vyc29yKSB7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2goZG5kLmN1cnNvcihjdXJzb3IpKTtcbiAgICB9XG4gICAgLyogVmlzaWJpbGl0eSAqL1xuICAgIG9wZW4oaWRlbnRpdHkpIHtcbiAgICAgICAgY29uc3QgaWQgPSBpZGVudGlmeU51bGwoaWRlbnRpdHkpO1xuICAgICAgICBpZiAoIWlkKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBpZiAodGhpcy5pc09wZW4oaWQpKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB0aGlzLmRpc3BhdGNoKHZpc2liaWxpdHkub3BlbihpZCwgdGhpcy5pc0ZpbHRlcmVkKSk7XG4gICAgICAgIHNhZmVSdW4odGhpcy5wcm9wcy5vblRvZ2dsZSwgaWQpO1xuICAgIH1cbiAgICBjbG9zZShpZGVudGl0eSkge1xuICAgICAgICBjb25zdCBpZCA9IGlkZW50aWZ5TnVsbChpZGVudGl0eSk7XG4gICAgICAgIGlmICghaWQpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGlmICghdGhpcy5pc09wZW4oaWQpKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB0aGlzLmRpc3BhdGNoKHZpc2liaWxpdHkuY2xvc2UoaWQsIHRoaXMuaXNGaWx0ZXJlZCkpO1xuICAgICAgICBzYWZlUnVuKHRoaXMucHJvcHMub25Ub2dnbGUsIGlkKTtcbiAgICB9XG4gICAgdG9nZ2xlKGlkZW50aXR5KSB7XG4gICAgICAgIGNvbnN0IGlkID0gaWRlbnRpZnlOdWxsKGlkZW50aXR5KTtcbiAgICAgICAgaWYgKCFpZClcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgcmV0dXJuIHRoaXMuaXNPcGVuKGlkKSA/IHRoaXMuY2xvc2UoaWQpIDogdGhpcy5vcGVuKGlkKTtcbiAgICB9XG4gICAgb3BlblBhcmVudHMoaWRlbnRpdHkpIHtcbiAgICAgICAgY29uc3QgaWQgPSBpZGVudGlmeU51bGwoaWRlbnRpdHkpO1xuICAgICAgICBpZiAoIWlkKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBub2RlID0gdXRpbHMuZGZzKHRoaXMucm9vdCwgaWQpO1xuICAgICAgICBsZXQgcGFyZW50ID0gbm9kZSA9PT0gbnVsbCB8fCBub2RlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBub2RlLnBhcmVudDtcbiAgICAgICAgd2hpbGUgKHBhcmVudCkge1xuICAgICAgICAgICAgdGhpcy5vcGVuKHBhcmVudC5pZCk7XG4gICAgICAgICAgICBwYXJlbnQgPSBwYXJlbnQucGFyZW50O1xuICAgICAgICB9XG4gICAgfVxuICAgIG9wZW5TaWJsaW5ncyhub2RlKSB7XG4gICAgICAgIGNvbnN0IHBhcmVudCA9IG5vZGUucGFyZW50O1xuICAgICAgICBpZiAoIXBhcmVudCkge1xuICAgICAgICAgICAgdGhpcy50b2dnbGUobm9kZS5pZCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAocGFyZW50LmNoaWxkcmVuKSB7XG4gICAgICAgICAgICBjb25zdCBpc09wZW4gPSBub2RlLmlzT3BlbjtcbiAgICAgICAgICAgIGZvciAobGV0IHNpYmxpbmcgb2YgcGFyZW50LmNoaWxkcmVuKSB7XG4gICAgICAgICAgICAgICAgaWYgKHNpYmxpbmcuaXNJbnRlcm5hbCkge1xuICAgICAgICAgICAgICAgICAgICBpc09wZW4gPyB0aGlzLmNsb3NlKHNpYmxpbmcuaWQpIDogdGhpcy5vcGVuKHNpYmxpbmcuaWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuc2Nyb2xsVG8odGhpcy5mb2N1c2VkTm9kZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgb3BlbkFsbCgpIHtcbiAgICAgICAgdXRpbHMud2Fsayh0aGlzLnJvb3QsIChub2RlKSA9PiB7XG4gICAgICAgICAgICBpZiAobm9kZS5pc0ludGVybmFsKVxuICAgICAgICAgICAgICAgIG5vZGUub3BlbigpO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgY2xvc2VBbGwoKSB7XG4gICAgICAgIHV0aWxzLndhbGsodGhpcy5yb290LCAobm9kZSkgPT4ge1xuICAgICAgICAgICAgaWYgKG5vZGUuaXNJbnRlcm5hbClcbiAgICAgICAgICAgICAgICBub2RlLmNsb3NlKCk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKiBTY3JvbGxpbmcgKi9cbiAgICBzY3JvbGxUbyhpZGVudGl0eSwgYWxpZ24gPSBcInNtYXJ0XCIpIHtcbiAgICAgICAgaWYgKCFpZGVudGl0eSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3QgaWQgPSBpZGVudGlmeShpZGVudGl0eSk7XG4gICAgICAgIHRoaXMub3BlblBhcmVudHMoaWQpO1xuICAgICAgICByZXR1cm4gdXRpbHNcbiAgICAgICAgICAgIC53YWl0Rm9yKCgpID0+IGlkIGluIHRoaXMuaWRUb0luZGV4KVxuICAgICAgICAgICAgLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmlkVG9JbmRleFtpZF07XG4gICAgICAgICAgICBpZiAoaW5kZXggPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAoX2EgPSB0aGlzLmxpc3QuY3VycmVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnNjcm9sbFRvSXRlbShpbmRleCwgYWxpZ24pO1xuICAgICAgICB9KVxuICAgICAgICAgICAgLmNhdGNoKCgpID0+IHtcbiAgICAgICAgICAgIC8vIElkOiAke2lkfSBuZXZlciBhcHBlYXJlZCBpbiB0aGUgbGlzdC5cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qIFN0YXRlIENoZWNrcyAqL1xuICAgIGdldCBpc0VkaXRpbmcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLm5vZGVzLmVkaXQuaWQgIT09IG51bGw7XG4gICAgfVxuICAgIGdldCBpc0ZpbHRlcmVkKCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHJldHVybiAhISgoX2EgPSB0aGlzLnByb3BzLnNlYXJjaFRlcm0pID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS50cmltKCkpO1xuICAgIH1cbiAgICBnZXQgaGFzRm9jdXMoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLm5vZGVzLmZvY3VzLnRyZWVGb2N1c2VkO1xuICAgIH1cbiAgICBnZXQgaGFzTm9TZWxlY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLm5vZGVzLnNlbGVjdGlvbi5pZHMuc2l6ZSA9PT0gMDtcbiAgICB9XG4gICAgZ2V0IGhhc09uZVNlbGVjdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUubm9kZXMuc2VsZWN0aW9uLmlkcy5zaXplID09PSAxO1xuICAgIH1cbiAgICBnZXQgaGFzTXVsdGlwbGVTZWxlY3Rpb25zKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5zdGF0ZS5ub2Rlcy5zZWxlY3Rpb24uaWRzLnNpemUgPiAxO1xuICAgIH1cbiAgICBpc1NlbGVjdGVkKGlkKSB7XG4gICAgICAgIGlmICghaWQpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIHJldHVybiB0aGlzLnN0YXRlLm5vZGVzLnNlbGVjdGlvbi5pZHMuaGFzKGlkKTtcbiAgICB9XG4gICAgaXNPcGVuKGlkKSB7XG4gICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICBpZiAoIWlkKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICBpZiAoaWQgPT09IFJPT1RfSUQpXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgY29uc3QgZGVmID0gKF9hID0gdGhpcy5wcm9wcy5vcGVuQnlEZWZhdWx0KSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiB0cnVlO1xuICAgICAgICBpZiAodGhpcy5pc0ZpbHRlcmVkKSB7XG4gICAgICAgICAgICByZXR1cm4gKF9iID0gdGhpcy5zdGF0ZS5ub2Rlcy5vcGVuLmZpbHRlcmVkW2lkXSkgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogdHJ1ZTsgLy8gRmlsdGVyZWQgZm9sZGVycyBhcmUgYWx3YXlzIG9wZW5lZCBieSBkZWZhdWx0XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gKF9jID0gdGhpcy5zdGF0ZS5ub2Rlcy5vcGVuLnVuZmlsdGVyZWRbaWRdKSAhPT0gbnVsbCAmJiBfYyAhPT0gdm9pZCAwID8gX2MgOiBkZWY7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaXNFZGl0YWJsZShkYXRhKSB7XG4gICAgICAgIGNvbnN0IGNoZWNrID0gdGhpcy5wcm9wcy5kaXNhYmxlRWRpdCB8fCAoKCkgPT4gZmFsc2UpO1xuICAgICAgICByZXR1cm4gIXV0aWxzLmFjY2VzcyhkYXRhLCBjaGVjayk7XG4gICAgfVxuICAgIGlzRHJhZ2dhYmxlKGRhdGEpIHtcbiAgICAgICAgY29uc3QgY2hlY2sgPSB0aGlzLnByb3BzLmRpc2FibGVEcmFnIHx8ICgoKSA9PiBmYWxzZSk7XG4gICAgICAgIHJldHVybiAhdXRpbHMuYWNjZXNzKGRhdGEsIGNoZWNrKTtcbiAgICB9XG4gICAgaXNEcmFnZ2luZyhub2RlKSB7XG4gICAgICAgIGNvbnN0IGlkID0gaWRlbnRpZnlOdWxsKG5vZGUpO1xuICAgICAgICBpZiAoIWlkKVxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICByZXR1cm4gdGhpcy5zdGF0ZS5ub2Rlcy5kcmFnLmlkID09PSBpZDtcbiAgICB9XG4gICAgaXNGb2N1c2VkKGlkKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmhhc0ZvY3VzICYmIHRoaXMuc3RhdGUubm9kZXMuZm9jdXMuaWQgPT09IGlkO1xuICAgIH1cbiAgICBpc01hdGNoKG5vZGUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubWF0Y2hGbihub2RlKTtcbiAgICB9XG4gICAgd2lsbFJlY2VpdmVEcm9wKG5vZGUpIHtcbiAgICAgICAgY29uc3QgaWQgPSBpZGVudGlmeU51bGwobm9kZSk7XG4gICAgICAgIGlmICghaWQpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IHsgZGVzdGluYXRpb25QYXJlbnRJZCwgZGVzdGluYXRpb25JbmRleCB9ID0gdGhpcy5zdGF0ZS5ub2Rlcy5kcmFnO1xuICAgICAgICByZXR1cm4gaWQgPT09IGRlc3RpbmF0aW9uUGFyZW50SWQgJiYgZGVzdGluYXRpb25JbmRleCA9PT0gbnVsbDtcbiAgICB9XG4gICAgLyogVHJlZSBFdmVudCBIYW5kbGVycyAqL1xuICAgIG9uRm9jdXMoKSB7XG4gICAgICAgIGNvbnN0IG5vZGUgPSB0aGlzLmZvY3VzZWROb2RlIHx8IHRoaXMuZmlyc3ROb2RlO1xuICAgICAgICBpZiAobm9kZSlcbiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2goZm9jdXMobm9kZS5pZCkpO1xuICAgIH1cbiAgICBvbkJsdXIoKSB7XG4gICAgICAgIHRoaXMuZGlzcGF0Y2godHJlZUJsdXIoKSk7XG4gICAgfVxuICAgIG9uSXRlbXNSZW5kZXJlZChhcmdzKSB7XG4gICAgICAgIHRoaXMudmlzaWJsZVN0YXJ0SW5kZXggPSBhcmdzLnZpc2libGVTdGFydEluZGV4O1xuICAgICAgICB0aGlzLnZpc2libGVTdG9wSW5kZXggPSBhcmdzLnZpc2libGVTdG9wSW5kZXg7XG4gICAgfVxuICAgIC8qIEdldCBSZW5kZXJlcnMgKi9cbiAgICBnZXQgcmVuZGVyQ29udGFpbmVyKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wcm9wcy5yZW5kZXJDb250YWluZXIgfHwgRGVmYXVsdENvbnRhaW5lcjtcbiAgICB9XG4gICAgZ2V0IHJlbmRlclJvdygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucHJvcHMucmVuZGVyUm93IHx8IERlZmF1bHRSb3c7XG4gICAgfVxuICAgIGdldCByZW5kZXJOb2RlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wcm9wcy5jaGlsZHJlbiB8fCBEZWZhdWx0Tm9kZTtcbiAgICB9XG4gICAgZ2V0IHJlbmRlckRyYWdQcmV2aWV3KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5wcm9wcy5yZW5kZXJEcmFnUHJldmlldyB8fCBEZWZhdWx0RHJhZ1ByZXZpZXc7XG4gICAgfVxuICAgIGdldCByZW5kZXJDdXJzb3IoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnByb3BzLnJlbmRlckN1cnNvciB8fCBEZWZhdWx0Q3Vyc29yO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6WyJfX2F3YWl0ZXIiLCJ0aGlzQXJnIiwiX2FyZ3VtZW50cyIsIlAiLCJnZW5lcmF0b3IiLCJhZG9wdCIsInZhbHVlIiwicmVzb2x2ZSIsIlByb21pc2UiLCJyZWplY3QiLCJmdWxmaWxsZWQiLCJzdGVwIiwibmV4dCIsImUiLCJyZWplY3RlZCIsInJlc3VsdCIsImRvbmUiLCJ0aGVuIiwiYXBwbHkiLCJ1dGlscyIsIkRlZmF1bHRDdXJzb3IiLCJEZWZhdWx0Um93IiwiRGVmYXVsdE5vZGUiLCJlZGl0IiwiZm9jdXMiLCJ0cmVlQmx1ciIsImNyZWF0ZVJvb3QiLCJST09UX0lEIiwiYWN0aW9ucyIsInZpc2liaWxpdHkiLCJzZWxlY3Rpb24iLCJkbmQiLCJEZWZhdWx0RHJhZ1ByZXZpZXciLCJEZWZhdWx0Q29udGFpbmVyIiwiY3JlYXRlTGlzdCIsImNyZWF0ZUluZGV4Iiwic2FmZVJ1biIsImlkZW50aWZ5IiwiaWRlbnRpZnlOdWxsIiwiVHJlZUFwaSIsImNvbnN0cnVjdG9yIiwic3RvcmUiLCJwcm9wcyIsImxpc3QiLCJsaXN0RWwiLCJ2aXNpYmxlU3RhcnRJbmRleCIsInZpc2libGVTdG9wSW5kZXgiLCJyb290IiwidmlzaWJsZU5vZGVzIiwiaWRUb0luZGV4IiwidXBkYXRlIiwiZGlzcGF0Y2giLCJhY3Rpb24iLCJzdGF0ZSIsImdldFN0YXRlIiwib3BlblN0YXRlIiwibm9kZXMiLCJvcGVuIiwidW5maWx0ZXJlZCIsIndpZHRoIiwiX2EiLCJoZWlnaHQiLCJpbmRlbnQiLCJyb3dIZWlnaHQiLCJvdmVyc2NhbkNvdW50Iiwic2VhcmNoVGVybSIsInRyaW0iLCJtYXRjaEZuIiwibWF0Y2giLCJzZWFyY2hNYXRjaCIsIm5vZGUiLCJ0ZXJtIiwic3RyaW5nIiwiSlNPTiIsInN0cmluZ2lmeSIsIk9iamVjdCIsInZhbHVlcyIsImRhdGEiLCJ0b0xvY2FsZUxvd2VyQ2FzZSIsImluY2x1ZGVzIiwiYWNjZXNzQ2hpbGRyZW4iLCJnZXQiLCJjaGlsZHJlbkFjY2Vzc29yIiwiYWNjZXNzIiwiYWNjZXNzSWQiLCJpZEFjY2Vzc29yIiwiaWQiLCJFcnJvciIsImZpcnN0Tm9kZSIsImxhc3ROb2RlIiwibGVuZ3RoIiwiZm9jdXNlZE5vZGUiLCJtb3N0UmVjZW50Tm9kZSIsIm1vc3RSZWNlbnQiLCJuZXh0Tm9kZSIsImluZGV4IiwiaW5kZXhPZiIsImF0IiwicHJldk5vZGUiLCJub2Rlc0JldHdlZW4iLCJzdGFydElkIiwiZW5kSWQiLCJpbmRleDEiLCJpbmRleDIiLCJzdGFydCIsIk1hdGgiLCJtaW4iLCJlbmQiLCJtYXgiLCJzbGljZSIsImtleSIsImVkaXRpbmdJZCIsImNyZWF0ZUludGVybmFsIiwiY3JlYXRlIiwidHlwZSIsImNyZWF0ZUxlYWYiLCJhcmd1bWVudHMiLCJvcHRzIiwiX2IiLCJwYXJlbnRJZCIsInVuZGVmaW5lZCIsImdldEluc2VydFBhcmVudElkIiwiZ2V0SW5zZXJ0SW5kZXgiLCJvbkNyZWF0ZSIsInBhcmVudE5vZGUiLCJzZXRUaW1lb3V0Iiwic2VsZWN0IiwiYWN0aXZhdGUiLCJkZWxldGUiLCJpZGVudHMiLCJBcnJheSIsImlzQXJyYXkiLCJpZHMiLCJtYXAiLCJmaWx0ZXIiLCJuIiwib25EZWxldGUiLCJyZXNvbHZlRWRpdCIsImNhbmNlbGxlZCIsInNjcm9sbFRvIiwiZWRpdFByb21pc2UiLCJzdWJtaXQiLCJpZGVudGl0eSIsIm9uUmVuYW1lIiwibmFtZSIsIm9uRm9jdXMiLCJyZXNldCIsIm9uQWN0aXZhdGUiLCJzZWxlY3RlZElkcyIsInNlbGVjdGVkTm9kZXMiLCJmcm9tIiwicHVzaCIsInNlbGVjdGlvbkZvbGxvd3NGb2N1cyIsInNjcm9sbCIsInBhZ2VVcCIsInN0b3AiLCJwYWdlIiwicm93SW5kZXgiLCJwYWdlRG93biIsImNoYW5nZUZvY3VzIiwib25seSIsImFuY2hvciIsImFsaWduIiwib25TZWxlY3QiLCJkZXNlbGVjdCIsInJlbW92ZSIsInNlbGVjdE11bHRpIiwiYWRkIiwic2VsZWN0Q29udGlndW91cyIsImRlc2VsZWN0QWxsIiwic2V0U2VsZWN0aW9uIiwic2VsZWN0QWxsIiwia2V5cyIsImFyZ3MiLCJTZXQiLCJzZXQiLCJjdXJzb3JQYXJlbnRJZCIsImN1cnNvciIsImN1cnNvck92ZXJGb2xkZXIiLCJkcmFnTm9kZXMiLCJkcmFnSWRzIiwiZHJhZ05vZGUiLCJkcmFnIiwiZHJhZ0Rlc3RpbmF0aW9uUGFyZW50IiwiZGVzdGluYXRpb25QYXJlbnRJZCIsImRyYWdEZXN0aW5hdGlvbkluZGV4IiwiZGVzdGluYXRpb25JbmRleCIsImNhbkRyb3AiLCJpc0ZpbHRlcmVkIiwiaXNEaXNhYmxlZCIsImRpc2FibGVEcm9wIiwiaXNJbnRlcm5hbCIsImlzRGVzY2VuZGFudCIsImhpZGVDdXJzb3IiLCJzaG93Q3Vyc29yIiwiaXNPcGVuIiwib25Ub2dnbGUiLCJjbG9zZSIsInRvZ2dsZSIsIm9wZW5QYXJlbnRzIiwiZGZzIiwicGFyZW50Iiwib3BlblNpYmxpbmdzIiwiY2hpbGRyZW4iLCJzaWJsaW5nIiwib3BlbkFsbCIsIndhbGsiLCJjbG9zZUFsbCIsIndhaXRGb3IiLCJjdXJyZW50Iiwic2Nyb2xsVG9JdGVtIiwiY2F0Y2giLCJpc0VkaXRpbmciLCJoYXNGb2N1cyIsInRyZWVGb2N1c2VkIiwiaGFzTm9TZWxlY3Rpb24iLCJzaXplIiwiaGFzT25lU2VsZWN0aW9uIiwiaGFzTXVsdGlwbGVTZWxlY3Rpb25zIiwiaXNTZWxlY3RlZCIsImhhcyIsIl9jIiwiZGVmIiwib3BlbkJ5RGVmYXVsdCIsImZpbHRlcmVkIiwiaXNFZGl0YWJsZSIsImNoZWNrIiwiZGlzYWJsZUVkaXQiLCJpc0RyYWdnYWJsZSIsImRpc2FibGVEcmFnIiwiaXNEcmFnZ2luZyIsImlzRm9jdXNlZCIsImlzTWF0Y2giLCJ3aWxsUmVjZWl2ZURyb3AiLCJvbkJsdXIiLCJvbkl0ZW1zUmVuZGVyZWQiLCJyZW5kZXJDb250YWluZXIiLCJyZW5kZXJSb3ciLCJyZW5kZXJOb2RlIiwicmVuZGVyRHJhZ1ByZXZpZXciLCJyZW5kZXJDdXJzb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/interfaces/tree-api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js":
/*!********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/dnd-slice.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actions: () => (/* binding */ actions),\n/* harmony export */   reducer: () => (/* binding */ reducer)\n/* harmony export */ });\n/* harmony import */ var _initial__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./initial */ \"(ssr)/./node_modules/react-arborist/dist/module/state/initial.js\");\n\n/* Actions */ const actions = {\n    cursor (cursor) {\n        return {\n            type: \"DND_CURSOR\",\n            cursor\n        };\n    },\n    dragStart (id, dragIds) {\n        return {\n            type: \"DND_DRAG_START\",\n            id,\n            dragIds\n        };\n    },\n    dragEnd () {\n        return {\n            type: \"DND_DRAG_END\"\n        };\n    },\n    hovering (parentId, index) {\n        return {\n            type: \"DND_HOVERING\",\n            parentId,\n            index\n        };\n    }\n};\n/* Reducer */ function reducer(state = (0,_initial__WEBPACK_IMPORTED_MODULE_0__.initialState)()[\"dnd\"], action) {\n    switch(action.type){\n        case \"DND_CURSOR\":\n            return Object.assign(Object.assign({}, state), {\n                cursor: action.cursor\n            });\n        case \"DND_DRAG_START\":\n            return Object.assign(Object.assign({}, state), {\n                dragId: action.id,\n                dragIds: action.dragIds\n            });\n        case \"DND_DRAG_END\":\n            return (0,_initial__WEBPACK_IMPORTED_MODULE_0__.initialState)()[\"dnd\"];\n        case \"DND_HOVERING\":\n            return Object.assign(Object.assign({}, state), {\n                parentId: action.parentId,\n                index: action.index\n            });\n        default:\n            return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/drag-slice.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/drag-slice.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer)\n/* harmony export */ });\n/* harmony import */ var _initial__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./initial */ \"(ssr)/./node_modules/react-arborist/dist/module/state/initial.js\");\n\n/* Reducer */ function reducer(state = (0,_initial__WEBPACK_IMPORTED_MODULE_0__.initialState)().nodes.drag, action) {\n    switch(action.type){\n        case \"DND_DRAG_START\":\n            return Object.assign(Object.assign({}, state), {\n                id: action.id,\n                selectedIds: action.dragIds\n            });\n        case \"DND_DRAG_END\":\n            return Object.assign(Object.assign({}, state), {\n                id: null,\n                destinationParentId: null,\n                destinationIndex: null,\n                selectedIds: []\n            });\n        case \"DND_HOVERING\":\n            if (action.parentId !== state.destinationParentId || action.index != state.destinationIndex) {\n                return Object.assign(Object.assign({}, state), {\n                    destinationParentId: action.parentId,\n                    destinationIndex: action.index\n                });\n            } else {\n                return state;\n            }\n        default:\n            return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/drag-slice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/edit-slice.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/edit-slice.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   edit: () => (/* binding */ edit),\n/* harmony export */   reducer: () => (/* binding */ reducer)\n/* harmony export */ });\n/* Actions */ function edit(id) {\n    return {\n        type: \"EDIT\",\n        id\n    };\n}\n/* Reducer */ function reducer(state = {\n    id: null\n}, action) {\n    if (action.type === \"EDIT\") {\n        return Object.assign(Object.assign({}, state), {\n            id: action.id\n        });\n    } else {\n        return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvc3RhdGUvZWRpdC1zbGljZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLFdBQVcsR0FDSixTQUFTQSxLQUFLQyxFQUFFO0lBQ25CLE9BQU87UUFBRUMsTUFBTTtRQUFRRDtJQUFHO0FBQzlCO0FBQ0EsV0FBVyxHQUNKLFNBQVNFLFFBQVFDLFFBQVE7SUFBRUgsSUFBSTtBQUFLLENBQUMsRUFBRUksTUFBTTtJQUNoRCxJQUFJQSxPQUFPSCxJQUFJLEtBQUssUUFBUTtRQUN4QixPQUFPSSxPQUFPQyxNQUFNLENBQUNELE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdILFFBQVE7WUFBRUgsSUFBSUksT0FBT0osRUFBRTtRQUFDO0lBQ25FLE9BQ0s7UUFDRCxPQUFPRztJQUNYO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvc3RhdGUvZWRpdC1zbGljZS5qcz85NTFmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIEFjdGlvbnMgKi9cbmV4cG9ydCBmdW5jdGlvbiBlZGl0KGlkKSB7XG4gICAgcmV0dXJuIHsgdHlwZTogXCJFRElUXCIsIGlkIH07XG59XG4vKiBSZWR1Y2VyICovXG5leHBvcnQgZnVuY3Rpb24gcmVkdWNlcihzdGF0ZSA9IHsgaWQ6IG51bGwgfSwgYWN0aW9uKSB7XG4gICAgaWYgKGFjdGlvbi50eXBlID09PSBcIkVESVRcIikge1xuICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSksIHsgaWQ6IGFjdGlvbi5pZCB9KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9XG59XG4iXSwibmFtZXMiOlsiZWRpdCIsImlkIiwidHlwZSIsInJlZHVjZXIiLCJzdGF0ZSIsImFjdGlvbiIsIk9iamVjdCIsImFzc2lnbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/edit-slice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/focus-slice.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/focus-slice.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focus: () => (/* binding */ focus),\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   treeBlur: () => (/* binding */ treeBlur)\n/* harmony export */ });\n/* Types */ /* Actions */ function focus(id) {\n    return {\n        type: \"FOCUS\",\n        id\n    };\n}\nfunction treeBlur() {\n    return {\n        type: \"TREE_BLUR\"\n    };\n}\n/* Reducer */ function reducer(state = {\n    id: null,\n    treeFocused: false\n}, action) {\n    if (action.type === \"FOCUS\") {\n        return Object.assign(Object.assign({}, state), {\n            id: action.id,\n            treeFocused: true\n        });\n    } else if (action.type === \"TREE_BLUR\") {\n        return Object.assign(Object.assign({}, state), {\n            treeFocused: false\n        });\n    } else {\n        return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvc3RhdGUvZm9jdXMtc2xpY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsU0FBUyxHQUNULFdBQVcsR0FDSixTQUFTQSxNQUFNQyxFQUFFO0lBQ3BCLE9BQU87UUFBRUMsTUFBTTtRQUFTRDtJQUFHO0FBQy9CO0FBQ08sU0FBU0U7SUFDWixPQUFPO1FBQUVELE1BQU07SUFBWTtBQUMvQjtBQUNBLFdBQVcsR0FDSixTQUFTRSxRQUFRQyxRQUFRO0lBQUVKLElBQUk7SUFBTUssYUFBYTtBQUFNLENBQUMsRUFBRUMsTUFBTTtJQUNwRSxJQUFJQSxPQUFPTCxJQUFJLEtBQUssU0FBUztRQUN6QixPQUFPTSxPQUFPQyxNQUFNLENBQUNELE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdKLFFBQVE7WUFBRUosSUFBSU0sT0FBT04sRUFBRTtZQUFFSyxhQUFhO1FBQUs7SUFDdEYsT0FDSyxJQUFJQyxPQUFPTCxJQUFJLEtBQUssYUFBYTtRQUNsQyxPQUFPTSxPQUFPQyxNQUFNLENBQUNELE9BQU9DLE1BQU0sQ0FBQyxDQUFDLEdBQUdKLFFBQVE7WUFBRUMsYUFBYTtRQUFNO0lBQ3hFLE9BQ0s7UUFDRCxPQUFPRDtJQUNYO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvc3RhdGUvZm9jdXMtc2xpY2UuanM/OTU0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBUeXBlcyAqL1xuLyogQWN0aW9ucyAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvY3VzKGlkKSB7XG4gICAgcmV0dXJuIHsgdHlwZTogXCJGT0NVU1wiLCBpZCB9O1xufVxuZXhwb3J0IGZ1bmN0aW9uIHRyZWVCbHVyKCkge1xuICAgIHJldHVybiB7IHR5cGU6IFwiVFJFRV9CTFVSXCIgfTtcbn1cbi8qIFJlZHVjZXIgKi9cbmV4cG9ydCBmdW5jdGlvbiByZWR1Y2VyKHN0YXRlID0geyBpZDogbnVsbCwgdHJlZUZvY3VzZWQ6IGZhbHNlIH0sIGFjdGlvbikge1xuICAgIGlmIChhY3Rpb24udHlwZSA9PT0gXCJGT0NVU1wiKSB7XG4gICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHN0YXRlKSwgeyBpZDogYWN0aW9uLmlkLCB0cmVlRm9jdXNlZDogdHJ1ZSB9KTtcbiAgICB9XG4gICAgZWxzZSBpZiAoYWN0aW9uLnR5cGUgPT09IFwiVFJFRV9CTFVSXCIpIHtcbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgc3RhdGUpLCB7IHRyZWVGb2N1c2VkOiBmYWxzZSB9KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBzdGF0ZTtcbiAgICB9XG59XG4iXSwibmFtZXMiOlsiZm9jdXMiLCJpZCIsInR5cGUiLCJ0cmVlQmx1ciIsInJlZHVjZXIiLCJzdGF0ZSIsInRyZWVGb2N1c2VkIiwiYWN0aW9uIiwiT2JqZWN0IiwiYXNzaWduIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/focus-slice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/initial.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/initial.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initialState: () => (/* binding */ initialState)\n/* harmony export */ });\nconst initialState = (props)=>{\n    var _a;\n    return {\n        nodes: {\n            // Changes together\n            open: {\n                filtered: {},\n                unfiltered: (_a = props === null || props === void 0 ? void 0 : props.initialOpenState) !== null && _a !== void 0 ? _a : {}\n            },\n            focus: {\n                id: null,\n                treeFocused: false\n            },\n            edit: {\n                id: null\n            },\n            drag: {\n                id: null,\n                selectedIds: [],\n                destinationParentId: null,\n                destinationIndex: null\n            },\n            selection: {\n                ids: new Set(),\n                anchor: null,\n                mostRecent: null\n            }\n        },\n        dnd: {\n            cursor: {\n                type: \"none\"\n            },\n            dragId: null,\n            dragIds: [],\n            parentId: null,\n            index: -1\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/initial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/open-slice.js":
/*!*********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/open-slice.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actions: () => (/* binding */ actions),\n/* harmony export */   reducer: () => (/* binding */ reducer)\n/* harmony export */ });\n/* Actions */ const actions = {\n    open (id, filtered) {\n        return {\n            type: \"VISIBILITY_OPEN\",\n            id,\n            filtered\n        };\n    },\n    close (id, filtered) {\n        return {\n            type: \"VISIBILITY_CLOSE\",\n            id,\n            filtered\n        };\n    },\n    toggle (id, filtered) {\n        return {\n            type: \"VISIBILITY_TOGGLE\",\n            id,\n            filtered\n        };\n    },\n    clear (filtered) {\n        return {\n            type: \"VISIBILITY_CLEAR\",\n            filtered\n        };\n    }\n};\n/* Reducer */ function openMapReducer(state = {}, action) {\n    if (action.type === \"VISIBILITY_OPEN\") {\n        return Object.assign(Object.assign({}, state), {\n            [action.id]: true\n        });\n    } else if (action.type === \"VISIBILITY_CLOSE\") {\n        return Object.assign(Object.assign({}, state), {\n            [action.id]: false\n        });\n    } else if (action.type === \"VISIBILITY_TOGGLE\") {\n        const prev = state[action.id];\n        return Object.assign(Object.assign({}, state), {\n            [action.id]: !prev\n        });\n    } else if (action.type === \"VISIBILITY_CLEAR\") {\n        return {};\n    } else {\n        return state;\n    }\n}\nfunction reducer(state = {\n    filtered: {},\n    unfiltered: {}\n}, action) {\n    if (!action.type.startsWith(\"VISIBILITY\")) return state;\n    if (action.filtered) {\n        return Object.assign(Object.assign({}, state), {\n            filtered: openMapReducer(state.filtered, action)\n        });\n    } else {\n        return Object.assign(Object.assign({}, state), {\n            unfiltered: openMapReducer(state.unfiltered, action)\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/open-slice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/root-reducer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/root-reducer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rootReducer: () => (/* binding */ rootReducer)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ \"(ssr)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var _focus_slice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focus-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/focus-slice.js\");\n/* harmony import */ var _edit_slice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/edit-slice.js\");\n/* harmony import */ var _dnd_slice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./dnd-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/dnd-slice.js\");\n/* harmony import */ var _selection_slice__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./selection-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/selection-slice.js\");\n/* harmony import */ var _open_slice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./open-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/open-slice.js\");\n/* harmony import */ var _drag_slice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./drag-slice */ \"(ssr)/./node_modules/react-arborist/dist/module/state/drag-slice.js\");\n\n\n\n\n\n\n\nconst rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)({\n    nodes: (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)({\n        focus: _focus_slice__WEBPACK_IMPORTED_MODULE_1__.reducer,\n        edit: _edit_slice__WEBPACK_IMPORTED_MODULE_2__.reducer,\n        open: _open_slice__WEBPACK_IMPORTED_MODULE_3__.reducer,\n        selection: _selection_slice__WEBPACK_IMPORTED_MODULE_4__.reducer,\n        drag: _drag_slice__WEBPACK_IMPORTED_MODULE_5__.reducer\n    }),\n    dnd: _dnd_slice__WEBPACK_IMPORTED_MODULE_6__.reducer\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvc3RhdGUvcm9vdC1yZWR1Y2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ1M7QUFDRjtBQUNGO0FBQ1k7QUFDVjtBQUNBO0FBQ3hDLE1BQU1RLGNBQWNSLHNEQUFlQSxDQUFDO0lBQ3ZDUyxPQUFPVCxzREFBZUEsQ0FBQztRQUNuQkUsS0FBS0EsbURBQUFBO1FBQ0xDLElBQUlBLGtEQUFBQTtRQUNKRyxJQUFJQSxrREFBQUE7UUFDSkQsU0FBU0EsdURBQUFBO1FBQ1RFLElBQUlBLGtEQUFBQTtJQUNSO0lBQ0FILEdBQUdBLGlEQUFBQTtBQUNQLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmVhY3QtYXJib3Jpc3QvZGlzdC9tb2R1bGUvc3RhdGUvcm9vdC1yZWR1Y2VyLmpzPzhlMGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29tYmluZVJlZHVjZXJzIH0gZnJvbSBcInJlZHV4XCI7XG5pbXBvcnQgeyByZWR1Y2VyIGFzIGZvY3VzIH0gZnJvbSBcIi4vZm9jdXMtc2xpY2VcIjtcbmltcG9ydCB7IHJlZHVjZXIgYXMgZWRpdCB9IGZyb20gXCIuL2VkaXQtc2xpY2VcIjtcbmltcG9ydCB7IHJlZHVjZXIgYXMgZG5kIH0gZnJvbSBcIi4vZG5kLXNsaWNlXCI7XG5pbXBvcnQgeyByZWR1Y2VyIGFzIHNlbGVjdGlvbiB9IGZyb20gXCIuL3NlbGVjdGlvbi1zbGljZVwiO1xuaW1wb3J0IHsgcmVkdWNlciBhcyBvcGVuIH0gZnJvbSBcIi4vb3Blbi1zbGljZVwiO1xuaW1wb3J0IHsgcmVkdWNlciBhcyBkcmFnIH0gZnJvbSBcIi4vZHJhZy1zbGljZVwiO1xuZXhwb3J0IGNvbnN0IHJvb3RSZWR1Y2VyID0gY29tYmluZVJlZHVjZXJzKHtcbiAgICBub2RlczogY29tYmluZVJlZHVjZXJzKHtcbiAgICAgICAgZm9jdXMsXG4gICAgICAgIGVkaXQsXG4gICAgICAgIG9wZW4sXG4gICAgICAgIHNlbGVjdGlvbixcbiAgICAgICAgZHJhZyxcbiAgICB9KSxcbiAgICBkbmQsXG59KTtcbiJdLCJuYW1lcyI6WyJjb21iaW5lUmVkdWNlcnMiLCJyZWR1Y2VyIiwiZm9jdXMiLCJlZGl0IiwiZG5kIiwic2VsZWN0aW9uIiwib3BlbiIsImRyYWciLCJyb290UmVkdWNlciIsIm5vZGVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/root-reducer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/state/selection-slice.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/state/selection-slice.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actions: () => (/* binding */ actions),\n/* harmony export */   reducer: () => (/* binding */ reducer)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/react-arborist/dist/module/utils.js\");\n/* harmony import */ var _initial__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./initial */ \"(ssr)/./node_modules/react-arborist/dist/module/state/initial.js\");\n\n\n/* Actions */ const actions = {\n    clear: ()=>({\n            type: \"SELECTION_CLEAR\"\n        }),\n    only: (id)=>({\n            type: \"SELECTION_ONLY\",\n            id: (0,_utils__WEBPACK_IMPORTED_MODULE_0__.identify)(id)\n        }),\n    add: (id)=>({\n            type: \"SELECTION_ADD\",\n            ids: (Array.isArray(id) ? id : [\n                id\n            ]).map(_utils__WEBPACK_IMPORTED_MODULE_0__.identify)\n        }),\n    remove: (id)=>({\n            type: \"SELECTION_REMOVE\",\n            ids: (Array.isArray(id) ? id : [\n                id\n            ]).map(_utils__WEBPACK_IMPORTED_MODULE_0__.identify)\n        }),\n    set: (args)=>Object.assign({\n            type: \"SELECTION_SET\"\n        }, args),\n    mostRecent: (id)=>({\n            type: \"SELECTION_MOST_RECENT\",\n            id: id === null ? null : (0,_utils__WEBPACK_IMPORTED_MODULE_0__.identify)(id)\n        }),\n    anchor: (id)=>({\n            type: \"SELECTION_ANCHOR\",\n            id: id === null ? null : (0,_utils__WEBPACK_IMPORTED_MODULE_0__.identify)(id)\n        })\n};\n/* Reducer */ function reducer(state = (0,_initial__WEBPACK_IMPORTED_MODULE_1__.initialState)()[\"nodes\"][\"selection\"], action) {\n    const ids = state.ids;\n    switch(action.type){\n        case \"SELECTION_CLEAR\":\n            return Object.assign(Object.assign({}, state), {\n                ids: new Set()\n            });\n        case \"SELECTION_ONLY\":\n            return Object.assign(Object.assign({}, state), {\n                ids: new Set([\n                    action.id\n                ])\n            });\n        case \"SELECTION_ADD\":\n            if (action.ids.length === 0) return state;\n            action.ids.forEach((id)=>ids.add(id));\n            return Object.assign(Object.assign({}, state), {\n                ids: new Set(ids)\n            });\n        case \"SELECTION_REMOVE\":\n            if (action.ids.length === 0) return state;\n            action.ids.forEach((id)=>ids.delete(id));\n            return Object.assign(Object.assign({}, state), {\n                ids: new Set(ids)\n            });\n        case \"SELECTION_SET\":\n            return Object.assign(Object.assign({}, state), {\n                ids: action.ids,\n                mostRecent: action.mostRecent,\n                anchor: action.anchor\n            });\n        case \"SELECTION_MOST_RECENT\":\n            return Object.assign(Object.assign({}, state), {\n                mostRecent: action.id\n            });\n        case \"SELECTION_ANCHOR\":\n            return Object.assign(Object.assign({}, state), {\n                anchor: action.id\n            });\n        default:\n            return state;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/state/selection-slice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-arborist/dist/module/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/react-arborist/dist/module/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   access: () => (/* binding */ access),\n/* harmony export */   bound: () => (/* binding */ bound),\n/* harmony export */   dfs: () => (/* binding */ dfs),\n/* harmony export */   focusNextElement: () => (/* binding */ focusNextElement),\n/* harmony export */   focusPrevElement: () => (/* binding */ focusPrevElement),\n/* harmony export */   getInsertIndex: () => (/* binding */ getInsertIndex),\n/* harmony export */   getInsertParentId: () => (/* binding */ getInsertParentId),\n/* harmony export */   identify: () => (/* binding */ identify),\n/* harmony export */   identifyNull: () => (/* binding */ identifyNull),\n/* harmony export */   indexOf: () => (/* binding */ indexOf),\n/* harmony export */   isClosed: () => (/* binding */ isClosed),\n/* harmony export */   isDescendant: () => (/* binding */ isDescendant),\n/* harmony export */   isItem: () => (/* binding */ isItem),\n/* harmony export */   isOpenWithEmptyChildren: () => (/* binding */ isOpenWithEmptyChildren),\n/* harmony export */   mergeRefs: () => (/* binding */ mergeRefs),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   safeRun: () => (/* binding */ safeRun),\n/* harmony export */   waitFor: () => (/* binding */ waitFor),\n/* harmony export */   walk: () => (/* binding */ walk)\n/* harmony export */ });\nfunction bound(n, min, max) {\n    return Math.max(Math.min(n, max), min);\n}\nfunction isItem(node) {\n    return node && node.isLeaf;\n}\nfunction isClosed(node) {\n    return node && node.isInternal && !node.isOpen;\n}\nfunction isOpenWithEmptyChildren(node) {\n    var _a;\n    return node && node.isOpen && !((_a = node.children) === null || _a === void 0 ? void 0 : _a.length);\n}\n/**\n * Is first param a descendant of the second param\n */ const isDescendant = (a, b)=>{\n    let n = a;\n    while(n){\n        if (n.id === b.id) return true;\n        n = n.parent;\n    }\n    return false;\n};\nconst indexOf = (node)=>{\n    if (!node.parent) throw Error(\"Node does not have a parent\");\n    return node.parent.children.findIndex((c)=>c.id === node.id);\n};\nfunction noop() {}\nfunction dfs(node, id) {\n    if (!node) return null;\n    if (node.id === id) return node;\n    if (node.children) {\n        for (let child of node.children){\n            const result = dfs(child, id);\n            if (result) return result;\n        }\n    }\n    return null;\n}\nfunction walk(node, fn) {\n    fn(node);\n    if (node.children) {\n        for (let child of node.children){\n            walk(child, fn);\n        }\n    }\n}\nfunction focusNextElement(target) {\n    const elements = getFocusable(target);\n    let next;\n    for(let i = 0; i < elements.length; ++i){\n        const item = elements[i];\n        if (item === target) {\n            next = nextItem(elements, i);\n            break;\n        }\n    }\n    // @ts-ignore ??\n    next === null || next === void 0 ? void 0 : next.focus();\n}\nfunction focusPrevElement(target) {\n    const elements = getFocusable(target);\n    let next;\n    for(let i = 0; i < elements.length; ++i){\n        const item = elements[i];\n        if (item === target) {\n            next = prevItem(elements, i);\n            break;\n        }\n    }\n    // @ts-ignore\n    next === null || next === void 0 ? void 0 : next.focus();\n}\nfunction nextItem(list, index) {\n    if (index + 1 < list.length) {\n        return list[index + 1];\n    } else {\n        return list[0];\n    }\n}\nfunction prevItem(list, index) {\n    if (index - 1 >= 0) {\n        return list[index - 1];\n    } else {\n        return list[list.length - 1];\n    }\n}\nfunction getFocusable(target) {\n    return Array.from(document.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex=\"-1\"]):not([disabled]), details:not([disabled]), summary:not(:disabled)')).filter((e)=>e === target || !target.contains(e));\n}\nfunction access(obj, accessor) {\n    if (typeof accessor === \"boolean\") return accessor;\n    if (typeof accessor === \"string\") return obj[accessor];\n    return accessor(obj);\n}\nfunction identifyNull(obj) {\n    if (obj === null) return null;\n    else return identify(obj);\n}\nfunction identify(obj) {\n    return typeof obj === \"string\" ? obj : obj.id;\n}\nfunction mergeRefs(...refs) {\n    return (instance)=>{\n        refs.forEach((ref)=>{\n            if (typeof ref === \"function\") {\n                ref(instance);\n            } else if (ref != null) {\n                ref.current = instance;\n            }\n        });\n    };\n}\nfunction safeRun(fn, ...args) {\n    if (fn) return fn(...args);\n}\nfunction waitFor(fn) {\n    return new Promise((resolve, reject)=>{\n        let tries = 0;\n        function check() {\n            tries += 1;\n            if (tries === 100) reject();\n            if (fn()) resolve();\n            else setTimeout(check, 10);\n        }\n        check();\n    });\n}\nfunction getInsertIndex(tree) {\n    var _a, _b;\n    const focus = tree.focusedNode;\n    if (!focus) return (_b = (_a = tree.root.children) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0;\n    if (focus.isOpen) return 0;\n    if (focus.parent) return focus.childIndex + 1;\n    return 0;\n}\nfunction getInsertParentId(tree) {\n    const focus = tree.focusedNode;\n    if (!focus) return null;\n    if (focus.isOpen) return focus.id;\n    if (focus.parent && !focus.parent.isRoot) return focus.parent.id;\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-arborist/dist/module/utils.js\n");

/***/ })

};
;