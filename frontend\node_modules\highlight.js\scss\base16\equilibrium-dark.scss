pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Equilibrium Dark
  Author: <PERSON>: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme equilibrium-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #0c1118  Default Background
base01  #181c22  Lighter Background (Used for status bars, line number and folding marks)
base02  #22262d  Selection Background
base03  #7b776e  Comments, Invisibles, Line Highlighting
base04  #949088  Dark Foreground (Used for status bars)
base05  #afaba2  Default Foreground, Caret, Delimiters, Operators
base06  #cac6bd  Light Foreground (Not often used)
base07  #e7e2d9  Light Background (Not often used)
base08  #f04339  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #df5923  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #bb8801  Classes, Markup Bold, Search Text Background
base0B  #7f8b00  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #00948b  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #008dd1  Functions, Methods, Attribute IDs, Headings
base0E  #6a7fd2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e3488e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #afaba2;
  background: #0c1118
}
.hljs::selection,
.hljs ::selection {
  background-color: #22262d;
  color: #afaba2
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #7b776e -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #7b776e
}
/* base04 - #949088 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #949088
}
/* base05 - #afaba2 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #afaba2
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f04339
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #df5923
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #bb8801
}
.hljs-strong {
  font-weight: bold;
  color: #bb8801
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7f8b00
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #00948b
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #008dd1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #6a7fd2
}
.hljs-emphasis {
  color: #6a7fd2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e3488e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}