"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/state-local";
exports.ids = ["vendor-chunks/state-local"];
exports.modules = {

/***/ "(ssr)/./node_modules/state-local/lib/es/state-local.js":
/*!********************************************************!*\
  !*** ./node_modules/state-local/lib/es/state-local.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread2(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction compose() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(x) {\n        return fns.reduceRight(function(y, f) {\n            return f(y);\n        }, x);\n    };\n}\nfunction curry(fn) {\n    return function curried() {\n        var _this = this;\n        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n            args[_key2] = arguments[_key2];\n        }\n        return args.length >= fn.length ? fn.apply(this, args) : function() {\n            for(var _len3 = arguments.length, nextArgs = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++){\n                nextArgs[_key3] = arguments[_key3];\n            }\n            return curried.apply(_this, [].concat(args, nextArgs));\n        };\n    };\n}\nfunction isObject(value) {\n    return ({}).toString.call(value).includes(\"Object\");\n}\nfunction isEmpty(obj) {\n    return !Object.keys(obj).length;\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction hasOwnProperty(object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n}\nfunction validateChanges(initial, changes) {\n    if (!isObject(changes)) errorHandler(\"changeType\");\n    if (Object.keys(changes).some(function(field) {\n        return !hasOwnProperty(initial, field);\n    })) errorHandler(\"changeField\");\n    return changes;\n}\nfunction validateSelector(selector) {\n    if (!isFunction(selector)) errorHandler(\"selectorType\");\n}\nfunction validateHandler(handler) {\n    if (!(isFunction(handler) || isObject(handler))) errorHandler(\"handlerType\");\n    if (isObject(handler) && Object.values(handler).some(function(_handler) {\n        return !isFunction(_handler);\n    })) errorHandler(\"handlersType\");\n}\nfunction validateInitial(initial) {\n    if (!initial) errorHandler(\"initialIsRequired\");\n    if (!isObject(initial)) errorHandler(\"initialType\");\n    if (isEmpty(initial)) errorHandler(\"initialContent\");\n}\nfunction throwError(errorMessages, type) {\n    throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n    initialIsRequired: \"initial state is required\",\n    initialType: \"initial state should be an object\",\n    initialContent: \"initial state shouldn't be an empty object\",\n    handlerType: \"handler should be an object or a function\",\n    handlersType: \"all handlers should be a functions\",\n    selectorType: \"selector should be a function\",\n    changeType: \"provided value of changes should be an object\",\n    changeField: 'it seams you want to change a field in the state which is not specified in the \"initial\" state',\n    \"default\": \"an unknown error accured in `state-local` package\"\n};\nvar errorHandler = curry(throwError)(errorMessages);\nvar validators = {\n    changes: validateChanges,\n    selector: validateSelector,\n    handler: validateHandler,\n    initial: validateInitial\n};\nfunction create(initial) {\n    var handler = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    validators.initial(initial);\n    validators.handler(handler);\n    var state = {\n        current: initial\n    };\n    var didUpdate = curry(didStateUpdate)(state, handler);\n    var update = curry(updateState)(state);\n    var validate = curry(validators.changes)(initial);\n    var getChanges = curry(extractChanges)(state);\n    function getState() {\n        var selector = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : function(state) {\n            return state;\n        };\n        validators.selector(selector);\n        return selector(state.current);\n    }\n    function setState(causedChanges) {\n        compose(didUpdate, update, validate, getChanges)(causedChanges);\n    }\n    return [\n        getState,\n        setState\n    ];\n}\nfunction extractChanges(state, causedChanges) {\n    return isFunction(causedChanges) ? causedChanges(state.current) : causedChanges;\n}\nfunction updateState(state, changes) {\n    state.current = _objectSpread2(_objectSpread2({}, state.current), changes);\n    return changes;\n}\nfunction didStateUpdate(state, handler, changes) {\n    isFunction(handler) ? handler(state.current) : Object.keys(changes).forEach(function(field) {\n        var _handler$field;\n        return (_handler$field = handler[field]) === null || _handler$field === void 0 ? void 0 : _handler$field.call(handler, state.current[field]);\n    });\n    return changes;\n}\nvar index = {\n    create: create\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (index);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3RhdGUtbG9jYWwvbGliL2VzL3N0YXRlLWxvY2FsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxTQUFTQSxnQkFBZ0JDLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxLQUFLO0lBQ3RDLElBQUlELE9BQU9ELEtBQUs7UUFDZEcsT0FBT0MsY0FBYyxDQUFDSixLQUFLQyxLQUFLO1lBQzlCQyxPQUFPQTtZQUNQRyxZQUFZO1lBQ1pDLGNBQWM7WUFDZEMsVUFBVTtRQUNaO0lBQ0YsT0FBTztRQUNMUCxHQUFHLENBQUNDLElBQUksR0FBR0M7SUFDYjtJQUVBLE9BQU9GO0FBQ1Q7QUFFQSxTQUFTUSxRQUFRQyxNQUFNLEVBQUVDLGNBQWM7SUFDckMsSUFBSUMsT0FBT1IsT0FBT1EsSUFBSSxDQUFDRjtJQUV2QixJQUFJTixPQUFPUyxxQkFBcUIsRUFBRTtRQUNoQyxJQUFJQyxVQUFVVixPQUFPUyxxQkFBcUIsQ0FBQ0g7UUFDM0MsSUFBSUMsZ0JBQWdCRyxVQUFVQSxRQUFRQyxNQUFNLENBQUMsU0FBVUMsR0FBRztZQUN4RCxPQUFPWixPQUFPYSx3QkFBd0IsQ0FBQ1AsUUFBUU0sS0FBS1YsVUFBVTtRQUNoRTtRQUNBTSxLQUFLTSxJQUFJLENBQUNDLEtBQUssQ0FBQ1AsTUFBTUU7SUFDeEI7SUFFQSxPQUFPRjtBQUNUO0FBRUEsU0FBU1EsZUFBZUMsTUFBTTtJQUM1QixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUMsVUFBVUMsTUFBTSxFQUFFRixJQUFLO1FBQ3pDLElBQUlHLFNBQVNGLFNBQVMsQ0FBQ0QsRUFBRSxJQUFJLE9BQU9DLFNBQVMsQ0FBQ0QsRUFBRSxHQUFHLENBQUM7UUFFcEQsSUFBSUEsSUFBSSxHQUFHO1lBQ1RiLFFBQVFMLE9BQU9xQixTQUFTLE1BQU1DLE9BQU8sQ0FBQyxTQUFVeEIsR0FBRztnQkFDakRGLGdCQUFnQnFCLFFBQVFuQixLQUFLdUIsTUFBTSxDQUFDdkIsSUFBSTtZQUMxQztRQUNGLE9BQU8sSUFBSUUsT0FBT3VCLHlCQUF5QixFQUFFO1lBQzNDdkIsT0FBT3dCLGdCQUFnQixDQUFDUCxRQUFRakIsT0FBT3VCLHlCQUF5QixDQUFDRjtRQUNuRSxPQUFPO1lBQ0xoQixRQUFRTCxPQUFPcUIsU0FBU0MsT0FBTyxDQUFDLFNBQVV4QixHQUFHO2dCQUMzQ0UsT0FBT0MsY0FBYyxDQUFDZ0IsUUFBUW5CLEtBQUtFLE9BQU9hLHdCQUF3QixDQUFDUSxRQUFRdkI7WUFDN0U7UUFDRjtJQUNGO0lBRUEsT0FBT21CO0FBQ1Q7QUFFQSxTQUFTUTtJQUNQLElBQUssSUFBSUMsT0FBT1AsVUFBVUMsTUFBTSxFQUFFTyxNQUFNLElBQUlDLE1BQU1GLE9BQU9HLE9BQU8sR0FBR0EsT0FBT0gsTUFBTUcsT0FBUTtRQUN0RkYsR0FBRyxDQUFDRSxLQUFLLEdBQUdWLFNBQVMsQ0FBQ1UsS0FBSztJQUM3QjtJQUVBLE9BQU8sU0FBVUMsQ0FBQztRQUNoQixPQUFPSCxJQUFJSSxXQUFXLENBQUMsU0FBVUMsQ0FBQyxFQUFFQyxDQUFDO1lBQ25DLE9BQU9BLEVBQUVEO1FBQ1gsR0FBR0Y7SUFDTDtBQUNGO0FBRUEsU0FBU0ksTUFBTUMsRUFBRTtJQUNmLE9BQU8sU0FBU0M7UUFDZCxJQUFJQyxRQUFRLElBQUk7UUFFaEIsSUFBSyxJQUFJQyxRQUFRbkIsVUFBVUMsTUFBTSxFQUFFbUIsT0FBTyxJQUFJWCxNQUFNVSxRQUFRRSxRQUFRLEdBQUdBLFFBQVFGLE9BQU9FLFFBQVM7WUFDN0ZELElBQUksQ0FBQ0MsTUFBTSxHQUFHckIsU0FBUyxDQUFDcUIsTUFBTTtRQUNoQztRQUVBLE9BQU9ELEtBQUtuQixNQUFNLElBQUllLEdBQUdmLE1BQU0sR0FBR2UsR0FBR3BCLEtBQUssQ0FBQyxJQUFJLEVBQUV3QixRQUFRO1lBQ3ZELElBQUssSUFBSUUsUUFBUXRCLFVBQVVDLE1BQU0sRUFBRXNCLFdBQVcsSUFBSWQsTUFBTWEsUUFBUUUsUUFBUSxHQUFHQSxRQUFRRixPQUFPRSxRQUFTO2dCQUNqR0QsUUFBUSxDQUFDQyxNQUFNLEdBQUd4QixTQUFTLENBQUN3QixNQUFNO1lBQ3BDO1lBRUEsT0FBT1AsUUFBUXJCLEtBQUssQ0FBQ3NCLE9BQU8sRUFBRSxDQUFDTyxNQUFNLENBQUNMLE1BQU1HO1FBQzlDO0lBQ0Y7QUFDRjtBQUVBLFNBQVNHLFNBQVM5QyxLQUFLO0lBQ3JCLE9BQU8sRUFBQyxHQUFFK0MsUUFBUSxDQUFDQyxJQUFJLENBQUNoRCxPQUFPaUQsUUFBUSxDQUFDO0FBQzFDO0FBRUEsU0FBU0MsUUFBUXBELEdBQUc7SUFDbEIsT0FBTyxDQUFDRyxPQUFPUSxJQUFJLENBQUNYLEtBQUt1QixNQUFNO0FBQ2pDO0FBRUEsU0FBUzhCLFdBQVduRCxLQUFLO0lBQ3ZCLE9BQU8sT0FBT0EsVUFBVTtBQUMxQjtBQUVBLFNBQVNvRCxlQUFlN0MsTUFBTSxFQUFFOEMsUUFBUTtJQUN0QyxPQUFPcEQsT0FBT3FELFNBQVMsQ0FBQ0YsY0FBYyxDQUFDSixJQUFJLENBQUN6QyxRQUFROEM7QUFDdEQ7QUFFQSxTQUFTRSxnQkFBZ0JDLE9BQU8sRUFBRUMsT0FBTztJQUN2QyxJQUFJLENBQUNYLFNBQVNXLFVBQVVDLGFBQWE7SUFDckMsSUFBSXpELE9BQU9RLElBQUksQ0FBQ2dELFNBQVNFLElBQUksQ0FBQyxTQUFVQyxLQUFLO1FBQzNDLE9BQU8sQ0FBQ1IsZUFBZUksU0FBU0k7SUFDbEMsSUFBSUYsYUFBYTtJQUNqQixPQUFPRDtBQUNUO0FBRUEsU0FBU0ksaUJBQWlCQyxRQUFRO0lBQ2hDLElBQUksQ0FBQ1gsV0FBV1csV0FBV0osYUFBYTtBQUMxQztBQUVBLFNBQVNLLGdCQUFnQkMsT0FBTztJQUM5QixJQUFJLENBQUViLENBQUFBLFdBQVdhLFlBQVlsQixTQUFTa0IsUUFBTyxHQUFJTixhQUFhO0lBQzlELElBQUlaLFNBQVNrQixZQUFZL0QsT0FBT2dFLE1BQU0sQ0FBQ0QsU0FBU0wsSUFBSSxDQUFDLFNBQVVPLFFBQVE7UUFDckUsT0FBTyxDQUFDZixXQUFXZTtJQUNyQixJQUFJUixhQUFhO0FBQ25CO0FBRUEsU0FBU1MsZ0JBQWdCWCxPQUFPO0lBQzlCLElBQUksQ0FBQ0EsU0FBU0UsYUFBYTtJQUMzQixJQUFJLENBQUNaLFNBQVNVLFVBQVVFLGFBQWE7SUFDckMsSUFBSVIsUUFBUU0sVUFBVUUsYUFBYTtBQUNyQztBQUVBLFNBQVNVLFdBQVdDLGFBQWEsRUFBRUMsSUFBSTtJQUNyQyxNQUFNLElBQUlDLE1BQU1GLGFBQWEsQ0FBQ0MsS0FBSyxJQUFJRCxhQUFhLENBQUMsVUFBVTtBQUNqRTtBQUVBLElBQUlBLGdCQUFnQjtJQUNsQkcsbUJBQW1CO0lBQ25CQyxhQUFhO0lBQ2JDLGdCQUFnQjtJQUNoQkMsYUFBYTtJQUNiQyxjQUFjO0lBQ2RDLGNBQWM7SUFDZEMsWUFBWTtJQUNaQyxhQUFhO0lBQ2IsV0FBVztBQUNiO0FBQ0EsSUFBSXJCLGVBQWV2QixNQUFNaUMsWUFBWUM7QUFDckMsSUFBSVcsYUFBYTtJQUNmdkIsU0FBU0Y7SUFDVE8sVUFBVUQ7SUFDVkcsU0FBU0Q7SUFDVFAsU0FBU1c7QUFDWDtBQUVBLFNBQVNjLE9BQU96QixPQUFPO0lBQ3JCLElBQUlRLFVBQVU1QyxVQUFVQyxNQUFNLEdBQUcsS0FBS0QsU0FBUyxDQUFDLEVBQUUsS0FBSzhELFlBQVk5RCxTQUFTLENBQUMsRUFBRSxHQUFHLENBQUM7SUFDbkY0RCxXQUFXeEIsT0FBTyxDQUFDQTtJQUNuQndCLFdBQVdoQixPQUFPLENBQUNBO0lBQ25CLElBQUltQixRQUFRO1FBQ1ZDLFNBQVM1QjtJQUNYO0lBQ0EsSUFBSTZCLFlBQVlsRCxNQUFNbUQsZ0JBQWdCSCxPQUFPbkI7SUFDN0MsSUFBSXVCLFNBQVNwRCxNQUFNcUQsYUFBYUw7SUFDaEMsSUFBSU0sV0FBV3RELE1BQU02QyxXQUFXdkIsT0FBTyxFQUFFRDtJQUN6QyxJQUFJa0MsYUFBYXZELE1BQU13RCxnQkFBZ0JSO0lBRXZDLFNBQVNTO1FBQ1AsSUFBSTlCLFdBQVcxQyxVQUFVQyxNQUFNLEdBQUcsS0FBS0QsU0FBUyxDQUFDLEVBQUUsS0FBSzhELFlBQVk5RCxTQUFTLENBQUMsRUFBRSxHQUFHLFNBQVUrRCxLQUFLO1lBQ2hHLE9BQU9BO1FBQ1Q7UUFDQUgsV0FBV2xCLFFBQVEsQ0FBQ0E7UUFDcEIsT0FBT0EsU0FBU3FCLE1BQU1DLE9BQU87SUFDL0I7SUFFQSxTQUFTUyxTQUFTQyxhQUFhO1FBQzdCcEUsUUFBUTJELFdBQVdFLFFBQVFFLFVBQVVDLFlBQVlJO0lBQ25EO0lBRUEsT0FBTztRQUFDRjtRQUFVQztLQUFTO0FBQzdCO0FBRUEsU0FBU0YsZUFBZVIsS0FBSyxFQUFFVyxhQUFhO0lBQzFDLE9BQU8zQyxXQUFXMkMsaUJBQWlCQSxjQUFjWCxNQUFNQyxPQUFPLElBQUlVO0FBQ3BFO0FBRUEsU0FBU04sWUFBWUwsS0FBSyxFQUFFMUIsT0FBTztJQUNqQzBCLE1BQU1DLE9BQU8sR0FBR25FLGVBQWVBLGVBQWUsQ0FBQyxHQUFHa0UsTUFBTUMsT0FBTyxHQUFHM0I7SUFDbEUsT0FBT0E7QUFDVDtBQUVBLFNBQVM2QixlQUFlSCxLQUFLLEVBQUVuQixPQUFPLEVBQUVQLE9BQU87SUFDN0NOLFdBQVdhLFdBQVdBLFFBQVFtQixNQUFNQyxPQUFPLElBQUluRixPQUFPUSxJQUFJLENBQUNnRCxTQUFTbEMsT0FBTyxDQUFDLFNBQVVxQyxLQUFLO1FBQ3pGLElBQUltQztRQUVKLE9BQU8sQ0FBQ0EsaUJBQWlCL0IsT0FBTyxDQUFDSixNQUFNLE1BQU0sUUFBUW1DLG1CQUFtQixLQUFLLElBQUksS0FBSyxJQUFJQSxlQUFlL0MsSUFBSSxDQUFDZ0IsU0FBU21CLE1BQU1DLE9BQU8sQ0FBQ3hCLE1BQU07SUFDN0k7SUFDQSxPQUFPSDtBQUNUO0FBRUEsSUFBSXVDLFFBQVE7SUFDVmYsUUFBUUE7QUFDVjtBQUVBLGlFQUFlZSxLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3N0YXRlLWxvY2FsL2xpYi9lcy9zdGF0ZS1sb2NhbC5qcz84NjY2Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIF9kZWZpbmVQcm9wZXJ0eShvYmosIGtleSwgdmFsdWUpIHtcbiAgaWYgKGtleSBpbiBvYmopIHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHtcbiAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICB3cml0YWJsZTogdHJ1ZVxuICAgIH0pO1xuICB9IGVsc2Uge1xuICAgIG9ialtrZXldID0gdmFsdWU7XG4gIH1cblxuICByZXR1cm4gb2JqO1xufVxuXG5mdW5jdGlvbiBvd25LZXlzKG9iamVjdCwgZW51bWVyYWJsZU9ubHkpIHtcbiAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhvYmplY3QpO1xuXG4gIGlmIChPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKSB7XG4gICAgdmFyIHN5bWJvbHMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKG9iamVjdCk7XG4gICAgaWYgKGVudW1lcmFibGVPbmx5KSBzeW1ib2xzID0gc3ltYm9scy5maWx0ZXIoZnVuY3Rpb24gKHN5bSkge1xuICAgICAgcmV0dXJuIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3Iob2JqZWN0LCBzeW0pLmVudW1lcmFibGU7XG4gICAgfSk7XG4gICAga2V5cy5wdXNoLmFwcGx5KGtleXMsIHN5bWJvbHMpO1xuICB9XG5cbiAgcmV0dXJuIGtleXM7XG59XG5cbmZ1bmN0aW9uIF9vYmplY3RTcHJlYWQyKHRhcmdldCkge1xuICBmb3IgKHZhciBpID0gMTsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKykge1xuICAgIHZhciBzb3VyY2UgPSBhcmd1bWVudHNbaV0gIT0gbnVsbCA/IGFyZ3VtZW50c1tpXSA6IHt9O1xuXG4gICAgaWYgKGkgJSAyKSB7XG4gICAgICBvd25LZXlzKE9iamVjdChzb3VyY2UpLCB0cnVlKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgICAgX2RlZmluZVByb3BlcnR5KHRhcmdldCwga2V5LCBzb3VyY2Vba2V5XSk7XG4gICAgICB9KTtcbiAgICB9IGVsc2UgaWYgKE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKSB7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0YXJnZXQsIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3JzKHNvdXJjZSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBvd25LZXlzKE9iamVjdChzb3VyY2UpKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwga2V5LCBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHNvdXJjZSwga2V5KSk7XG4gICAgICB9KTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG5mdW5jdGlvbiBjb21wb3NlKCkge1xuICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgZm5zID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgIGZuc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgfVxuXG4gIHJldHVybiBmdW5jdGlvbiAoeCkge1xuICAgIHJldHVybiBmbnMucmVkdWNlUmlnaHQoZnVuY3Rpb24gKHksIGYpIHtcbiAgICAgIHJldHVybiBmKHkpO1xuICAgIH0sIHgpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBjdXJyeShmbikge1xuICByZXR1cm4gZnVuY3Rpb24gY3VycmllZCgpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgZm9yICh2YXIgX2xlbjIgPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4yKSwgX2tleTIgPSAwOyBfa2V5MiA8IF9sZW4yOyBfa2V5MisrKSB7XG4gICAgICBhcmdzW19rZXkyXSA9IGFyZ3VtZW50c1tfa2V5Ml07XG4gICAgfVxuXG4gICAgcmV0dXJuIGFyZ3MubGVuZ3RoID49IGZuLmxlbmd0aCA/IGZuLmFwcGx5KHRoaXMsIGFyZ3MpIDogZnVuY3Rpb24gKCkge1xuICAgICAgZm9yICh2YXIgX2xlbjMgPSBhcmd1bWVudHMubGVuZ3RoLCBuZXh0QXJncyA9IG5ldyBBcnJheShfbGVuMyksIF9rZXkzID0gMDsgX2tleTMgPCBfbGVuMzsgX2tleTMrKykge1xuICAgICAgICBuZXh0QXJnc1tfa2V5M10gPSBhcmd1bWVudHNbX2tleTNdO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gY3VycmllZC5hcHBseShfdGhpcywgW10uY29uY2F0KGFyZ3MsIG5leHRBcmdzKSk7XG4gICAgfTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gaXNPYmplY3QodmFsdWUpIHtcbiAgcmV0dXJuIHt9LnRvU3RyaW5nLmNhbGwodmFsdWUpLmluY2x1ZGVzKCdPYmplY3QnKTtcbn1cblxuZnVuY3Rpb24gaXNFbXB0eShvYmopIHtcbiAgcmV0dXJuICFPYmplY3Qua2V5cyhvYmopLmxlbmd0aDtcbn1cblxuZnVuY3Rpb24gaXNGdW5jdGlvbih2YWx1ZSkge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nO1xufVxuXG5mdW5jdGlvbiBoYXNPd25Qcm9wZXJ0eShvYmplY3QsIHByb3BlcnR5KSB7XG4gIHJldHVybiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqZWN0LCBwcm9wZXJ0eSk7XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlQ2hhbmdlcyhpbml0aWFsLCBjaGFuZ2VzKSB7XG4gIGlmICghaXNPYmplY3QoY2hhbmdlcykpIGVycm9ySGFuZGxlcignY2hhbmdlVHlwZScpO1xuICBpZiAoT2JqZWN0LmtleXMoY2hhbmdlcykuc29tZShmdW5jdGlvbiAoZmllbGQpIHtcbiAgICByZXR1cm4gIWhhc093blByb3BlcnR5KGluaXRpYWwsIGZpZWxkKTtcbiAgfSkpIGVycm9ySGFuZGxlcignY2hhbmdlRmllbGQnKTtcbiAgcmV0dXJuIGNoYW5nZXM7XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlU2VsZWN0b3Ioc2VsZWN0b3IpIHtcbiAgaWYgKCFpc0Z1bmN0aW9uKHNlbGVjdG9yKSkgZXJyb3JIYW5kbGVyKCdzZWxlY3RvclR5cGUnKTtcbn1cblxuZnVuY3Rpb24gdmFsaWRhdGVIYW5kbGVyKGhhbmRsZXIpIHtcbiAgaWYgKCEoaXNGdW5jdGlvbihoYW5kbGVyKSB8fCBpc09iamVjdChoYW5kbGVyKSkpIGVycm9ySGFuZGxlcignaGFuZGxlclR5cGUnKTtcbiAgaWYgKGlzT2JqZWN0KGhhbmRsZXIpICYmIE9iamVjdC52YWx1ZXMoaGFuZGxlcikuc29tZShmdW5jdGlvbiAoX2hhbmRsZXIpIHtcbiAgICByZXR1cm4gIWlzRnVuY3Rpb24oX2hhbmRsZXIpO1xuICB9KSkgZXJyb3JIYW5kbGVyKCdoYW5kbGVyc1R5cGUnKTtcbn1cblxuZnVuY3Rpb24gdmFsaWRhdGVJbml0aWFsKGluaXRpYWwpIHtcbiAgaWYgKCFpbml0aWFsKSBlcnJvckhhbmRsZXIoJ2luaXRpYWxJc1JlcXVpcmVkJyk7XG4gIGlmICghaXNPYmplY3QoaW5pdGlhbCkpIGVycm9ySGFuZGxlcignaW5pdGlhbFR5cGUnKTtcbiAgaWYgKGlzRW1wdHkoaW5pdGlhbCkpIGVycm9ySGFuZGxlcignaW5pdGlhbENvbnRlbnQnKTtcbn1cblxuZnVuY3Rpb24gdGhyb3dFcnJvcihlcnJvck1lc3NhZ2VzLCB0eXBlKSB7XG4gIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2VzW3R5cGVdIHx8IGVycm9yTWVzc2FnZXNbXCJkZWZhdWx0XCJdKTtcbn1cblxudmFyIGVycm9yTWVzc2FnZXMgPSB7XG4gIGluaXRpYWxJc1JlcXVpcmVkOiAnaW5pdGlhbCBzdGF0ZSBpcyByZXF1aXJlZCcsXG4gIGluaXRpYWxUeXBlOiAnaW5pdGlhbCBzdGF0ZSBzaG91bGQgYmUgYW4gb2JqZWN0JyxcbiAgaW5pdGlhbENvbnRlbnQ6ICdpbml0aWFsIHN0YXRlIHNob3VsZG5cXCd0IGJlIGFuIGVtcHR5IG9iamVjdCcsXG4gIGhhbmRsZXJUeXBlOiAnaGFuZGxlciBzaG91bGQgYmUgYW4gb2JqZWN0IG9yIGEgZnVuY3Rpb24nLFxuICBoYW5kbGVyc1R5cGU6ICdhbGwgaGFuZGxlcnMgc2hvdWxkIGJlIGEgZnVuY3Rpb25zJyxcbiAgc2VsZWN0b3JUeXBlOiAnc2VsZWN0b3Igc2hvdWxkIGJlIGEgZnVuY3Rpb24nLFxuICBjaGFuZ2VUeXBlOiAncHJvdmlkZWQgdmFsdWUgb2YgY2hhbmdlcyBzaG91bGQgYmUgYW4gb2JqZWN0JyxcbiAgY2hhbmdlRmllbGQ6ICdpdCBzZWFtcyB5b3Ugd2FudCB0byBjaGFuZ2UgYSBmaWVsZCBpbiB0aGUgc3RhdGUgd2hpY2ggaXMgbm90IHNwZWNpZmllZCBpbiB0aGUgXCJpbml0aWFsXCIgc3RhdGUnLFxuICBcImRlZmF1bHRcIjogJ2FuIHVua25vd24gZXJyb3IgYWNjdXJlZCBpbiBgc3RhdGUtbG9jYWxgIHBhY2thZ2UnXG59O1xudmFyIGVycm9ySGFuZGxlciA9IGN1cnJ5KHRocm93RXJyb3IpKGVycm9yTWVzc2FnZXMpO1xudmFyIHZhbGlkYXRvcnMgPSB7XG4gIGNoYW5nZXM6IHZhbGlkYXRlQ2hhbmdlcyxcbiAgc2VsZWN0b3I6IHZhbGlkYXRlU2VsZWN0b3IsXG4gIGhhbmRsZXI6IHZhbGlkYXRlSGFuZGxlcixcbiAgaW5pdGlhbDogdmFsaWRhdGVJbml0aWFsXG59O1xuXG5mdW5jdGlvbiBjcmVhdGUoaW5pdGlhbCkge1xuICB2YXIgaGFuZGxlciA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDoge307XG4gIHZhbGlkYXRvcnMuaW5pdGlhbChpbml0aWFsKTtcbiAgdmFsaWRhdG9ycy5oYW5kbGVyKGhhbmRsZXIpO1xuICB2YXIgc3RhdGUgPSB7XG4gICAgY3VycmVudDogaW5pdGlhbFxuICB9O1xuICB2YXIgZGlkVXBkYXRlID0gY3VycnkoZGlkU3RhdGVVcGRhdGUpKHN0YXRlLCBoYW5kbGVyKTtcbiAgdmFyIHVwZGF0ZSA9IGN1cnJ5KHVwZGF0ZVN0YXRlKShzdGF0ZSk7XG4gIHZhciB2YWxpZGF0ZSA9IGN1cnJ5KHZhbGlkYXRvcnMuY2hhbmdlcykoaW5pdGlhbCk7XG4gIHZhciBnZXRDaGFuZ2VzID0gY3VycnkoZXh0cmFjdENoYW5nZXMpKHN0YXRlKTtcblxuICBmdW5jdGlvbiBnZXRTdGF0ZSgpIHtcbiAgICB2YXIgc2VsZWN0b3IgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IGZ1bmN0aW9uIChzdGF0ZSkge1xuICAgICAgcmV0dXJuIHN0YXRlO1xuICAgIH07XG4gICAgdmFsaWRhdG9ycy5zZWxlY3RvcihzZWxlY3Rvcik7XG4gICAgcmV0dXJuIHNlbGVjdG9yKHN0YXRlLmN1cnJlbnQpO1xuICB9XG5cbiAgZnVuY3Rpb24gc2V0U3RhdGUoY2F1c2VkQ2hhbmdlcykge1xuICAgIGNvbXBvc2UoZGlkVXBkYXRlLCB1cGRhdGUsIHZhbGlkYXRlLCBnZXRDaGFuZ2VzKShjYXVzZWRDaGFuZ2VzKTtcbiAgfVxuXG4gIHJldHVybiBbZ2V0U3RhdGUsIHNldFN0YXRlXTtcbn1cblxuZnVuY3Rpb24gZXh0cmFjdENoYW5nZXMoc3RhdGUsIGNhdXNlZENoYW5nZXMpIHtcbiAgcmV0dXJuIGlzRnVuY3Rpb24oY2F1c2VkQ2hhbmdlcykgPyBjYXVzZWRDaGFuZ2VzKHN0YXRlLmN1cnJlbnQpIDogY2F1c2VkQ2hhbmdlcztcbn1cblxuZnVuY3Rpb24gdXBkYXRlU3RhdGUoc3RhdGUsIGNoYW5nZXMpIHtcbiAgc3RhdGUuY3VycmVudCA9IF9vYmplY3RTcHJlYWQyKF9vYmplY3RTcHJlYWQyKHt9LCBzdGF0ZS5jdXJyZW50KSwgY2hhbmdlcyk7XG4gIHJldHVybiBjaGFuZ2VzO1xufVxuXG5mdW5jdGlvbiBkaWRTdGF0ZVVwZGF0ZShzdGF0ZSwgaGFuZGxlciwgY2hhbmdlcykge1xuICBpc0Z1bmN0aW9uKGhhbmRsZXIpID8gaGFuZGxlcihzdGF0ZS5jdXJyZW50KSA6IE9iamVjdC5rZXlzKGNoYW5nZXMpLmZvckVhY2goZnVuY3Rpb24gKGZpZWxkKSB7XG4gICAgdmFyIF9oYW5kbGVyJGZpZWxkO1xuXG4gICAgcmV0dXJuIChfaGFuZGxlciRmaWVsZCA9IGhhbmRsZXJbZmllbGRdKSA9PT0gbnVsbCB8fCBfaGFuZGxlciRmaWVsZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2hhbmRsZXIkZmllbGQuY2FsbChoYW5kbGVyLCBzdGF0ZS5jdXJyZW50W2ZpZWxkXSk7XG4gIH0pO1xuICByZXR1cm4gY2hhbmdlcztcbn1cblxudmFyIGluZGV4ID0ge1xuICBjcmVhdGU6IGNyZWF0ZVxufTtcblxuZXhwb3J0IGRlZmF1bHQgaW5kZXg7XG4iXSwibmFtZXMiOlsiX2RlZmluZVByb3BlcnR5Iiwib2JqIiwia2V5IiwidmFsdWUiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImVudW1lcmFibGUiLCJjb25maWd1cmFibGUiLCJ3cml0YWJsZSIsIm93bktleXMiLCJvYmplY3QiLCJlbnVtZXJhYmxlT25seSIsImtleXMiLCJnZXRPd25Qcm9wZXJ0eVN5bWJvbHMiLCJzeW1ib2xzIiwiZmlsdGVyIiwic3ltIiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwicHVzaCIsImFwcGx5IiwiX29iamVjdFNwcmVhZDIiLCJ0YXJnZXQiLCJpIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwic291cmNlIiwiZm9yRWFjaCIsImdldE93blByb3BlcnR5RGVzY3JpcHRvcnMiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiY29tcG9zZSIsIl9sZW4iLCJmbnMiLCJBcnJheSIsIl9rZXkiLCJ4IiwicmVkdWNlUmlnaHQiLCJ5IiwiZiIsImN1cnJ5IiwiZm4iLCJjdXJyaWVkIiwiX3RoaXMiLCJfbGVuMiIsImFyZ3MiLCJfa2V5MiIsIl9sZW4zIiwibmV4dEFyZ3MiLCJfa2V5MyIsImNvbmNhdCIsImlzT2JqZWN0IiwidG9TdHJpbmciLCJjYWxsIiwiaW5jbHVkZXMiLCJpc0VtcHR5IiwiaXNGdW5jdGlvbiIsImhhc093blByb3BlcnR5IiwicHJvcGVydHkiLCJwcm90b3R5cGUiLCJ2YWxpZGF0ZUNoYW5nZXMiLCJpbml0aWFsIiwiY2hhbmdlcyIsImVycm9ySGFuZGxlciIsInNvbWUiLCJmaWVsZCIsInZhbGlkYXRlU2VsZWN0b3IiLCJzZWxlY3RvciIsInZhbGlkYXRlSGFuZGxlciIsImhhbmRsZXIiLCJ2YWx1ZXMiLCJfaGFuZGxlciIsInZhbGlkYXRlSW5pdGlhbCIsInRocm93RXJyb3IiLCJlcnJvck1lc3NhZ2VzIiwidHlwZSIsIkVycm9yIiwiaW5pdGlhbElzUmVxdWlyZWQiLCJpbml0aWFsVHlwZSIsImluaXRpYWxDb250ZW50IiwiaGFuZGxlclR5cGUiLCJoYW5kbGVyc1R5cGUiLCJzZWxlY3RvclR5cGUiLCJjaGFuZ2VUeXBlIiwiY2hhbmdlRmllbGQiLCJ2YWxpZGF0b3JzIiwiY3JlYXRlIiwidW5kZWZpbmVkIiwic3RhdGUiLCJjdXJyZW50IiwiZGlkVXBkYXRlIiwiZGlkU3RhdGVVcGRhdGUiLCJ1cGRhdGUiLCJ1cGRhdGVTdGF0ZSIsInZhbGlkYXRlIiwiZ2V0Q2hhbmdlcyIsImV4dHJhY3RDaGFuZ2VzIiwiZ2V0U3RhdGUiLCJzZXRTdGF0ZSIsImNhdXNlZENoYW5nZXMiLCJfaGFuZGxlciRmaWVsZCIsImluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/state-local/lib/es/state-local.js\n");

/***/ })

};
;