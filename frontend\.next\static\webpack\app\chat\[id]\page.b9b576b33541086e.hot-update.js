"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/chat-header.tsx":
/*!************************************!*\
  !*** ./components/chat-header.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatHeader: function() { return /* binding */ ChatHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./lib/auth-context.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Code,Code2,Eye,MoreVertical,Settings,Share!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ ChatHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatHeader(param) {\n    let { chat, versions, currentVersion, onVersionChange, showPreview, onTogglePreview } = param;\n    _s();\n    const { user } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const goBack = ()=>{\n        router.push(\"/dashboard\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#1e1e1e] border-b border-[#333]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-[#2d2d30] border-b border-[#333]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goBack,\n                                className: \"p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center space-x-1 px-2 py-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs\",\n                                        children: \"Explorer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onTogglePreview(!showPreview),\n                                className: \"flex items-center space-x-1 px-2 py-1 rounded transition-colors \".concat(showPreview ? \"bg-[#0e639c] text-white\" : \"text-[#cccccc] hover:bg-[#3c3c3c]\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs\",\n                                        children: \"Preview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onTogglePreview(false),\n                                className: \"flex items-center space-x-1 px-2 py-1 rounded transition-colors \".concat(!showPreview ? \"bg-[#0e639c] text-white\" : \"text-[#cccccc] hover:bg-[#3c3c3c]\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs\",\n                                        children: \"Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-3 h-3 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-[#cccccc] font-medium\",\n                                children: chat.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            versions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center space-x-1 px-2 py-1 bg-[#3c3c3c] hover:bg-[#4c4c4c] rounded text-[#cccccc] text-xs transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"v\",\n                                                        (currentVersion === null || currentVersion === void 0 ? void 0 : currentVersion.version_number) || 1\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-48 bg-[#252526] border-[#3c3c3c]\",\n                                        children: versions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                onClick: ()=>onVersionChange(version),\n                                                className: \"text-[#cccccc] hover:bg-[#3c3c3c] \".concat((currentVersion === null || currentVersion === void 0 ? void 0 : currentVersion.id) === version.id ? \"bg-[#3c3c3c]\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Version \",\n                                                                version.version_number\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        version.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-[#858585]\",\n                                                            children: version.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, version.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-1 text-[#cccccc] hover:bg-[#3c3c3c] rounded transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"bg-[#252526] border-[#3c3c3c]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                className: \"text-[#cccccc] hover:bg-[#3c3c3c]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Share Chat\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                className: \"text-[#cccccc] hover:bg-[#3c3c3c]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Chat Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center bg-[#252526]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 px-4 py-2 text-sm border-r border-[#333] transition-colors \".concat(!showPreview ? \"bg-[#1e1e1e] text-[#cccccc] border-b-2 border-b-[#0e639c]\" : \"text-[#858585] hover:text-[#cccccc] hover:bg-[#2d2d30] cursor-pointer\"),\n                            onClick: ()=>onTogglePreview(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"globals.css\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-[#4ec9b0]\",\n                                    children: \"+5 / -5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 px-4 py-2 text-sm border-r border-[#333] transition-colors \".concat(showPreview ? \"bg-[#1e1e1e] text-[#cccccc] border-b-2 border-b-[#0e639c]\" : \"text-[#858585] hover:text-[#cccccc] hover:bg-[#2d2d30] cursor-pointer\"),\n                            onClick: ()=>onTogglePreview(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Code_Code2_Eye_MoreVertical_Settings_Share_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\chat-header.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatHeader, \"/ipm2DYjne/lfnn1P5PW8H/SkGk=\", false, function() {\n    return [\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = ChatHeader;\nvar _c;\n$RefreshReg$(_c, \"ChatHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/chat-header.tsx\n"));

/***/ })

});