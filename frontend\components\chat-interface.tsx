'use client'

import { useState, useRef, useEffect } from 'react'
import { chatAPI, type Chat, type Message, type Version } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Bot, User, Send, Loader2 } from 'lucide-react'

interface ChatInterfaceProps {
  chat: Chat
  messages: Message[]
  onNewMessage: (message: Message) => void
  onNewVersion: (version: Version) => void
}

export function ChatInterface({ 
  chat, 
  messages, 
  onNewMessage, 
  onNewVersion 
}: ChatInterfaceProps) {
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [streamingMessage, setStreamingMessage] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString(),
      chat_id: chat.id
    }

    onNewMessage(userMessage)
    setInput('')
    setIsLoading(true)
    setStreamingMessage('')

    try {
      const response = await fetch('/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chat.id,
          message: input.trim()
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      const reader = response.body?.getReader()
      if (!reader) throw new Error('No response body')

      let fullResponse = ''
      
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              
              if (data.type === 'message') {
                fullResponse += data.content
                setStreamingMessage(fullResponse)
              } else if (data.type === 'version') {
                onNewVersion(data.version)
              } else if (data.type === 'done') {
                const assistantMessage: Message = {
                  id: Date.now().toString(),
                  role: 'assistant',
                  content: fullResponse,
                  timestamp: new Date().toISOString(),
                  chat_id: chat.id
                }
                onNewMessage(assistantMessage)
                setStreamingMessage('')
              }
            } catch (e) {
              console.error('Error parsing SSE data:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="h-full flex flex-col bg-[#0d1117]">
      {/* Chat Header */}
      <div className="p-4 border-b border-[#21262d]">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">AI</span>
          </div>
          <div>
            <h2 className="text-[#f0f6fc] font-semibold">{chat.name}</h2>
            <p className="text-[#7d8590] text-sm">Codora chat interface</p>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className="space-y-2">
            {message.role === 'user' ? (
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-[#21262d] rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="w-3 h-3 text-[#7d8590]" />
                </div>
                <div className="flex-1">
                  <div className="text-[#f0f6fc] text-sm leading-relaxed">
                    {message.content}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                    <Bot className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-[#f0f6fc] text-sm font-medium">AI Assistant</span>
                </div>
                <div className="ml-8 text-[#e6edf3] text-sm leading-relaxed whitespace-pre-wrap">
                  {message.content}
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Streaming Message */}
        {streamingMessage && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                <Bot className="w-3 h-3 text-white" />
              </div>
              <span className="text-[#f0f6fc] text-sm font-medium">AI Assistant</span>
            </div>
            <div className="ml-8 text-[#e6edf3] text-sm leading-relaxed whitespace-pre-wrap">
              {streamingMessage}
              <span className="inline-block w-2 h-4 bg-[#0969da] ml-1 animate-pulse" />
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t border-[#21262d] p-4">
        <div className="space-y-3">
          <div className="relative">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask a follow up..."
              className="w-full bg-[#0d1117] border border-[#30363d] text-[#f0f6fc] placeholder-[#7d8590] resize-none min-h-[80px] max-h-[200px] pr-12 rounded-lg focus:border-[#0969da] focus:ring-1 focus:ring-[#0969da]"
              disabled={isLoading}
            />
            <Button
              onClick={handleSendMessage}
              disabled={isLoading || !input.trim()}
              className="absolute bottom-3 right-3 bg-[#0969da] hover:bg-[#0860ca] text-white p-2 h-8 w-8 rounded-md"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
          
          {isLoading && (
            <div className="flex items-center space-x-2 text-sm text-[#7d8590]">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>AI may make mistakes. Please use with discretion.</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
