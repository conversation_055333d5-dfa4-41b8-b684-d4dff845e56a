'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/lib/auth-context'
import { MarkdownContent } from './markdown-content'
import {
  Send,
  Loader2,
  User,
  Bot,
  Wrench,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { type Chat, type Message, type Version } from '@/lib/api'

interface ChatInterfaceProps {
  chat: Chat
  messages: Message[]
  onNewMessage: (message: Message) => void
  onNewVersion: (version: Version) => void
  initialPrompt?: string | null
}

interface StreamMessage {
  type: 'message' | 'tool_result' | 'completion' | 'error' | 'done'
  content?: string
  tool_name?: string
  result?: string
  version_id?: number
  files_count?: number
  version_created?: boolean
  error?: string
}

export function ChatInterface({
  chat,
  messages,
  onNewMessage,
  onNewVersion,
  initialPrompt
}: ChatInterfaceProps) {
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [streamingMessage, setStreamingMessage] = useState('')
  const [toolResults, setToolResults] = useState<string[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()
  const { token } = useAuth()
  const initialPromptSent = useRef(false)

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage, toolResults])

  useEffect(() => {
    // Send initial prompt if provided
    if (initialPrompt && !initialPromptSent.current) {
      initialPromptSent.current = true
      setInput(initialPrompt)
      setTimeout(() => {
        handleSendMessage(initialPrompt)
      }, 100)
    }
  }, [initialPrompt])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (messageContent?: string) => {
    const content = messageContent || input.trim()
    if (!content || isLoading) return

    setInput('')
    setIsLoading(true)
    setStreamingMessage('')
    setToolResults([])

    try {
      // Add user message immediately
      const userMessage: Message = {
        id: Date.now(), // Temporary ID
        chat_id: chat.id,
        content,
        role: 'user',
        created_at: new Date().toISOString()
      }
      onNewMessage(userMessage)

      // Send message to API using fetch for streaming
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/chat/${chat.id}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token || ''}`,
        },
        body: JSON.stringify({ content })
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      const decoder = new TextDecoder()
      let currentStreamingMessage = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: StreamMessage = JSON.parse(line.slice(6))

              switch (data.type) {
                case 'message':
                  currentStreamingMessage += data.content || ''
                  setStreamingMessage(currentStreamingMessage)
                  break

                case 'tool_result':
                  setToolResults(prev => [...prev, `${data.tool_name}: ${data.result}`])
                  break

                case 'completion':
                  // Add assistant message
                  const assistantMessage: Message = {
                    id: Date.now() + 1,
                    chat_id: chat.id,
                    content: currentStreamingMessage,
                    role: 'assistant',
                    created_at: new Date().toISOString()
                  }
                  onNewMessage(assistantMessage)

                  if (data.version_created && data.version_id) {
                    // Create version object
                    const newVersion: Version = {
                      id: data.version_id,
                      chat_id: chat.id,
                      version_number: data.version_id, // Simplified
                      created_at: new Date().toISOString(),
                      files: []
                    }
                    onNewVersion(newVersion)

                    toast({
                      title: "Version Created",
                      description: `Created version with ${data.files_count} files`
                    })
                  }
                  return // Exit the function

                case 'error':
                  toast({
                    title: "Error",
                    description: data.error || "Something went wrong",
                    variant: "destructive"
                  })
                  return

                case 'done':
                  return
              }
            } catch (e) {
              // Ignore JSON parse errors
            }
          }
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
      setStreamingMessage('')
      setToolResults([])
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <div className="h-full flex flex-col bg-[#0d1117]">
      {/* Chat Header */}
      <div className="p-4 border-b border-[#21262d]">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">AI</span>
          </div>
          <div>
            <h2 className="text-[#f0f6fc] font-semibold">{chat.name}</h2>
            <p className="text-[#7d8590] text-sm">Codora chat interface</p>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className="space-y-2">
            {message.role === 'user' ? (
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-[#21262d] rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="w-3 h-3 text-[#7d8590]" />
                </div>
                <div className="flex-1">
                  <div className="text-[#f0f6fc] text-sm leading-relaxed">
                    {message.content}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                    <Bot className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-[#f0f6fc] text-sm font-medium">AI Assistant</span>
                </div>
                <div className="ml-8 text-[#e6edf3] text-sm leading-relaxed">
                  <MarkdownContent content={message.content} />
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Streaming Message */}
        {streamingMessage && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                <Bot className="w-3 h-3 text-white" />
              </div>
              <span className="text-[#f0f6fc] text-sm font-medium">AI Assistant</span>
            </div>
            <div className="ml-8 text-[#e6edf3] text-sm leading-relaxed">
              <MarkdownContent content={streamingMessage} />
              <span className="inline-block w-2 h-4 bg-[#0969da] ml-1 animate-pulse" />
            </div>
          </div>
        )}

        {/* Tool Results */}
        {toolResults.map((result, index) => (
          <div key={index} className="ml-8 p-3 bg-[#161b22] border border-[#30363d] rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Wrench className="w-4 h-4 text-[#7d8590]" />
              <span className="text-sm text-[#7d8590]">
                Tool executed
              </span>
              <CheckCircle className="w-4 h-4 text-[#3fb950]" />
            </div>
            <div className="text-xs text-[#7d8590] font-mono bg-[#0d1117] p-2 rounded border border-[#21262d]">
              {result}
            </div>
          </div>
        ))}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t border-[#21262d] p-4">
        <div className="space-y-3">
          <div className="relative">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask a follow up..."
              className="w-full bg-[#0d1117] border border-[#30363d] text-[#f0f6fc] placeholder-[#7d8590] resize-none min-h-[80px] max-h-[200px] pr-12 rounded-lg focus:border-[#0969da] focus:ring-1 focus:ring-[#0969da]"
              disabled={isLoading}
            />
            <Button
              onClick={() => handleSendMessage()}
              disabled={isLoading || !input.trim()}
              className="absolute bottom-3 right-3 bg-[#0969da] hover:bg-[#0860ca] text-white p-2 h-8 w-8 rounded-md"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>

          {isLoading && (
            <div className="flex items-center space-x-2 text-sm text-[#7d8590]">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>AI may make mistakes. Please use with discretion.</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
