"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/redux";
exports.ids = ["vendor-chunks/redux"];
exports.modules = {

/***/ "(ssr)/./node_modules/redux/dist/redux.mjs":
/*!*******************************************!*\
  !*** ./node_modules/redux/dist/redux.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __DO_NOT_USE__ActionTypes: () => (/* binding */ actionTypes_default),\n/* harmony export */   applyMiddleware: () => (/* binding */ applyMiddleware),\n/* harmony export */   bindActionCreators: () => (/* binding */ bindActionCreators),\n/* harmony export */   combineReducers: () => (/* binding */ combineReducers),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   isAction: () => (/* binding */ isAction),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   legacy_createStore: () => (/* binding */ legacy_createStore)\n/* harmony export */ });\n// src/utils/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n    return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n// src/utils/symbol-observable.ts\nvar $$observable = /* @__PURE__ */ (()=>typeof Symbol === \"function\" && Symbol.observable || \"@@observable\")();\nvar symbol_observable_default = $$observable;\n// src/utils/actionTypes.ts\nvar randomString = ()=>Math.random().toString(36).substring(7).split(\"\").join(\".\");\nvar ActionTypes = {\n    INIT: `@@redux/INIT${randomString()}`,\n    REPLACE: `@@redux/REPLACE${randomString()}`,\n    PROBE_UNKNOWN_ACTION: ()=>`@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nvar actionTypes_default = ActionTypes;\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) return false;\n    let proto = obj;\n    while(Object.getPrototypeOf(proto) !== null){\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}\n// src/utils/kindOf.ts\nfunction miniKindOf(val) {\n    if (val === void 0) return \"undefined\";\n    if (val === null) return \"null\";\n    const type = typeof val;\n    switch(type){\n        case \"boolean\":\n        case \"string\":\n        case \"number\":\n        case \"symbol\":\n        case \"function\":\n            {\n                return type;\n            }\n    }\n    if (Array.isArray(val)) return \"array\";\n    if (isDate(val)) return \"date\";\n    if (isError(val)) return \"error\";\n    const constructorName = ctorName(val);\n    switch(constructorName){\n        case \"Symbol\":\n        case \"Promise\":\n        case \"WeakMap\":\n        case \"WeakSet\":\n        case \"Map\":\n        case \"Set\":\n            return constructorName;\n    }\n    return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, \"\");\n}\nfunction ctorName(val) {\n    return typeof val.constructor === \"function\" ? val.constructor.name : null;\n}\nfunction isError(val) {\n    return val instanceof Error || typeof val.message === \"string\" && val.constructor && typeof val.constructor.stackTraceLimit === \"number\";\n}\nfunction isDate(val) {\n    if (val instanceof Date) return true;\n    return typeof val.toDateString === \"function\" && typeof val.getDate === \"function\" && typeof val.setDate === \"function\";\n}\nfunction kindOf(val) {\n    let typeOfVal = typeof val;\n    if (true) {\n        typeOfVal = miniKindOf(val);\n    }\n    return typeOfVal;\n}\n// src/createStore.ts\nfunction createStore(reducer, preloadedState, enhancer) {\n    if (typeof reducer !== \"function\") {\n        throw new Error( false ? 0 : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n    }\n    if (typeof preloadedState === \"function\" && typeof enhancer === \"function\" || typeof enhancer === \"function\" && typeof arguments[3] === \"function\") {\n        throw new Error( false ? 0 : \"It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.\");\n    }\n    if (typeof preloadedState === \"function\" && typeof enhancer === \"undefined\") {\n        enhancer = preloadedState;\n        preloadedState = void 0;\n    }\n    if (typeof enhancer !== \"undefined\") {\n        if (typeof enhancer !== \"function\") {\n            throw new Error( false ? 0 : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n        }\n        return enhancer(createStore)(reducer, preloadedState);\n    }\n    let currentReducer = reducer;\n    let currentState = preloadedState;\n    let currentListeners = /* @__PURE__ */ new Map();\n    let nextListeners = currentListeners;\n    let listenerIdCounter = 0;\n    let isDispatching = false;\n    function ensureCanMutateNextListeners() {\n        if (nextListeners === currentListeners) {\n            nextListeners = /* @__PURE__ */ new Map();\n            currentListeners.forEach((listener, key)=>{\n                nextListeners.set(key, listener);\n            });\n        }\n    }\n    function getState() {\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.\");\n        }\n        return currentState;\n    }\n    function subscribe(listener) {\n        if (typeof listener !== \"function\") {\n            throw new Error( false ? 0 : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n        }\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.\");\n        }\n        let isSubscribed = true;\n        ensureCanMutateNextListeners();\n        const listenerId = listenerIdCounter++;\n        nextListeners.set(listenerId, listener);\n        return function unsubscribe() {\n            if (!isSubscribed) {\n                return;\n            }\n            if (isDispatching) {\n                throw new Error( false ? 0 : \"You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.\");\n            }\n            isSubscribed = false;\n            ensureCanMutateNextListeners();\n            nextListeners.delete(listenerId);\n            currentListeners = null;\n        };\n    }\n    function dispatch(action) {\n        if (!isPlainObject(action)) {\n            throw new Error( false ? 0 : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n        }\n        if (typeof action.type === \"undefined\") {\n            throw new Error( false ? 0 : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n        }\n        if (typeof action.type !== \"string\") {\n            throw new Error( false ? 0 : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n        }\n        if (isDispatching) {\n            throw new Error( false ? 0 : \"Reducers may not dispatch actions.\");\n        }\n        try {\n            isDispatching = true;\n            currentState = currentReducer(currentState, action);\n        } finally{\n            isDispatching = false;\n        }\n        const listeners = currentListeners = nextListeners;\n        listeners.forEach((listener)=>{\n            listener();\n        });\n        return action;\n    }\n    function replaceReducer(nextReducer) {\n        if (typeof nextReducer !== \"function\") {\n            throw new Error( false ? 0 : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n        }\n        currentReducer = nextReducer;\n        dispatch({\n            type: actionTypes_default.REPLACE\n        });\n    }\n    function observable() {\n        const outerSubscribe = subscribe;\n        return {\n            /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */ subscribe (observer) {\n                if (typeof observer !== \"object\" || observer === null) {\n                    throw new Error( false ? 0 : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n                }\n                function observeState() {\n                    const observerAsObserver = observer;\n                    if (observerAsObserver.next) {\n                        observerAsObserver.next(getState());\n                    }\n                }\n                observeState();\n                const unsubscribe = outerSubscribe(observeState);\n                return {\n                    unsubscribe\n                };\n            },\n            [symbol_observable_default] () {\n                return this;\n            }\n        };\n    }\n    dispatch({\n        type: actionTypes_default.INIT\n    });\n    const store = {\n        dispatch,\n        subscribe,\n        getState,\n        replaceReducer,\n        [symbol_observable_default]: observable\n    };\n    return store;\n}\nfunction legacy_createStore(reducer, preloadedState, enhancer) {\n    return createStore(reducer, preloadedState, enhancer);\n}\n// src/utils/warning.ts\nfunction warning(message) {\n    if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        console.error(message);\n    }\n    try {\n        throw new Error(message);\n    } catch (e) {}\n}\n// src/combineReducers.ts\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n    const reducerKeys = Object.keys(reducers);\n    const argumentName = action && action.type === actionTypes_default.INIT ? \"preloadedState argument passed to createStore\" : \"previous state received by the reducer\";\n    if (reducerKeys.length === 0) {\n        return \"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.\";\n    }\n    if (!isPlainObject(inputState)) {\n        return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following keys: \"${reducerKeys.join('\", \"')}\"`;\n    }\n    const unexpectedKeys = Object.keys(inputState).filter((key)=>!reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n    unexpectedKeys.forEach((key)=>{\n        unexpectedKeyCache[key] = true;\n    });\n    if (action && action.type === actionTypes_default.REPLACE) return;\n    if (unexpectedKeys.length > 0) {\n        return `Unexpected ${unexpectedKeys.length > 1 ? \"keys\" : \"key\"} \"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. Expected to find one of the known reducer keys instead: \"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n    }\n}\nfunction assertReducerShape(reducers) {\n    Object.keys(reducers).forEach((key)=>{\n        const reducer = reducers[key];\n        const initialState = reducer(void 0, {\n            type: actionTypes_default.INIT\n        });\n        if (typeof initialState === \"undefined\") {\n            throw new Error( false ? 0 : `The slice reducer for key \"${key}\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        if (typeof reducer(void 0, {\n            type: actionTypes_default.PROBE_UNKNOWN_ACTION()\n        }) === \"undefined\") {\n            throw new Error( false ? 0 : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. Don't try to handle '${actionTypes_default.INIT}' or other actions in \"redux/*\" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.`);\n        }\n    });\n}\nfunction combineReducers(reducers) {\n    const reducerKeys = Object.keys(reducers);\n    const finalReducers = {};\n    for(let i = 0; i < reducerKeys.length; i++){\n        const key = reducerKeys[i];\n        if (true) {\n            if (typeof reducers[key] === \"undefined\") {\n                warning(`No reducer provided for key \"${key}\"`);\n            }\n        }\n        if (typeof reducers[key] === \"function\") {\n            finalReducers[key] = reducers[key];\n        }\n    }\n    const finalReducerKeys = Object.keys(finalReducers);\n    let unexpectedKeyCache;\n    if (true) {\n        unexpectedKeyCache = {};\n    }\n    let shapeAssertionError;\n    try {\n        assertReducerShape(finalReducers);\n    } catch (e) {\n        shapeAssertionError = e;\n    }\n    return function combination(state = {}, action) {\n        if (shapeAssertionError) {\n            throw shapeAssertionError;\n        }\n        if (true) {\n            const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n            if (warningMessage) {\n                warning(warningMessage);\n            }\n        }\n        let hasChanged = false;\n        const nextState = {};\n        for(let i = 0; i < finalReducerKeys.length; i++){\n            const key = finalReducerKeys[i];\n            const reducer = finalReducers[key];\n            const previousStateForKey = state[key];\n            const nextStateForKey = reducer(previousStateForKey, action);\n            if (typeof nextStateForKey === \"undefined\") {\n                const actionType = action && action.type;\n                throw new Error( false ? 0 : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : \"(unknown type)\"}, the slice reducer for key \"${key}\" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.`);\n            }\n            nextState[key] = nextStateForKey;\n            hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n        }\n        hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n        return hasChanged ? nextState : state;\n    };\n}\n// src/bindActionCreators.ts\nfunction bindActionCreator(actionCreator, dispatch) {\n    return function(...args) {\n        return dispatch(actionCreator.apply(this, args));\n    };\n}\nfunction bindActionCreators(actionCreators, dispatch) {\n    if (typeof actionCreators === \"function\") {\n        return bindActionCreator(actionCreators, dispatch);\n    }\n    if (typeof actionCreators !== \"object\" || actionCreators === null) {\n        throw new Error( false ? 0 : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n    }\n    const boundActionCreators = {};\n    for(const key in actionCreators){\n        const actionCreator = actionCreators[key];\n        if (typeof actionCreator === \"function\") {\n            boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n        }\n    }\n    return boundActionCreators;\n}\n// src/compose.ts\nfunction compose(...funcs) {\n    if (funcs.length === 0) {\n        return (arg)=>arg;\n    }\n    if (funcs.length === 1) {\n        return funcs[0];\n    }\n    return funcs.reduce((a, b)=>(...args)=>a(b(...args)));\n}\n// src/applyMiddleware.ts\nfunction applyMiddleware(...middlewares) {\n    return (createStore2)=>(reducer, preloadedState)=>{\n            const store = createStore2(reducer, preloadedState);\n            let dispatch = ()=>{\n                throw new Error( false ? 0 : \"Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.\");\n            };\n            const middlewareAPI = {\n                getState: store.getState,\n                dispatch: (action, ...args)=>dispatch(action, ...args)\n            };\n            const chain = middlewares.map((middleware)=>middleware(middlewareAPI));\n            dispatch = compose(...chain)(store.dispatch);\n            return {\n                ...store,\n                dispatch\n            };\n        };\n}\n// src/utils/isAction.ts\nfunction isAction(action) {\n    return isPlainObject(action) && \"type\" in action && typeof action.type === \"string\";\n}\n //# sourceMappingURL=redux.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/redux/dist/redux.mjs\n");

/***/ })

};
;