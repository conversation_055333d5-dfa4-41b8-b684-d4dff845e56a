"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-table";
exports.ids = ["vendor-chunks/markdown-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/markdown-table/index.js":
/*!**********************************************!*\
  !*** ./node_modules/markdown-table/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   markdownTable: () => (/* binding */ markdownTable)\n/* harmony export */ });\n// To do: next major: remove.\n/**\n * @typedef {Options} MarkdownTableOptions\n *   Configuration.\n */ /**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [alignDelimiters=true]\n *   Whether to align the delimiters (default: `true`);\n *   they are aligned by default:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {ReadonlyArray<string | null | undefined> | string | null | undefined} [align]\n *   How to align columns (default: `''`);\n *   one style for all columns or styles for their respective columns;\n *   each style is either `'l'` (left), `'r'` (right), or `'c'` (center);\n *   other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left;\n *   *only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean | null | undefined} [delimiterEnd=true]\n *   Whether to end each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean | null | undefined} [delimiterStart=true]\n *   Whether to begin each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean | null | undefined} [padding=true]\n *   Whether to add a space of padding between delimiters and cells\n *   (default: `true`).\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {((value: string) => number) | null | undefined} [stringLength]\n *   Function to detect the length of table cell content (optional);\n *   this is used when aligning the delimiters (`|`) between table cells;\n *   full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source;\n *   to fix this, you can pass this function,\n *   which receives the cell content and returns its “visible” size;\n *   note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */ /**\n * @param {string} value\n *   Cell value.\n * @returns {number}\n *   Cell size.\n */ function defaultStringLength(value) {\n    return value.length;\n}\n/**\n * Generate a markdown\n * ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables))\n * table.\n *\n * @param {ReadonlyArray<ReadonlyArray<string | null | undefined>>} table\n *   Table data (matrix of strings).\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Result.\n */ function markdownTable(table, options) {\n    const settings = options || {};\n    // To do: next major: change to spread.\n    const align = (settings.align || []).concat();\n    const stringLength = settings.stringLength || defaultStringLength;\n    /** @type {Array<number>} Character codes as symbols for alignment per column. */ const alignments = [];\n    /** @type {Array<Array<string>>} Cells per row. */ const cellMatrix = [];\n    /** @type {Array<Array<number>>} Sizes of each cell per row. */ const sizeMatrix = [];\n    /** @type {Array<number>} */ const longestCellByColumn = [];\n    let mostCellsPerRow = 0;\n    let rowIndex = -1;\n    // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n    // do superfluous work when aligning, so optimize for aligning.\n    while(++rowIndex < table.length){\n        /** @type {Array<string>} */ const row = [];\n        /** @type {Array<number>} */ const sizes = [];\n        let columnIndex = -1;\n        if (table[rowIndex].length > mostCellsPerRow) {\n            mostCellsPerRow = table[rowIndex].length;\n        }\n        while(++columnIndex < table[rowIndex].length){\n            const cell = serialize(table[rowIndex][columnIndex]);\n            if (settings.alignDelimiters !== false) {\n                const size = stringLength(cell);\n                sizes[columnIndex] = size;\n                if (longestCellByColumn[columnIndex] === undefined || size > longestCellByColumn[columnIndex]) {\n                    longestCellByColumn[columnIndex] = size;\n                }\n            }\n            row.push(cell);\n        }\n        cellMatrix[rowIndex] = row;\n        sizeMatrix[rowIndex] = sizes;\n    }\n    // Figure out which alignments to use.\n    let columnIndex = -1;\n    if (typeof align === \"object\" && \"length\" in align) {\n        while(++columnIndex < mostCellsPerRow){\n            alignments[columnIndex] = toAlignment(align[columnIndex]);\n        }\n    } else {\n        const code = toAlignment(align);\n        while(++columnIndex < mostCellsPerRow){\n            alignments[columnIndex] = code;\n        }\n    }\n    // Inject the alignment row.\n    columnIndex = -1;\n    /** @type {Array<string>} */ const row = [];\n    /** @type {Array<number>} */ const sizes = [];\n    while(++columnIndex < mostCellsPerRow){\n        const code = alignments[columnIndex];\n        let before = \"\";\n        let after = \"\";\n        if (code === 99 /* `c` */ ) {\n            before = \":\";\n            after = \":\";\n        } else if (code === 108 /* `l` */ ) {\n            before = \":\";\n        } else if (code === 114 /* `r` */ ) {\n            after = \":\";\n        }\n        // There *must* be at least one hyphen-minus in each alignment cell.\n        let size = settings.alignDelimiters === false ? 1 : Math.max(1, longestCellByColumn[columnIndex] - before.length - after.length);\n        const cell = before + \"-\".repeat(size) + after;\n        if (settings.alignDelimiters !== false) {\n            size = before.length + size + after.length;\n            if (size > longestCellByColumn[columnIndex]) {\n                longestCellByColumn[columnIndex] = size;\n            }\n            sizes[columnIndex] = size;\n        }\n        row[columnIndex] = cell;\n    }\n    // Inject the alignment row.\n    cellMatrix.splice(1, 0, row);\n    sizeMatrix.splice(1, 0, sizes);\n    rowIndex = -1;\n    /** @type {Array<string>} */ const lines = [];\n    while(++rowIndex < cellMatrix.length){\n        const row = cellMatrix[rowIndex];\n        const sizes = sizeMatrix[rowIndex];\n        columnIndex = -1;\n        /** @type {Array<string>} */ const line = [];\n        while(++columnIndex < mostCellsPerRow){\n            const cell = row[columnIndex] || \"\";\n            let before = \"\";\n            let after = \"\";\n            if (settings.alignDelimiters !== false) {\n                const size = longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0);\n                const code = alignments[columnIndex];\n                if (code === 114 /* `r` */ ) {\n                    before = \" \".repeat(size);\n                } else if (code === 99 /* `c` */ ) {\n                    if (size % 2) {\n                        before = \" \".repeat(size / 2 + 0.5);\n                        after = \" \".repeat(size / 2 - 0.5);\n                    } else {\n                        before = \" \".repeat(size / 2);\n                        after = before;\n                    }\n                } else {\n                    after = \" \".repeat(size);\n                }\n            }\n            if (settings.delimiterStart !== false && !columnIndex) {\n                line.push(\"|\");\n            }\n            if (settings.padding !== false && // Don’t add the opening space if we’re not aligning and the cell is\n            // empty: there will be a closing space.\n            !(settings.alignDelimiters === false && cell === \"\") && (settings.delimiterStart !== false || columnIndex)) {\n                line.push(\" \");\n            }\n            if (settings.alignDelimiters !== false) {\n                line.push(before);\n            }\n            line.push(cell);\n            if (settings.alignDelimiters !== false) {\n                line.push(after);\n            }\n            if (settings.padding !== false) {\n                line.push(\" \");\n            }\n            if (settings.delimiterEnd !== false || columnIndex !== mostCellsPerRow - 1) {\n                line.push(\"|\");\n            }\n        }\n        lines.push(settings.delimiterEnd === false ? line.join(\"\").replace(/ +$/, \"\") : line.join(\"\"));\n    }\n    return lines.join(\"\\n\");\n}\n/**\n * @param {string | null | undefined} [value]\n *   Value to serialize.\n * @returns {string}\n *   Result.\n */ function serialize(value) {\n    return value === null || value === undefined ? \"\" : String(value);\n}\n/**\n * @param {string | null | undefined} value\n *   Value.\n * @returns {number}\n *   Alignment.\n */ function toAlignment(value) {\n    const code = typeof value === \"string\" ? value.codePointAt(0) : 0;\n    return code === 67 /* `C` */  || code === 99 /* `c` */  ? 99 /* `c` */  : code === 76 /* `L` */  || code === 108 /* `l` */  ? 108 /* `l` */  : code === 82 /* `R` */  || code === 114 /* `r` */  ? 114 /* `r` */  : 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWFya2Rvd24tdGFibGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUE2QjtBQUM3Qjs7O0NBR0MsR0FFRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTJJQyxHQUVEOzs7OztDQUtDLEdBQ0QsU0FBU0Esb0JBQW9CQyxLQUFLO0lBQ2hDLE9BQU9BLE1BQU1DLE1BQU07QUFDckI7QUFFQTs7Ozs7Ozs7Ozs7Q0FXQyxHQUNNLFNBQVNDLGNBQWNDLEtBQUssRUFBRUMsT0FBTztJQUMxQyxNQUFNQyxXQUFXRCxXQUFXLENBQUM7SUFDN0IsdUNBQXVDO0lBQ3ZDLE1BQU1FLFFBQVEsQ0FBQ0QsU0FBU0MsS0FBSyxJQUFJLEVBQUUsRUFBRUMsTUFBTTtJQUMzQyxNQUFNQyxlQUFlSCxTQUFTRyxZQUFZLElBQUlUO0lBQzlDLCtFQUErRSxHQUMvRSxNQUFNVSxhQUFhLEVBQUU7SUFDckIsZ0RBQWdELEdBQ2hELE1BQU1DLGFBQWEsRUFBRTtJQUNyQiw2REFBNkQsR0FDN0QsTUFBTUMsYUFBYSxFQUFFO0lBQ3JCLDBCQUEwQixHQUMxQixNQUFNQyxzQkFBc0IsRUFBRTtJQUM5QixJQUFJQyxrQkFBa0I7SUFDdEIsSUFBSUMsV0FBVyxDQUFDO0lBRWhCLDhFQUE4RTtJQUM5RSwrREFBK0Q7SUFDL0QsTUFBTyxFQUFFQSxXQUFXWCxNQUFNRixNQUFNLENBQUU7UUFDaEMsMEJBQTBCLEdBQzFCLE1BQU1jLE1BQU0sRUFBRTtRQUNkLDBCQUEwQixHQUMxQixNQUFNQyxRQUFRLEVBQUU7UUFDaEIsSUFBSUMsY0FBYyxDQUFDO1FBRW5CLElBQUlkLEtBQUssQ0FBQ1csU0FBUyxDQUFDYixNQUFNLEdBQUdZLGlCQUFpQjtZQUM1Q0Esa0JBQWtCVixLQUFLLENBQUNXLFNBQVMsQ0FBQ2IsTUFBTTtRQUMxQztRQUVBLE1BQU8sRUFBRWdCLGNBQWNkLEtBQUssQ0FBQ1csU0FBUyxDQUFDYixNQUFNLENBQUU7WUFDN0MsTUFBTWlCLE9BQU9DLFVBQVVoQixLQUFLLENBQUNXLFNBQVMsQ0FBQ0csWUFBWTtZQUVuRCxJQUFJWixTQUFTZSxlQUFlLEtBQUssT0FBTztnQkFDdEMsTUFBTUMsT0FBT2IsYUFBYVU7Z0JBQzFCRixLQUFLLENBQUNDLFlBQVksR0FBR0k7Z0JBRXJCLElBQ0VULG1CQUFtQixDQUFDSyxZQUFZLEtBQUtLLGFBQ3JDRCxPQUFPVCxtQkFBbUIsQ0FBQ0ssWUFBWSxFQUN2QztvQkFDQUwsbUJBQW1CLENBQUNLLFlBQVksR0FBR0k7Z0JBQ3JDO1lBQ0Y7WUFFQU4sSUFBSVEsSUFBSSxDQUFDTDtRQUNYO1FBRUFSLFVBQVUsQ0FBQ0ksU0FBUyxHQUFHQztRQUN2QkosVUFBVSxDQUFDRyxTQUFTLEdBQUdFO0lBQ3pCO0lBRUEsc0NBQXNDO0lBQ3RDLElBQUlDLGNBQWMsQ0FBQztJQUVuQixJQUFJLE9BQU9YLFVBQVUsWUFBWSxZQUFZQSxPQUFPO1FBQ2xELE1BQU8sRUFBRVcsY0FBY0osZ0JBQWlCO1lBQ3RDSixVQUFVLENBQUNRLFlBQVksR0FBR08sWUFBWWxCLEtBQUssQ0FBQ1csWUFBWTtRQUMxRDtJQUNGLE9BQU87UUFDTCxNQUFNUSxPQUFPRCxZQUFZbEI7UUFFekIsTUFBTyxFQUFFVyxjQUFjSixnQkFBaUI7WUFDdENKLFVBQVUsQ0FBQ1EsWUFBWSxHQUFHUTtRQUM1QjtJQUNGO0lBRUEsNEJBQTRCO0lBQzVCUixjQUFjLENBQUM7SUFDZiwwQkFBMEIsR0FDMUIsTUFBTUYsTUFBTSxFQUFFO0lBQ2QsMEJBQTBCLEdBQzFCLE1BQU1DLFFBQVEsRUFBRTtJQUVoQixNQUFPLEVBQUVDLGNBQWNKLGdCQUFpQjtRQUN0QyxNQUFNWSxPQUFPaEIsVUFBVSxDQUFDUSxZQUFZO1FBQ3BDLElBQUlTLFNBQVM7UUFDYixJQUFJQyxRQUFRO1FBRVosSUFBSUYsU0FBUyxHQUFHLE9BQU8sS0FBSTtZQUN6QkMsU0FBUztZQUNUQyxRQUFRO1FBQ1YsT0FBTyxJQUFJRixTQUFTLElBQUksT0FBTyxLQUFJO1lBQ2pDQyxTQUFTO1FBQ1gsT0FBTyxJQUFJRCxTQUFTLElBQUksT0FBTyxLQUFJO1lBQ2pDRSxRQUFRO1FBQ1Y7UUFFQSxvRUFBb0U7UUFDcEUsSUFBSU4sT0FDRmhCLFNBQVNlLGVBQWUsS0FBSyxRQUN6QixJQUNBUSxLQUFLQyxHQUFHLENBQ04sR0FDQWpCLG1CQUFtQixDQUFDSyxZQUFZLEdBQUdTLE9BQU96QixNQUFNLEdBQUcwQixNQUFNMUIsTUFBTTtRQUd2RSxNQUFNaUIsT0FBT1EsU0FBUyxJQUFJSSxNQUFNLENBQUNULFFBQVFNO1FBRXpDLElBQUl0QixTQUFTZSxlQUFlLEtBQUssT0FBTztZQUN0Q0MsT0FBT0ssT0FBT3pCLE1BQU0sR0FBR29CLE9BQU9NLE1BQU0xQixNQUFNO1lBRTFDLElBQUlvQixPQUFPVCxtQkFBbUIsQ0FBQ0ssWUFBWSxFQUFFO2dCQUMzQ0wsbUJBQW1CLENBQUNLLFlBQVksR0FBR0k7WUFDckM7WUFFQUwsS0FBSyxDQUFDQyxZQUFZLEdBQUdJO1FBQ3ZCO1FBRUFOLEdBQUcsQ0FBQ0UsWUFBWSxHQUFHQztJQUNyQjtJQUVBLDRCQUE0QjtJQUM1QlIsV0FBV3FCLE1BQU0sQ0FBQyxHQUFHLEdBQUdoQjtJQUN4QkosV0FBV29CLE1BQU0sQ0FBQyxHQUFHLEdBQUdmO0lBRXhCRixXQUFXLENBQUM7SUFDWiwwQkFBMEIsR0FDMUIsTUFBTWtCLFFBQVEsRUFBRTtJQUVoQixNQUFPLEVBQUVsQixXQUFXSixXQUFXVCxNQUFNLENBQUU7UUFDckMsTUFBTWMsTUFBTUwsVUFBVSxDQUFDSSxTQUFTO1FBQ2hDLE1BQU1FLFFBQVFMLFVBQVUsQ0FBQ0csU0FBUztRQUNsQ0csY0FBYyxDQUFDO1FBQ2YsMEJBQTBCLEdBQzFCLE1BQU1nQixPQUFPLEVBQUU7UUFFZixNQUFPLEVBQUVoQixjQUFjSixnQkFBaUI7WUFDdEMsTUFBTUssT0FBT0gsR0FBRyxDQUFDRSxZQUFZLElBQUk7WUFDakMsSUFBSVMsU0FBUztZQUNiLElBQUlDLFFBQVE7WUFFWixJQUFJdEIsU0FBU2UsZUFBZSxLQUFLLE9BQU87Z0JBQ3RDLE1BQU1DLE9BQ0pULG1CQUFtQixDQUFDSyxZQUFZLEdBQUlELENBQUFBLEtBQUssQ0FBQ0MsWUFBWSxJQUFJO2dCQUM1RCxNQUFNUSxPQUFPaEIsVUFBVSxDQUFDUSxZQUFZO2dCQUVwQyxJQUFJUSxTQUFTLElBQUksT0FBTyxLQUFJO29CQUMxQkMsU0FBUyxJQUFJSSxNQUFNLENBQUNUO2dCQUN0QixPQUFPLElBQUlJLFNBQVMsR0FBRyxPQUFPLEtBQUk7b0JBQ2hDLElBQUlKLE9BQU8sR0FBRzt3QkFDWkssU0FBUyxJQUFJSSxNQUFNLENBQUNULE9BQU8sSUFBSTt3QkFDL0JNLFFBQVEsSUFBSUcsTUFBTSxDQUFDVCxPQUFPLElBQUk7b0JBQ2hDLE9BQU87d0JBQ0xLLFNBQVMsSUFBSUksTUFBTSxDQUFDVCxPQUFPO3dCQUMzQk0sUUFBUUQ7b0JBQ1Y7Z0JBQ0YsT0FBTztvQkFDTEMsUUFBUSxJQUFJRyxNQUFNLENBQUNUO2dCQUNyQjtZQUNGO1lBRUEsSUFBSWhCLFNBQVM2QixjQUFjLEtBQUssU0FBUyxDQUFDakIsYUFBYTtnQkFDckRnQixLQUFLVixJQUFJLENBQUM7WUFDWjtZQUVBLElBQ0VsQixTQUFTOEIsT0FBTyxLQUFLLFNBQ3JCLG9FQUFvRTtZQUNwRSx3Q0FBd0M7WUFDeEMsQ0FBRTlCLENBQUFBLFNBQVNlLGVBQWUsS0FBSyxTQUFTRixTQUFTLEVBQUMsS0FDakRiLENBQUFBLFNBQVM2QixjQUFjLEtBQUssU0FBU2pCLFdBQVUsR0FDaEQ7Z0JBQ0FnQixLQUFLVixJQUFJLENBQUM7WUFDWjtZQUVBLElBQUlsQixTQUFTZSxlQUFlLEtBQUssT0FBTztnQkFDdENhLEtBQUtWLElBQUksQ0FBQ0c7WUFDWjtZQUVBTyxLQUFLVixJQUFJLENBQUNMO1lBRVYsSUFBSWIsU0FBU2UsZUFBZSxLQUFLLE9BQU87Z0JBQ3RDYSxLQUFLVixJQUFJLENBQUNJO1lBQ1o7WUFFQSxJQUFJdEIsU0FBUzhCLE9BQU8sS0FBSyxPQUFPO2dCQUM5QkYsS0FBS1YsSUFBSSxDQUFDO1lBQ1o7WUFFQSxJQUNFbEIsU0FBUytCLFlBQVksS0FBSyxTQUMxQm5CLGdCQUFnQkosa0JBQWtCLEdBQ2xDO2dCQUNBb0IsS0FBS1YsSUFBSSxDQUFDO1lBQ1o7UUFDRjtRQUVBUyxNQUFNVCxJQUFJLENBQ1JsQixTQUFTK0IsWUFBWSxLQUFLLFFBQ3RCSCxLQUFLSSxJQUFJLENBQUMsSUFBSUMsT0FBTyxDQUFDLE9BQU8sTUFDN0JMLEtBQUtJLElBQUksQ0FBQztJQUVsQjtJQUVBLE9BQU9MLE1BQU1LLElBQUksQ0FBQztBQUNwQjtBQUVBOzs7OztDQUtDLEdBQ0QsU0FBU2xCLFVBQVVuQixLQUFLO0lBQ3RCLE9BQU9BLFVBQVUsUUFBUUEsVUFBVXNCLFlBQVksS0FBS2lCLE9BQU92QztBQUM3RDtBQUVBOzs7OztDQUtDLEdBQ0QsU0FBU3dCLFlBQVl4QixLQUFLO0lBQ3hCLE1BQU15QixPQUFPLE9BQU96QixVQUFVLFdBQVdBLE1BQU13QyxXQUFXLENBQUMsS0FBSztJQUVoRSxPQUFPZixTQUFTLEdBQUcsT0FBTyxPQUFNQSxTQUFTLEdBQUcsT0FBTyxNQUMvQyxHQUFHLE9BQU8sTUFDVkEsU0FBUyxHQUFHLE9BQU8sT0FBTUEsU0FBUyxJQUFJLE9BQU8sTUFDM0MsSUFBSSxPQUFPLE1BQ1hBLFNBQVMsR0FBRyxPQUFPLE9BQU1BLFNBQVMsSUFBSSxPQUFPLE1BQzNDLElBQUksT0FBTyxNQUNYO0FBQ1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWFya2Rvd24tdGFibGUvaW5kZXguanM/OTVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUbyBkbzogbmV4dCBtYWpvcjogcmVtb3ZlLlxuLyoqXG4gKiBAdHlwZWRlZiB7T3B0aW9uc30gTWFya2Rvd25UYWJsZU9wdGlvbnNcbiAqICAgQ29uZmlndXJhdGlvbi5cbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIE9wdGlvbnNcbiAqICAgQ29uZmlndXJhdGlvbi5cbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbiB8IG51bGwgfCB1bmRlZmluZWR9IFthbGlnbkRlbGltaXRlcnM9dHJ1ZV1cbiAqICAgV2hldGhlciB0byBhbGlnbiB0aGUgZGVsaW1pdGVycyAoZGVmYXVsdDogYHRydWVgKTtcbiAqICAgdGhleSBhcmUgYWxpZ25lZCBieSBkZWZhdWx0OlxuICpcbiAqICAgYGBgbWFya2Rvd25cbiAqICAgfCBBbHBoYSB8IEIgICAgIHxcbiAqICAgfCAtLS0tLSB8IC0tLS0tIHxcbiAqICAgfCBDICAgICB8IERlbHRhIHxcbiAqICAgYGBgXG4gKlxuICogICBQYXNzIGBmYWxzZWAgdG8gbWFrZSB0aGVtIHN0YWdnZXJlZDpcbiAqXG4gKiAgIGBgYG1hcmtkb3duXG4gKiAgIHwgQWxwaGEgfCBCIHxcbiAqICAgfCAtIHwgLSB8XG4gKiAgIHwgQyB8IERlbHRhIHxcbiAqICAgYGBgXG4gKiBAcHJvcGVydHkge1JlYWRvbmx5QXJyYXk8c3RyaW5nIHwgbnVsbCB8IHVuZGVmaW5lZD4gfCBzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbYWxpZ25dXG4gKiAgIEhvdyB0byBhbGlnbiBjb2x1bW5zIChkZWZhdWx0OiBgJydgKTtcbiAqICAgb25lIHN0eWxlIGZvciBhbGwgY29sdW1ucyBvciBzdHlsZXMgZm9yIHRoZWlyIHJlc3BlY3RpdmUgY29sdW1ucztcbiAqICAgZWFjaCBzdHlsZSBpcyBlaXRoZXIgYCdsJ2AgKGxlZnQpLCBgJ3InYCAocmlnaHQpLCBvciBgJ2MnYCAoY2VudGVyKTtcbiAqICAgb3RoZXIgdmFsdWVzIGFyZSB0cmVhdGVkIGFzIGAnJ2AsIHdoaWNoIGRvZXNu4oCZdCBwbGFjZSB0aGUgY29sb24gaW4gdGhlXG4gKiAgIGFsaWdubWVudCByb3cgYnV0IGRvZXMgYWxpZ24gbGVmdDtcbiAqICAgKm9ubHkgdGhlIGxvd2VyY2FzZWQgZmlyc3QgY2hhcmFjdGVyIGlzIHVzZWQsIHNvIGBSaWdodGAgaXMgZmluZS4qXG4gKiBAcHJvcGVydHkge2Jvb2xlYW4gfCBudWxsIHwgdW5kZWZpbmVkfSBbZGVsaW1pdGVyRW5kPXRydWVdXG4gKiAgIFdoZXRoZXIgdG8gZW5kIGVhY2ggcm93IHdpdGggdGhlIGRlbGltaXRlciAoZGVmYXVsdDogYHRydWVgKS5cbiAqXG4gKiAgID4g8J+RiSAqKk5vdGUqKjogcGxlYXNlIGRvbuKAmXQgdXNlIHRoaXM6IGl0IGNvdWxkIGNyZWF0ZSBmcmFnaWxlIHN0cnVjdHVyZXNcbiAqICAgPiB0aGF0IGFyZW7igJl0IHVuZGVyc3RhbmRhYmxlIHRvIHNvbWUgbWFya2Rvd24gcGFyc2Vycy5cbiAqXG4gKiAgIFdoZW4gYHRydWVgLCB0aGVyZSBhcmUgZW5kaW5nIGRlbGltaXRlcnM6XG4gKlxuICogICBgYGBtYXJrZG93blxuICogICB8IEFscGhhIHwgQiAgICAgfFxuICogICB8IC0tLS0tIHwgLS0tLS0gfFxuICogICB8IEMgICAgIHwgRGVsdGEgfFxuICogICBgYGBcbiAqXG4gKiAgIFdoZW4gYGZhbHNlYCwgdGhlcmUgYXJlIG5vIGVuZGluZyBkZWxpbWl0ZXJzOlxuICpcbiAqICAgYGBgbWFya2Rvd25cbiAqICAgfCBBbHBoYSB8IEJcbiAqICAgfCAtLS0tLSB8IC0tLS0tXG4gKiAgIHwgQyAgICAgfCBEZWx0YVxuICogICBgYGBcbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbiB8IG51bGwgfCB1bmRlZmluZWR9IFtkZWxpbWl0ZXJTdGFydD10cnVlXVxuICogICBXaGV0aGVyIHRvIGJlZ2luIGVhY2ggcm93IHdpdGggdGhlIGRlbGltaXRlciAoZGVmYXVsdDogYHRydWVgKS5cbiAqXG4gKiAgID4g8J+RiSAqKk5vdGUqKjogcGxlYXNlIGRvbuKAmXQgdXNlIHRoaXM6IGl0IGNvdWxkIGNyZWF0ZSBmcmFnaWxlIHN0cnVjdHVyZXNcbiAqICAgPiB0aGF0IGFyZW7igJl0IHVuZGVyc3RhbmRhYmxlIHRvIHNvbWUgbWFya2Rvd24gcGFyc2Vycy5cbiAqXG4gKiAgIFdoZW4gYHRydWVgLCB0aGVyZSBhcmUgc3RhcnRpbmcgZGVsaW1pdGVyczpcbiAqXG4gKiAgIGBgYG1hcmtkb3duXG4gKiAgIHwgQWxwaGEgfCBCICAgICB8XG4gKiAgIHwgLS0tLS0gfCAtLS0tLSB8XG4gKiAgIHwgQyAgICAgfCBEZWx0YSB8XG4gKiAgIGBgYFxuICpcbiAqICAgV2hlbiBgZmFsc2VgLCB0aGVyZSBhcmUgbm8gc3RhcnRpbmcgZGVsaW1pdGVyczpcbiAqXG4gKiAgIGBgYG1hcmtkb3duXG4gKiAgIEFscGhhIHwgQiAgICAgfFxuICogICAtLS0tLSB8IC0tLS0tIHxcbiAqICAgQyAgICAgfCBEZWx0YSB8XG4gKiAgIGBgYFxuICogQHByb3BlcnR5IHtib29sZWFuIHwgbnVsbCB8IHVuZGVmaW5lZH0gW3BhZGRpbmc9dHJ1ZV1cbiAqICAgV2hldGhlciB0byBhZGQgYSBzcGFjZSBvZiBwYWRkaW5nIGJldHdlZW4gZGVsaW1pdGVycyBhbmQgY2VsbHNcbiAqICAgKGRlZmF1bHQ6IGB0cnVlYCkuXG4gKlxuICogICBXaGVuIGB0cnVlYCwgdGhlcmUgaXMgcGFkZGluZzpcbiAqXG4gKiAgIGBgYG1hcmtkb3duXG4gKiAgIHwgQWxwaGEgfCBCICAgICB8XG4gKiAgIHwgLS0tLS0gfCAtLS0tLSB8XG4gKiAgIHwgQyAgICAgfCBEZWx0YSB8XG4gKiAgIGBgYFxuICpcbiAqICAgV2hlbiBgZmFsc2VgLCB0aGVyZSBpcyBubyBwYWRkaW5nOlxuICpcbiAqICAgYGBgbWFya2Rvd25cbiAqICAgfEFscGhhfEIgICAgfFxuICogICB8LS0tLS18LS0tLS18XG4gKiAgIHxDICAgIHxEZWx0YXxcbiAqICAgYGBgXG4gKiBAcHJvcGVydHkgeygodmFsdWU6IHN0cmluZykgPT4gbnVtYmVyKSB8IG51bGwgfCB1bmRlZmluZWR9IFtzdHJpbmdMZW5ndGhdXG4gKiAgIEZ1bmN0aW9uIHRvIGRldGVjdCB0aGUgbGVuZ3RoIG9mIHRhYmxlIGNlbGwgY29udGVudCAob3B0aW9uYWwpO1xuICogICB0aGlzIGlzIHVzZWQgd2hlbiBhbGlnbmluZyB0aGUgZGVsaW1pdGVycyAoYHxgKSBiZXR3ZWVuIHRhYmxlIGNlbGxzO1xuICogICBmdWxsLXdpZHRoIGNoYXJhY3RlcnMgYW5kIGVtb2ppIG1lc3MgdXAgZGVsaW1pdGVyIGFsaWdubWVudCB3aGVuIHZpZXdpbmdcbiAqICAgdGhlIG1hcmtkb3duIHNvdXJjZTtcbiAqICAgdG8gZml4IHRoaXMsIHlvdSBjYW4gcGFzcyB0aGlzIGZ1bmN0aW9uLFxuICogICB3aGljaCByZWNlaXZlcyB0aGUgY2VsbCBjb250ZW50IGFuZCByZXR1cm5zIGl0cyDigJx2aXNpYmxl4oCdIHNpemU7XG4gKiAgIG5vdGUgdGhhdCB3aGF0IGlzIGFuZCBpc27igJl0IHZpc2libGUgZGVwZW5kcyBvbiB3aGVyZSB0aGUgdGV4dCBpcyBkaXNwbGF5ZWQuXG4gKlxuICogICBXaXRob3V0IHN1Y2ggYSBmdW5jdGlvbiwgdGhlIGZvbGxvd2luZzpcbiAqXG4gKiAgIGBgYGpzXG4gKiAgIG1hcmtkb3duVGFibGUoW1xuICogICAgIFsnQWxwaGEnLCAnQnJhdm8nXSxcbiAqICAgICBbJ+S4reaWhycsICdDaGFybGllJ10sXG4gKiAgICAgWyfwn5Gp4oCN4p2k77iP4oCN8J+RqScsICdEZWx0YSddXG4gKiAgIF0pXG4gKiAgIGBgYFxuICpcbiAqICAgWWllbGRzOlxuICpcbiAqICAgYGBgbWFya2Rvd25cbiAqICAgfCBBbHBoYSB8IEJyYXZvIHxcbiAqICAgfCAtIHwgLSB8XG4gKiAgIHwg5Lit5paHIHwgQ2hhcmxpZSB8XG4gKiAgIHwg8J+RqeKAjeKdpO+4j+KAjfCfkakgfCBEZWx0YSB8XG4gKiAgIGBgYFxuICpcbiAqICAgV2l0aCBbYHN0cmluZy13aWR0aGBdKGh0dHBzOi8vZ2l0aHViLmNvbS9zaW5kcmVzb3JodXMvc3RyaW5nLXdpZHRoKTpcbiAqXG4gKiAgIGBgYGpzXG4gKiAgIGltcG9ydCBzdHJpbmdXaWR0aCBmcm9tICdzdHJpbmctd2lkdGgnXG4gKlxuICogICBtYXJrZG93blRhYmxlKFxuICogICAgIFtcbiAqICAgICAgIFsnQWxwaGEnLCAnQnJhdm8nXSxcbiAqICAgICAgIFsn5Lit5paHJywgJ0NoYXJsaWUnXSxcbiAqICAgICAgIFsn8J+RqeKAjeKdpO+4j+KAjfCfkaknLCAnRGVsdGEnXVxuICogICAgIF0sXG4gKiAgICAge3N0cmluZ0xlbmd0aDogc3RyaW5nV2lkdGh9XG4gKiAgIClcbiAqICAgYGBgXG4gKlxuICogICBZaWVsZHM6XG4gKlxuICogICBgYGBtYXJrZG93blxuICogICB8IEFscGhhIHwgQnJhdm8gICB8XG4gKiAgIHwgLS0tLS0gfCAtLS0tLS0tIHxcbiAqICAgfCDkuK3mlocgIHwgQ2hhcmxpZSB8XG4gKiAgIHwg8J+RqeKAjeKdpO+4j+KAjfCfkakgICAgfCBEZWx0YSAgIHxcbiAqICAgYGBgXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAqICAgQ2VsbCB2YWx1ZS5cbiAqIEByZXR1cm5zIHtudW1iZXJ9XG4gKiAgIENlbGwgc2l6ZS5cbiAqL1xuZnVuY3Rpb24gZGVmYXVsdFN0cmluZ0xlbmd0aCh2YWx1ZSkge1xuICByZXR1cm4gdmFsdWUubGVuZ3RoXG59XG5cbi8qKlxuICogR2VuZXJhdGUgYSBtYXJrZG93blxuICogKFtHRk1dKGh0dHBzOi8vZG9jcy5naXRodWIuY29tL2VuL2dpdGh1Yi93cml0aW5nLW9uLWdpdGh1Yi93b3JraW5nLXdpdGgtYWR2YW5jZWQtZm9ybWF0dGluZy9vcmdhbml6aW5nLWluZm9ybWF0aW9uLXdpdGgtdGFibGVzKSlcbiAqIHRhYmxlLlxuICpcbiAqIEBwYXJhbSB7UmVhZG9ubHlBcnJheTxSZWFkb25seUFycmF5PHN0cmluZyB8IG51bGwgfCB1bmRlZmluZWQ+Pn0gdGFibGVcbiAqICAgVGFibGUgZGF0YSAobWF0cml4IG9mIHN0cmluZ3MpLlxuICogQHBhcmFtIHtSZWFkb25seTxPcHRpb25zPiB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uIChvcHRpb25hbCkuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBSZXN1bHQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtYXJrZG93blRhYmxlKHRhYmxlLCBvcHRpb25zKSB7XG4gIGNvbnN0IHNldHRpbmdzID0gb3B0aW9ucyB8fCB7fVxuICAvLyBUbyBkbzogbmV4dCBtYWpvcjogY2hhbmdlIHRvIHNwcmVhZC5cbiAgY29uc3QgYWxpZ24gPSAoc2V0dGluZ3MuYWxpZ24gfHwgW10pLmNvbmNhdCgpXG4gIGNvbnN0IHN0cmluZ0xlbmd0aCA9IHNldHRpbmdzLnN0cmluZ0xlbmd0aCB8fCBkZWZhdWx0U3RyaW5nTGVuZ3RoXG4gIC8qKiBAdHlwZSB7QXJyYXk8bnVtYmVyPn0gQ2hhcmFjdGVyIGNvZGVzIGFzIHN5bWJvbHMgZm9yIGFsaWdubWVudCBwZXIgY29sdW1uLiAqL1xuICBjb25zdCBhbGlnbm1lbnRzID0gW11cbiAgLyoqIEB0eXBlIHtBcnJheTxBcnJheTxzdHJpbmc+Pn0gQ2VsbHMgcGVyIHJvdy4gKi9cbiAgY29uc3QgY2VsbE1hdHJpeCA9IFtdXG4gIC8qKiBAdHlwZSB7QXJyYXk8QXJyYXk8bnVtYmVyPj59IFNpemVzIG9mIGVhY2ggY2VsbCBwZXIgcm93LiAqL1xuICBjb25zdCBzaXplTWF0cml4ID0gW11cbiAgLyoqIEB0eXBlIHtBcnJheTxudW1iZXI+fSAqL1xuICBjb25zdCBsb25nZXN0Q2VsbEJ5Q29sdW1uID0gW11cbiAgbGV0IG1vc3RDZWxsc1BlclJvdyA9IDBcbiAgbGV0IHJvd0luZGV4ID0gLTFcblxuICAvLyBUaGlzIGlzIGEgc3VwZXJmbHVvdXMgbG9vcCBpZiB3ZSBkb27igJl0IGFsaWduIGRlbGltaXRlcnMsIGJ1dCBvdGhlcndpc2Ugd2XigJlkXG4gIC8vIGRvIHN1cGVyZmx1b3VzIHdvcmsgd2hlbiBhbGlnbmluZywgc28gb3B0aW1pemUgZm9yIGFsaWduaW5nLlxuICB3aGlsZSAoKytyb3dJbmRleCA8IHRhYmxlLmxlbmd0aCkge1xuICAgIC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn0gKi9cbiAgICBjb25zdCByb3cgPSBbXVxuICAgIC8qKiBAdHlwZSB7QXJyYXk8bnVtYmVyPn0gKi9cbiAgICBjb25zdCBzaXplcyA9IFtdXG4gICAgbGV0IGNvbHVtbkluZGV4ID0gLTFcblxuICAgIGlmICh0YWJsZVtyb3dJbmRleF0ubGVuZ3RoID4gbW9zdENlbGxzUGVyUm93KSB7XG4gICAgICBtb3N0Q2VsbHNQZXJSb3cgPSB0YWJsZVtyb3dJbmRleF0ubGVuZ3RoXG4gICAgfVxuXG4gICAgd2hpbGUgKCsrY29sdW1uSW5kZXggPCB0YWJsZVtyb3dJbmRleF0ubGVuZ3RoKSB7XG4gICAgICBjb25zdCBjZWxsID0gc2VyaWFsaXplKHRhYmxlW3Jvd0luZGV4XVtjb2x1bW5JbmRleF0pXG5cbiAgICAgIGlmIChzZXR0aW5ncy5hbGlnbkRlbGltaXRlcnMgIT09IGZhbHNlKSB7XG4gICAgICAgIGNvbnN0IHNpemUgPSBzdHJpbmdMZW5ndGgoY2VsbClcbiAgICAgICAgc2l6ZXNbY29sdW1uSW5kZXhdID0gc2l6ZVxuXG4gICAgICAgIGlmIChcbiAgICAgICAgICBsb25nZXN0Q2VsbEJ5Q29sdW1uW2NvbHVtbkluZGV4XSA9PT0gdW5kZWZpbmVkIHx8XG4gICAgICAgICAgc2l6ZSA+IGxvbmdlc3RDZWxsQnlDb2x1bW5bY29sdW1uSW5kZXhdXG4gICAgICAgICkge1xuICAgICAgICAgIGxvbmdlc3RDZWxsQnlDb2x1bW5bY29sdW1uSW5kZXhdID0gc2l6ZVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJvdy5wdXNoKGNlbGwpXG4gICAgfVxuXG4gICAgY2VsbE1hdHJpeFtyb3dJbmRleF0gPSByb3dcbiAgICBzaXplTWF0cml4W3Jvd0luZGV4XSA9IHNpemVzXG4gIH1cblxuICAvLyBGaWd1cmUgb3V0IHdoaWNoIGFsaWdubWVudHMgdG8gdXNlLlxuICBsZXQgY29sdW1uSW5kZXggPSAtMVxuXG4gIGlmICh0eXBlb2YgYWxpZ24gPT09ICdvYmplY3QnICYmICdsZW5ndGgnIGluIGFsaWduKSB7XG4gICAgd2hpbGUgKCsrY29sdW1uSW5kZXggPCBtb3N0Q2VsbHNQZXJSb3cpIHtcbiAgICAgIGFsaWdubWVudHNbY29sdW1uSW5kZXhdID0gdG9BbGlnbm1lbnQoYWxpZ25bY29sdW1uSW5kZXhdKVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBjb25zdCBjb2RlID0gdG9BbGlnbm1lbnQoYWxpZ24pXG5cbiAgICB3aGlsZSAoKytjb2x1bW5JbmRleCA8IG1vc3RDZWxsc1BlclJvdykge1xuICAgICAgYWxpZ25tZW50c1tjb2x1bW5JbmRleF0gPSBjb2RlXG4gICAgfVxuICB9XG5cbiAgLy8gSW5qZWN0IHRoZSBhbGlnbm1lbnQgcm93LlxuICBjb2x1bW5JbmRleCA9IC0xXG4gIC8qKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn0gKi9cbiAgY29uc3Qgcm93ID0gW11cbiAgLyoqIEB0eXBlIHtBcnJheTxudW1iZXI+fSAqL1xuICBjb25zdCBzaXplcyA9IFtdXG5cbiAgd2hpbGUgKCsrY29sdW1uSW5kZXggPCBtb3N0Q2VsbHNQZXJSb3cpIHtcbiAgICBjb25zdCBjb2RlID0gYWxpZ25tZW50c1tjb2x1bW5JbmRleF1cbiAgICBsZXQgYmVmb3JlID0gJydcbiAgICBsZXQgYWZ0ZXIgPSAnJ1xuXG4gICAgaWYgKGNvZGUgPT09IDk5IC8qIGBjYCAqLykge1xuICAgICAgYmVmb3JlID0gJzonXG4gICAgICBhZnRlciA9ICc6J1xuICAgIH0gZWxzZSBpZiAoY29kZSA9PT0gMTA4IC8qIGBsYCAqLykge1xuICAgICAgYmVmb3JlID0gJzonXG4gICAgfSBlbHNlIGlmIChjb2RlID09PSAxMTQgLyogYHJgICovKSB7XG4gICAgICBhZnRlciA9ICc6J1xuICAgIH1cblxuICAgIC8vIFRoZXJlICptdXN0KiBiZSBhdCBsZWFzdCBvbmUgaHlwaGVuLW1pbnVzIGluIGVhY2ggYWxpZ25tZW50IGNlbGwuXG4gICAgbGV0IHNpemUgPVxuICAgICAgc2V0dGluZ3MuYWxpZ25EZWxpbWl0ZXJzID09PSBmYWxzZVxuICAgICAgICA/IDFcbiAgICAgICAgOiBNYXRoLm1heChcbiAgICAgICAgICAgIDEsXG4gICAgICAgICAgICBsb25nZXN0Q2VsbEJ5Q29sdW1uW2NvbHVtbkluZGV4XSAtIGJlZm9yZS5sZW5ndGggLSBhZnRlci5sZW5ndGhcbiAgICAgICAgICApXG5cbiAgICBjb25zdCBjZWxsID0gYmVmb3JlICsgJy0nLnJlcGVhdChzaXplKSArIGFmdGVyXG5cbiAgICBpZiAoc2V0dGluZ3MuYWxpZ25EZWxpbWl0ZXJzICE9PSBmYWxzZSkge1xuICAgICAgc2l6ZSA9IGJlZm9yZS5sZW5ndGggKyBzaXplICsgYWZ0ZXIubGVuZ3RoXG5cbiAgICAgIGlmIChzaXplID4gbG9uZ2VzdENlbGxCeUNvbHVtbltjb2x1bW5JbmRleF0pIHtcbiAgICAgICAgbG9uZ2VzdENlbGxCeUNvbHVtbltjb2x1bW5JbmRleF0gPSBzaXplXG4gICAgICB9XG5cbiAgICAgIHNpemVzW2NvbHVtbkluZGV4XSA9IHNpemVcbiAgICB9XG5cbiAgICByb3dbY29sdW1uSW5kZXhdID0gY2VsbFxuICB9XG5cbiAgLy8gSW5qZWN0IHRoZSBhbGlnbm1lbnQgcm93LlxuICBjZWxsTWF0cml4LnNwbGljZSgxLCAwLCByb3cpXG4gIHNpemVNYXRyaXguc3BsaWNlKDEsIDAsIHNpemVzKVxuXG4gIHJvd0luZGV4ID0gLTFcbiAgLyoqIEB0eXBlIHtBcnJheTxzdHJpbmc+fSAqL1xuICBjb25zdCBsaW5lcyA9IFtdXG5cbiAgd2hpbGUgKCsrcm93SW5kZXggPCBjZWxsTWF0cml4Lmxlbmd0aCkge1xuICAgIGNvbnN0IHJvdyA9IGNlbGxNYXRyaXhbcm93SW5kZXhdXG4gICAgY29uc3Qgc2l6ZXMgPSBzaXplTWF0cml4W3Jvd0luZGV4XVxuICAgIGNvbHVtbkluZGV4ID0gLTFcbiAgICAvKiogQHR5cGUge0FycmF5PHN0cmluZz59ICovXG4gICAgY29uc3QgbGluZSA9IFtdXG5cbiAgICB3aGlsZSAoKytjb2x1bW5JbmRleCA8IG1vc3RDZWxsc1BlclJvdykge1xuICAgICAgY29uc3QgY2VsbCA9IHJvd1tjb2x1bW5JbmRleF0gfHwgJydcbiAgICAgIGxldCBiZWZvcmUgPSAnJ1xuICAgICAgbGV0IGFmdGVyID0gJydcblxuICAgICAgaWYgKHNldHRpbmdzLmFsaWduRGVsaW1pdGVycyAhPT0gZmFsc2UpIHtcbiAgICAgICAgY29uc3Qgc2l6ZSA9XG4gICAgICAgICAgbG9uZ2VzdENlbGxCeUNvbHVtbltjb2x1bW5JbmRleF0gLSAoc2l6ZXNbY29sdW1uSW5kZXhdIHx8IDApXG4gICAgICAgIGNvbnN0IGNvZGUgPSBhbGlnbm1lbnRzW2NvbHVtbkluZGV4XVxuXG4gICAgICAgIGlmIChjb2RlID09PSAxMTQgLyogYHJgICovKSB7XG4gICAgICAgICAgYmVmb3JlID0gJyAnLnJlcGVhdChzaXplKVxuICAgICAgICB9IGVsc2UgaWYgKGNvZGUgPT09IDk5IC8qIGBjYCAqLykge1xuICAgICAgICAgIGlmIChzaXplICUgMikge1xuICAgICAgICAgICAgYmVmb3JlID0gJyAnLnJlcGVhdChzaXplIC8gMiArIDAuNSlcbiAgICAgICAgICAgIGFmdGVyID0gJyAnLnJlcGVhdChzaXplIC8gMiAtIDAuNSlcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgYmVmb3JlID0gJyAnLnJlcGVhdChzaXplIC8gMilcbiAgICAgICAgICAgIGFmdGVyID0gYmVmb3JlXG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGFmdGVyID0gJyAnLnJlcGVhdChzaXplKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmIChzZXR0aW5ncy5kZWxpbWl0ZXJTdGFydCAhPT0gZmFsc2UgJiYgIWNvbHVtbkluZGV4KSB7XG4gICAgICAgIGxpbmUucHVzaCgnfCcpXG4gICAgICB9XG5cbiAgICAgIGlmIChcbiAgICAgICAgc2V0dGluZ3MucGFkZGluZyAhPT0gZmFsc2UgJiZcbiAgICAgICAgLy8gRG9u4oCZdCBhZGQgdGhlIG9wZW5pbmcgc3BhY2UgaWYgd2XigJlyZSBub3QgYWxpZ25pbmcgYW5kIHRoZSBjZWxsIGlzXG4gICAgICAgIC8vIGVtcHR5OiB0aGVyZSB3aWxsIGJlIGEgY2xvc2luZyBzcGFjZS5cbiAgICAgICAgIShzZXR0aW5ncy5hbGlnbkRlbGltaXRlcnMgPT09IGZhbHNlICYmIGNlbGwgPT09ICcnKSAmJlxuICAgICAgICAoc2V0dGluZ3MuZGVsaW1pdGVyU3RhcnQgIT09IGZhbHNlIHx8IGNvbHVtbkluZGV4KVxuICAgICAgKSB7XG4gICAgICAgIGxpbmUucHVzaCgnICcpXG4gICAgICB9XG5cbiAgICAgIGlmIChzZXR0aW5ncy5hbGlnbkRlbGltaXRlcnMgIT09IGZhbHNlKSB7XG4gICAgICAgIGxpbmUucHVzaChiZWZvcmUpXG4gICAgICB9XG5cbiAgICAgIGxpbmUucHVzaChjZWxsKVxuXG4gICAgICBpZiAoc2V0dGluZ3MuYWxpZ25EZWxpbWl0ZXJzICE9PSBmYWxzZSkge1xuICAgICAgICBsaW5lLnB1c2goYWZ0ZXIpXG4gICAgICB9XG5cbiAgICAgIGlmIChzZXR0aW5ncy5wYWRkaW5nICE9PSBmYWxzZSkge1xuICAgICAgICBsaW5lLnB1c2goJyAnKVxuICAgICAgfVxuXG4gICAgICBpZiAoXG4gICAgICAgIHNldHRpbmdzLmRlbGltaXRlckVuZCAhPT0gZmFsc2UgfHxcbiAgICAgICAgY29sdW1uSW5kZXggIT09IG1vc3RDZWxsc1BlclJvdyAtIDFcbiAgICAgICkge1xuICAgICAgICBsaW5lLnB1c2goJ3wnKVxuICAgICAgfVxuICAgIH1cblxuICAgIGxpbmVzLnB1c2goXG4gICAgICBzZXR0aW5ncy5kZWxpbWl0ZXJFbmQgPT09IGZhbHNlXG4gICAgICAgID8gbGluZS5qb2luKCcnKS5yZXBsYWNlKC8gKyQvLCAnJylcbiAgICAgICAgOiBsaW5lLmpvaW4oJycpXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIGxpbmVzLmpvaW4oJ1xcbicpXG59XG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbdmFsdWVdXG4gKiAgIFZhbHVlIHRvIHNlcmlhbGl6ZS5cbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKiAgIFJlc3VsdC5cbiAqL1xuZnVuY3Rpb24gc2VyaWFsaXplKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkID8gJycgOiBTdHJpbmcodmFsdWUpXG59XG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSB2YWx1ZVxuICogICBWYWx1ZS5cbiAqIEByZXR1cm5zIHtudW1iZXJ9XG4gKiAgIEFsaWdubWVudC5cbiAqL1xuZnVuY3Rpb24gdG9BbGlnbm1lbnQodmFsdWUpIHtcbiAgY29uc3QgY29kZSA9IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgPyB2YWx1ZS5jb2RlUG9pbnRBdCgwKSA6IDBcblxuICByZXR1cm4gY29kZSA9PT0gNjcgLyogYENgICovIHx8IGNvZGUgPT09IDk5IC8qIGBjYCAqL1xuICAgID8gOTkgLyogYGNgICovXG4gICAgOiBjb2RlID09PSA3NiAvKiBgTGAgKi8gfHwgY29kZSA9PT0gMTA4IC8qIGBsYCAqL1xuICAgICAgPyAxMDggLyogYGxgICovXG4gICAgICA6IGNvZGUgPT09IDgyIC8qIGBSYCAqLyB8fCBjb2RlID09PSAxMTQgLyogYHJgICovXG4gICAgICAgID8gMTE0IC8qIGByYCAqL1xuICAgICAgICA6IDBcbn1cbiJdLCJuYW1lcyI6WyJkZWZhdWx0U3RyaW5nTGVuZ3RoIiwidmFsdWUiLCJsZW5ndGgiLCJtYXJrZG93blRhYmxlIiwidGFibGUiLCJvcHRpb25zIiwic2V0dGluZ3MiLCJhbGlnbiIsImNvbmNhdCIsInN0cmluZ0xlbmd0aCIsImFsaWdubWVudHMiLCJjZWxsTWF0cml4Iiwic2l6ZU1hdHJpeCIsImxvbmdlc3RDZWxsQnlDb2x1bW4iLCJtb3N0Q2VsbHNQZXJSb3ciLCJyb3dJbmRleCIsInJvdyIsInNpemVzIiwiY29sdW1uSW5kZXgiLCJjZWxsIiwic2VyaWFsaXplIiwiYWxpZ25EZWxpbWl0ZXJzIiwic2l6ZSIsInVuZGVmaW5lZCIsInB1c2giLCJ0b0FsaWdubWVudCIsImNvZGUiLCJiZWZvcmUiLCJhZnRlciIsIk1hdGgiLCJtYXgiLCJyZXBlYXQiLCJzcGxpY2UiLCJsaW5lcyIsImxpbmUiLCJkZWxpbWl0ZXJTdGFydCIsInBhZGRpbmciLCJkZWxpbWl0ZXJFbmQiLCJqb2luIiwicmVwbGFjZSIsIlN0cmluZyIsImNvZGVQb2ludEF0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/markdown-table/index.js\n");

/***/ })

};
;