"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark/dev/lib/constructs.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/constructs.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */ \n\n\n/** @satisfies {Extension['document']} */ const document = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n};\n/** @satisfies {Extension['contentInitial']} */ const contentInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n};\n/** @satisfies {Extension['flowInitial']} */ const flowInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n};\n/** @satisfies {Extension['flow']} */ const flow = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n};\n/** @satisfies {Extension['string']} */ const string = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n};\n/** @satisfies {Extension['text']} */ const text = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n};\n/** @satisfies {Extension['insideSpan']} */ const insideSpan = {\n    null: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n        _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver\n    ]\n};\n/** @satisfies {Extension['attentionMarkers']} */ const attentionMarkers = {\n    null: [\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore\n    ]\n};\n/** @satisfies {Extension['disable']} */ const disable = {\n    null: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */ \n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__(\"micromark\");\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */ function createTokenizer(parser, initialize, from) {\n    /** @type {Point} */ let point = {\n        _bufferIndex: -1,\n        _index: 0,\n        line: from && from.line || 1,\n        column: from && from.column || 1,\n        offset: from && from.offset || 0\n    };\n    /** @type {Record<string, number>} */ const columnStart = {};\n    /** @type {Array<Construct>} */ const resolveAllConstructs = [];\n    /** @type {Array<Chunk>} */ let chunks = [];\n    /** @type {Array<Token>} */ let stack = [];\n    /** @type {boolean | undefined} */ let consumed = true;\n    /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */ const effects = {\n        attempt: constructFactory(onsuccessfulconstruct),\n        check: constructFactory(onsuccessfulcheck),\n        consume,\n        enter,\n        exit,\n        interrupt: constructFactory(onsuccessfulcheck, {\n            interrupt: true\n        })\n    };\n    /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */ const context = {\n        code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        containerState: {},\n        defineSkip,\n        events: [],\n        now,\n        parser,\n        previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        sliceSerialize,\n        sliceStream,\n        write\n    };\n    /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */ let state = initialize.tokenize.call(context, effects);\n    /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */ let expectedCode;\n    if (initialize.resolveAll) {\n        resolveAllConstructs.push(initialize);\n    }\n    return context;\n    /** @type {TokenizeContext['write']} */ function write(slice) {\n        chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice);\n        main();\n        // Exit if we’re not done, resolve might change stuff.\n        if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            return [];\n        }\n        addResult(initialize, 0);\n        // Otherwise, resolve, and exit.\n        context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context);\n        return context.events;\n    }\n    //\n    // Tools.\n    //\n    /** @type {TokenizeContext['sliceSerialize']} */ function sliceSerialize(token, expandTabs) {\n        return serializeChunks(sliceStream(token), expandTabs);\n    }\n    /** @type {TokenizeContext['sliceStream']} */ function sliceStream(token) {\n        return sliceChunks(chunks, token);\n    }\n    /** @type {TokenizeContext['now']} */ function now() {\n        // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n        const { _bufferIndex, _index, line, column, offset } = point;\n        return {\n            _bufferIndex,\n            _index,\n            line,\n            column,\n            offset\n        };\n    }\n    /** @type {TokenizeContext['defineSkip']} */ function defineSkip(value) {\n        columnStart[value.line] = value.column;\n        accountForPotentialSkip();\n        debug(\"position: define skip: `%j`\", point);\n    }\n    //\n    // State management.\n    //\n    /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function main() {\n        /** @type {number} */ let chunkIndex;\n        while(point._index < chunks.length){\n            const chunk = chunks[point._index];\n            // If we’re in a buffer chunk, loop through it.\n            if (typeof chunk === \"string\") {\n                chunkIndex = point._index;\n                if (point._bufferIndex < 0) {\n                    point._bufferIndex = 0;\n                }\n                while(point._index === chunkIndex && point._bufferIndex < chunk.length){\n                    go(chunk.charCodeAt(point._bufferIndex));\n                }\n            } else {\n                go(chunk);\n            }\n        }\n    }\n    /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */ function go(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, \"expected character to be consumed\");\n        consumed = undefined;\n        debug(\"main: passing `%s` to %s\", code, state && state.name);\n        expectedCode = code;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === \"function\", \"expected state\");\n        state = state(code);\n    }\n    /** @type {Effects['consume']} */ function consume(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected given code to equal expected code\");\n        debug(\"consume: `%s`\", code);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === undefined, \"expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === null ? context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\" : context.events[context.events.length - 1][0] === \"enter\", \"expected last token to be open\");\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            point.line++;\n            point.column = 1;\n            point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1;\n            accountForPotentialSkip();\n            debug(\"position: after eol: `%j`\", point);\n        } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n            point.column++;\n            point.offset++;\n        }\n        // Not in a string chunk.\n        if (point._bufferIndex < 0) {\n            point._index++;\n        } else {\n            point._bufferIndex++;\n            // At end of string chunk.\n            if (point._bufferIndex === // Points w/ non-negative `_bufferIndex` reference\n            // strings.\n            /** @type {string} */ chunks[point._index].length) {\n                point._bufferIndex = -1;\n                point._index++;\n            }\n        }\n        // Expose the previous character.\n        context.previous = code;\n        // Mark as consumed.\n        consumed = true;\n    }\n    /** @type {Effects['enter']} */ function enter(type, fields) {\n        /** @type {Token} */ // @ts-expect-error Patch instead of assign required fields to help GC.\n        const token = fields || {};\n        token.type = type;\n        token.start = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        debug(\"enter: `%s`\", type);\n        context.events.push([\n            \"enter\",\n            token,\n            context\n        ]);\n        stack.push(token);\n        return token;\n    }\n    /** @type {Effects['exit']} */ function exit(type) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        const token = stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, \"cannot close w/o open tokens\");\n        token.end = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, \"expected exit token to match current token\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(!(token.start._index === token.end._index && token.start._bufferIndex === token.end._bufferIndex), \"expected non-empty token (`\" + type + \"`)\");\n        debug(\"exit: `%s`\", token.type);\n        context.events.push([\n            \"exit\",\n            token,\n            context\n        ]);\n        return token;\n    }\n    /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulconstruct(construct, info) {\n        addResult(construct, info.from);\n    }\n    /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulcheck(_, info) {\n        info.restore();\n    }\n    /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */ function constructFactory(onreturn, fields) {\n        return hook;\n        /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */ function hook(constructs, returnState, bogusState) {\n            /** @type {ReadonlyArray<Construct>} */ let listOfConstructs;\n            /** @type {number} */ let constructIndex;\n            /** @type {Construct} */ let currentConstruct;\n            /** @type {Info} */ let info;\n            return Array.isArray(constructs) ? /* c8 ignore next 1 */ handleListOfConstructs(constructs) : \"tokenize\" in constructs ? handleListOfConstructs([\n                /** @type {Construct} */ constructs\n            ]) : handleMapOfConstructs(constructs);\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleMapOfConstructs(map) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    const left = code !== null && map[code];\n                    const all = code !== null && map.null;\n                    const list = [\n                        // To do: add more extension tests.\n                        /* c8 ignore next 2 */ ...Array.isArray(left) ? left : left ? [\n                            left\n                        ] : [],\n                        ...Array.isArray(all) ? all : all ? [\n                            all\n                        ] : []\n                    ];\n                    return handleListOfConstructs(list)(code);\n                }\n            }\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleListOfConstructs(list) {\n                listOfConstructs = list;\n                constructIndex = 0;\n                if (list.length === 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, \"expected `bogusState` to be given\");\n                    return bogusState;\n                }\n                return handleConstruct(list[constructIndex]);\n            }\n            /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */ function handleConstruct(construct) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    // To do: not needed to store if there is no bogus state, probably?\n                    // Currently doesn’t work because `inspect` in document does a check\n                    // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n                    // by not storing.\n                    info = store();\n                    currentConstruct = construct;\n                    if (!construct.partial) {\n                        context.currentConstruct = construct;\n                    }\n                    // Always populated by defaults.\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n                    if (construct.name && context.parser.constructs.disable.null.includes(construct.name)) {\n                        return nok(code);\n                    }\n                    return construct.tokenize.call(// If we do have fields, create an object w/ `context` as its\n                    // prototype.\n                    // This allows a “live binding”, which is needed for `interrupt`.\n                    fields ? Object.assign(Object.create(context), fields) : context, effects, ok, nok)(code);\n                }\n            }\n            /** @type {State} */ function ok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                onreturn(currentConstruct, info);\n                return returnState;\n            }\n            /** @type {State} */ function nok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                info.restore();\n                if (++constructIndex < listOfConstructs.length) {\n                    return handleConstruct(listOfConstructs[constructIndex]);\n                }\n                return bogusState;\n            }\n        }\n    }\n    /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */ function addResult(construct, from) {\n        if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n            resolveAllConstructs.push(construct);\n        }\n        if (construct.resolve) {\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(context.events, from, context.events.length - from, construct.resolve(context.events.slice(from), context));\n        }\n        if (construct.resolveTo) {\n            context.events = construct.resolveTo(context.events, context);\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(construct.partial || context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\", \"expected last token to end\");\n    }\n    /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */ function store() {\n        const startPoint = now();\n        const startPrevious = context.previous;\n        const startCurrentConstruct = context.currentConstruct;\n        const startEventsIndex = context.events.length;\n        const startStack = Array.from(stack);\n        return {\n            from: startEventsIndex,\n            restore\n        };\n        /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */ function restore() {\n            point = startPoint;\n            context.previous = startPrevious;\n            context.currentConstruct = startCurrentConstruct;\n            context.events.length = startEventsIndex;\n            stack = startStack;\n            accountForPotentialSkip();\n            debug(\"position: restore: `%j`\", point);\n        }\n    }\n    /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function accountForPotentialSkip() {\n        if (point.line in columnStart && point.column < 2) {\n            point.column = columnStart[point.line];\n            point.offset += columnStart[point.line] - 1;\n        }\n    }\n}\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */ function sliceChunks(chunks, token) {\n    const startIndex = token.start._index;\n    const startBufferIndex = token.start._bufferIndex;\n    const endIndex = token.end._index;\n    const endBufferIndex = token.end._bufferIndex;\n    /** @type {Array<Chunk>} */ let view;\n    if (startIndex === endIndex) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, \"expected non-negative end buffer index\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, \"expected non-negative start buffer index\");\n        // @ts-expect-error `_bufferIndex` is used on string chunks.\n        view = [\n            chunks[startIndex].slice(startBufferIndex, endBufferIndex)\n        ];\n    } else {\n        view = chunks.slice(startIndex, endIndex);\n        if (startBufferIndex > -1) {\n            const head = view[0];\n            if (typeof head === \"string\") {\n                view[0] = head.slice(startBufferIndex);\n            /* c8 ignore next 4 -- used to be used, no longer */ } else {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, \"expected `startBufferIndex` to be `0`\");\n                view.shift();\n            }\n        }\n        if (endBufferIndex > 0) {\n            // @ts-expect-error `_bufferIndex` is used on string chunks.\n            view.push(chunks[endIndex].slice(0, endBufferIndex));\n        }\n    }\n    return view;\n}\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */ function serializeChunks(chunks, expandTabs) {\n    let index = -1;\n    /** @type {Array<string>} */ const result = [];\n    /** @type {boolean | undefined} */ let atTab;\n    while(++index < chunks.length){\n        const chunk = chunks[index];\n        /** @type {string} */ let value;\n        if (typeof chunk === \"string\") {\n            value = chunk;\n        } else switch(chunk){\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab:\n                {\n                    value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace:\n                {\n                    if (!expandTabs && atTab) continue;\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space;\n                    break;\n                }\n            default:\n                {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === \"number\", \"expected number\");\n                    // Currently only replacement character.\n                    value = String.fromCharCode(chunk);\n                }\n        }\n        atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab;\n        result.push(value);\n    }\n    return result.join(\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/content.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ \n\n\n\n/** @type {InitialConstruct} */ const content = {\n    tokenize: initializeContent\n};\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */ function initializeContent(effects) {\n    const contentStart = effects.attempt(this.parser.constructs.contentInitial, afterContentStartConstruct, paragraphInitial);\n    /** @type {Token} */ let previous;\n    return contentStart;\n    /** @type {State} */ function afterContentStartConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix);\n    }\n    /** @type {State} */ function paragraphInitial(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected anything other than a line ending or EOF\");\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n        return lineStart(code);\n    }\n    /** @type {State} */ function lineStart(code) {\n        const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n            previous\n        });\n        if (previous) {\n            previous.next = token;\n        }\n        previous = token;\n        return data(code);\n    }\n    /** @type {State} */ function data(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            return lineStart;\n        }\n        // Data.\n        effects.consume(code);\n        return data;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/document.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const document = {\n    tokenize: initializeDocument\n};\n/** @type {Construct} */ const containerConstruct = {\n    tokenize: tokenizeContainer\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeDocument(effects) {\n    const self = this;\n    /** @type {Array<StackItem>} */ const stack = [];\n    let continued = 0;\n    /** @type {TokenizeContext | undefined} */ let childFlow;\n    /** @type {Token | undefined} */ let childToken;\n    /** @type {number} */ let lineStartOffset;\n    return start;\n    /** @type {State} */ function start(code) {\n        // First we iterate through the open blocks, starting with the root\n        // document, and descending through last children down to the last open\n        // block.\n        // Each block imposes a condition that the line must satisfy if the block is\n        // to remain open.\n        // For example, a block quote requires a `>` character.\n        // A paragraph requires a non-blank line.\n        // In this phase we may match all or just some of the open blocks.\n        // But we cannot close unmatched blocks yet, because we may have a lazy\n        // continuation line.\n        if (continued < stack.length) {\n            const item = stack[continued];\n            self.containerState = item[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(item[0].continuation, \"expected `continuation` to be defined on container construct\");\n            return effects.attempt(item[0].continuation, documentContinue, checkNewContainers)(code);\n        }\n        // Done.\n        return checkNewContainers(code);\n    }\n    /** @type {State} */ function documentContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined after continuation\");\n        continued++;\n        // Note: this field is called `_closeFlow` but it also closes containers.\n        // Perhaps a good idea to rename it but it’s already used in the wild by\n        // extensions.\n        if (self.containerState._closeFlow) {\n            self.containerState._closeFlow = undefined;\n            if (childFlow) {\n                closeFlow();\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when dealing with lazy lines in `writeToChild`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {Point | undefined} */ let point;\n            // Find the flow chunk.\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    point = self.events[indexBeforeFlow][1].end;\n                    break;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            let index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n            return checkNewContainers(code);\n        }\n        return start(code);\n    }\n    /** @type {State} */ function checkNewContainers(code) {\n        // Next, after consuming the continuation markers for existing blocks, we\n        // look for new block starts (e.g. `>` for a block quote).\n        // If we encounter a new block start, we close any blocks unmatched in\n        // step 1 before creating the new block as a child of the last matched\n        // block.\n        if (continued === stack.length) {\n            // No need to `check` whether there’s a container, of `exitContainers`\n            // would be moot.\n            // We can instead immediately `attempt` to parse one.\n            if (!childFlow) {\n                return documentContinued(code);\n            }\n            // If we have concrete content, such as block HTML or fenced code,\n            // we can’t have containers “pierce” into them, so we can immediately\n            // start.\n            if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n                return flowStart(code);\n            }\n            // If we do have flow, it could still be a blank line,\n            // but we’d be interrupting it w/ a new container if there’s a current\n            // construct.\n            // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n            // needed in micromark-extension-gfm-table@1.0.6).\n            self.interrupt = Boolean(childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack);\n        }\n        // Check if there is a new container.\n        self.containerState = {};\n        return effects.check(containerConstruct, thereIsANewContainer, thereIsNoNewContainer)(code);\n    }\n    /** @type {State} */ function thereIsANewContainer(code) {\n        if (childFlow) closeFlow();\n        exitContainers(continued);\n        return documentContinued(code);\n    }\n    /** @type {State} */ function thereIsNoNewContainer(code) {\n        self.parser.lazy[self.now().line] = continued !== stack.length;\n        lineStartOffset = self.now().offset;\n        return flowStart(code);\n    }\n    /** @type {State} */ function documentContinued(code) {\n        // Try new containers.\n        self.containerState = {};\n        return effects.attempt(containerConstruct, containerContinue, flowStart)(code);\n    }\n    /** @type {State} */ function containerContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.currentConstruct, \"expected `currentConstruct` to be defined on tokenizer\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined on tokenizer\");\n        continued++;\n        stack.push([\n            self.currentConstruct,\n            self.containerState\n        ]);\n        // Try another.\n        return documentContinued(code);\n    }\n    /** @type {State} */ function flowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            if (childFlow) closeFlow();\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        childFlow = childFlow || self.parser.flow(self.now());\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n            _tokenizer: childFlow,\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n            previous: childToken\n        });\n        return flowContinue(code);\n    }\n    /** @type {State} */ function flowContinue(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true);\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow));\n            // Get ready for the next line.\n            continued = 0;\n            self.interrupt = undefined;\n            return start;\n        }\n        effects.consume(code);\n        return flowContinue;\n    }\n    /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */ function writeToChild(token, endOfFile) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when continuing\");\n        const stream = self.sliceStream(token);\n        if (endOfFile) stream.push(null);\n        token.previous = childToken;\n        if (childToken) childToken.next = token;\n        childToken = token;\n        childFlow.defineSkip(token.start);\n        childFlow.write(stream);\n        // Alright, so we just added a lazy line:\n        //\n        // ```markdown\n        // > a\n        // b.\n        //\n        // Or:\n        //\n        // > ~~~c\n        // d\n        //\n        // Or:\n        //\n        // > | e |\n        // f\n        // ```\n        //\n        // The construct in the second example (fenced code) does not accept lazy\n        // lines, so it marked itself as done at the end of its first line, and\n        // then the content construct parses `d`.\n        // Most constructs in markdown match on the first line: if the first line\n        // forms a construct, a non-lazy line can’t “unmake” it.\n        //\n        // The construct in the third example is potentially a GFM table, and\n        // those are *weird*.\n        // It *could* be a table, from the first line, if the following line\n        // matches a condition.\n        // In this case, that second line is lazy, which “unmakes” the first line\n        // and turns the whole into one content block.\n        //\n        // We’ve now parsed the non-lazy and the lazy line, and can figure out\n        // whether the lazy line started a new flow block.\n        // If it did, we exit the current containers between the two flow blocks.\n        if (self.parser.lazy[token.start.line]) {\n            let index = childFlow.events.length;\n            while(index--){\n                if (// The token starts before the line ending…\n                childFlow.events[index][1].start.offset < lineStartOffset && // …and either is not ended yet…\n                (!childFlow.events[index][1].end || // …or ends after it.\n                childFlow.events[index][1].end.offset > lineStartOffset)) {\n                    // Exit: there’s still something open, which means it’s a lazy line\n                    // part of something.\n                    return;\n                }\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when closing flow in `documentContinue`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {boolean | undefined} */ let seen;\n            /** @type {Point | undefined} */ let point;\n            // Find the previous chunk (the one before the lazy line).\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    if (seen) {\n                        point = self.events[indexBeforeFlow][1].end;\n                        break;\n                    }\n                    seen = true;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n        }\n    }\n    /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */ function exitContainers(size) {\n        let index = stack.length;\n        // Exit open containers.\n        while(index-- > size){\n            const entry = stack[index];\n            self.containerState = entry[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(entry[0].exit, \"expected `exit` to be defined on container construct\");\n            entry[0].exit.call(self, effects);\n        }\n        stack.length = size;\n    }\n    function closeFlow() {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined when closing flow\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when closing it\");\n        childFlow.write([\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof\n        ]);\n        childToken = undefined;\n        childFlow = undefined;\n        self.containerState._closeFlow = undefined;\n    }\n}\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */ function tokenizeContainer(effects, ok, nok) {\n    // Always populated by defaults.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(this.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, effects.attempt(this.parser.constructs.document, ok, nok), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix, this.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS9kb2N1bWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBOzs7Ozs7Ozs7Ozs7Q0FZQyxHQUVEOzs7Q0FHQyxHQUVrQztBQUNpQjtBQUNPO0FBQ2Q7QUFDZ0I7QUFFN0QsNkJBQTZCLEdBQ3RCLE1BQU1RLFdBQVc7SUFBQ0MsVUFBVUM7QUFBa0IsRUFBQztBQUV0RCxzQkFBc0IsR0FDdEIsTUFBTUMscUJBQXFCO0lBQUNGLFVBQVVHO0FBQWlCO0FBRXZEOzs7OztDQUtDLEdBQ0QsU0FBU0YsbUJBQW1CRyxPQUFPO0lBQ2pDLE1BQU1DLE9BQU8sSUFBSTtJQUNqQiw2QkFBNkIsR0FDN0IsTUFBTUMsUUFBUSxFQUFFO0lBQ2hCLElBQUlDLFlBQVk7SUFDaEIsd0NBQXdDLEdBQ3hDLElBQUlDO0lBQ0osOEJBQThCLEdBQzlCLElBQUlDO0lBQ0osbUJBQW1CLEdBQ25CLElBQUlDO0lBRUosT0FBT0M7SUFFUCxrQkFBa0IsR0FDbEIsU0FBU0EsTUFBTUMsSUFBSTtRQUNqQixtRUFBbUU7UUFDbkUsdUVBQXVFO1FBQ3ZFLFNBQVM7UUFDVCw0RUFBNEU7UUFDNUUsa0JBQWtCO1FBQ2xCLHVEQUF1RDtRQUN2RCx5Q0FBeUM7UUFDekMsa0VBQWtFO1FBQ2xFLHVFQUF1RTtRQUN2RSxxQkFBcUI7UUFDckIsSUFBSUwsWUFBWUQsTUFBTU8sTUFBTSxFQUFFO1lBQzVCLE1BQU1DLE9BQU9SLEtBQUssQ0FBQ0MsVUFBVTtZQUM3QkYsS0FBS1UsY0FBYyxHQUFHRCxJQUFJLENBQUMsRUFBRTtZQUM3QnRCLDBDQUFNQSxDQUNKc0IsSUFBSSxDQUFDLEVBQUUsQ0FBQ0UsWUFBWSxFQUNwQjtZQUVGLE9BQU9aLFFBQVFhLE9BQU8sQ0FDcEJILElBQUksQ0FBQyxFQUFFLENBQUNFLFlBQVksRUFDcEJFLGtCQUNBQyxvQkFDQVA7UUFDSjtRQUVBLFFBQVE7UUFDUixPQUFPTyxtQkFBbUJQO0lBQzVCO0lBRUEsa0JBQWtCLEdBQ2xCLFNBQVNNLGlCQUFpQk4sSUFBSTtRQUM1QnBCLDBDQUFNQSxDQUNKYSxLQUFLVSxjQUFjLEVBQ25CO1FBR0ZSO1FBRUEseUVBQXlFO1FBQ3pFLHdFQUF3RTtRQUN4RSxjQUFjO1FBQ2QsSUFBSUYsS0FBS1UsY0FBYyxDQUFDSyxVQUFVLEVBQUU7WUFDbENmLEtBQUtVLGNBQWMsQ0FBQ0ssVUFBVSxHQUFHQztZQUVqQyxJQUFJYixXQUFXO2dCQUNiYztZQUNGO1lBRUEsa0VBQWtFO1lBQ2xFLDREQUE0RDtZQUM1RCxNQUFNQyxtQkFBbUJsQixLQUFLbUIsTUFBTSxDQUFDWCxNQUFNO1lBQzNDLElBQUlZLGtCQUFrQkY7WUFDdEIsOEJBQThCLEdBQzlCLElBQUlHO1lBRUosdUJBQXVCO1lBQ3ZCLE1BQU9ELGtCQUFtQjtnQkFDeEIsSUFDRXBCLEtBQUttQixNQUFNLENBQUNDLGdCQUFnQixDQUFDLEVBQUUsS0FBSyxVQUNwQ3BCLEtBQUttQixNQUFNLENBQUNDLGdCQUFnQixDQUFDLEVBQUUsQ0FBQ0UsSUFBSSxLQUFLN0Isd0RBQUtBLENBQUM4QixTQUFTLEVBQ3hEO29CQUNBRixRQUFRckIsS0FBS21CLE1BQU0sQ0FBQ0MsZ0JBQWdCLENBQUMsRUFBRSxDQUFDSSxHQUFHO29CQUMzQztnQkFDRjtZQUNGO1lBRUFyQywwQ0FBTUEsQ0FBQ2tDLE9BQU87WUFFZEksZUFBZXZCO1lBRWYsaUJBQWlCO1lBQ2pCLElBQUl3QixRQUFRUjtZQUVaLE1BQU9RLFFBQVExQixLQUFLbUIsTUFBTSxDQUFDWCxNQUFNLENBQUU7Z0JBQ2pDUixLQUFLbUIsTUFBTSxDQUFDTyxNQUFNLENBQUMsRUFBRSxDQUFDRixHQUFHLEdBQUc7b0JBQUMsR0FBR0gsS0FBSztnQkFBQTtnQkFDckNLO1lBQ0Y7WUFFQSw0REFBNEQ7WUFDNURwQyw4REFBTUEsQ0FDSlUsS0FBS21CLE1BQU0sRUFDWEMsa0JBQWtCLEdBQ2xCLEdBQ0FwQixLQUFLbUIsTUFBTSxDQUFDUSxLQUFLLENBQUNUO1lBR3BCLCtCQUErQjtZQUMvQmxCLEtBQUttQixNQUFNLENBQUNYLE1BQU0sR0FBR2tCO1lBRXJCLE9BQU9aLG1CQUFtQlA7UUFDNUI7UUFFQSxPQUFPRCxNQUFNQztJQUNmO0lBRUEsa0JBQWtCLEdBQ2xCLFNBQVNPLG1CQUFtQlAsSUFBSTtRQUM5Qix5RUFBeUU7UUFDekUsMERBQTBEO1FBQzFELHNFQUFzRTtRQUN0RSxzRUFBc0U7UUFDdEUsU0FBUztRQUNULElBQUlMLGNBQWNELE1BQU1PLE1BQU0sRUFBRTtZQUM5QixzRUFBc0U7WUFDdEUsaUJBQWlCO1lBQ2pCLHFEQUFxRDtZQUNyRCxJQUFJLENBQUNMLFdBQVc7Z0JBQ2QsT0FBT3lCLGtCQUFrQnJCO1lBQzNCO1lBRUEsa0VBQWtFO1lBQ2xFLHFFQUFxRTtZQUNyRSxTQUFTO1lBQ1QsSUFBSUosVUFBVTBCLGdCQUFnQixJQUFJMUIsVUFBVTBCLGdCQUFnQixDQUFDQyxRQUFRLEVBQUU7Z0JBQ3JFLE9BQU9DLFVBQVV4QjtZQUNuQjtZQUVBLHNEQUFzRDtZQUN0RCxzRUFBc0U7WUFDdEUsYUFBYTtZQUNiLHVFQUF1RTtZQUN2RSxrREFBa0Q7WUFDbERQLEtBQUtnQyxTQUFTLEdBQUdDLFFBQ2Y5QixVQUFVMEIsZ0JBQWdCLElBQUksQ0FBQzFCLFVBQVUrQiw2QkFBNkI7UUFFMUU7UUFFQSxxQ0FBcUM7UUFDckNsQyxLQUFLVSxjQUFjLEdBQUcsQ0FBQztRQUN2QixPQUFPWCxRQUFRb0MsS0FBSyxDQUNsQnRDLG9CQUNBdUMsc0JBQ0FDLHVCQUNBOUI7SUFDSjtJQUVBLGtCQUFrQixHQUNsQixTQUFTNkIscUJBQXFCN0IsSUFBSTtRQUNoQyxJQUFJSixXQUFXYztRQUNmUSxlQUFldkI7UUFDZixPQUFPMEIsa0JBQWtCckI7SUFDM0I7SUFFQSxrQkFBa0IsR0FDbEIsU0FBUzhCLHNCQUFzQjlCLElBQUk7UUFDakNQLEtBQUtzQyxNQUFNLENBQUNDLElBQUksQ0FBQ3ZDLEtBQUt3QyxHQUFHLEdBQUdDLElBQUksQ0FBQyxHQUFHdkMsY0FBY0QsTUFBTU8sTUFBTTtRQUM5REgsa0JBQWtCTCxLQUFLd0MsR0FBRyxHQUFHRSxNQUFNO1FBQ25DLE9BQU9YLFVBQVV4QjtJQUNuQjtJQUVBLGtCQUFrQixHQUNsQixTQUFTcUIsa0JBQWtCckIsSUFBSTtRQUM3QixzQkFBc0I7UUFDdEJQLEtBQUtVLGNBQWMsR0FBRyxDQUFDO1FBQ3ZCLE9BQU9YLFFBQVFhLE9BQU8sQ0FDcEJmLG9CQUNBOEMsbUJBQ0FaLFdBQ0F4QjtJQUNKO0lBRUEsa0JBQWtCLEdBQ2xCLFNBQVNvQyxrQkFBa0JwQyxJQUFJO1FBQzdCcEIsMENBQU1BLENBQ0phLEtBQUs2QixnQkFBZ0IsRUFDckI7UUFFRjFDLDBDQUFNQSxDQUNKYSxLQUFLVSxjQUFjLEVBQ25CO1FBRUZSO1FBQ0FELE1BQU0yQyxJQUFJLENBQUM7WUFBQzVDLEtBQUs2QixnQkFBZ0I7WUFBRTdCLEtBQUtVLGNBQWM7U0FBQztRQUN2RCxlQUFlO1FBQ2YsT0FBT2tCLGtCQUFrQnJCO0lBQzNCO0lBRUEsa0JBQWtCLEdBQ2xCLFNBQVN3QixVQUFVeEIsSUFBSTtRQUNyQixJQUFJQSxTQUFTaEIsd0RBQUtBLENBQUNzRCxHQUFHLEVBQUU7WUFDdEIsSUFBSTFDLFdBQVdjO1lBQ2ZRLGVBQWU7WUFDZjFCLFFBQVErQyxPQUFPLENBQUN2QztZQUNoQjtRQUNGO1FBRUFKLFlBQVlBLGFBQWFILEtBQUtzQyxNQUFNLENBQUNTLElBQUksQ0FBQy9DLEtBQUt3QyxHQUFHO1FBQ2xEekMsUUFBUWlELEtBQUssQ0FBQ3ZELHdEQUFLQSxDQUFDOEIsU0FBUyxFQUFFO1lBQzdCMEIsWUFBWTlDO1lBQ1orQyxhQUFhMUQsNERBQVNBLENBQUMyRCxlQUFlO1lBQ3RDQyxVQUFVaEQ7UUFDWjtRQUVBLE9BQU9pRCxhQUFhOUM7SUFDdEI7SUFFQSxrQkFBa0IsR0FDbEIsU0FBUzhDLGFBQWE5QyxJQUFJO1FBQ3hCLElBQUlBLFNBQVNoQix3REFBS0EsQ0FBQ3NELEdBQUcsRUFBRTtZQUN0QlMsYUFBYXZELFFBQVF3RCxJQUFJLENBQUM5RCx3REFBS0EsQ0FBQzhCLFNBQVMsR0FBRztZQUM1Q0UsZUFBZTtZQUNmMUIsUUFBUStDLE9BQU8sQ0FBQ3ZDO1lBQ2hCO1FBQ0Y7UUFFQSxJQUFJbEIsNEVBQWtCQSxDQUFDa0IsT0FBTztZQUM1QlIsUUFBUStDLE9BQU8sQ0FBQ3ZDO1lBQ2hCK0MsYUFBYXZELFFBQVF3RCxJQUFJLENBQUM5RCx3REFBS0EsQ0FBQzhCLFNBQVM7WUFDekMsK0JBQStCO1lBQy9CckIsWUFBWTtZQUNaRixLQUFLZ0MsU0FBUyxHQUFHaEI7WUFDakIsT0FBT1Y7UUFDVDtRQUVBUCxRQUFRK0MsT0FBTyxDQUFDdkM7UUFDaEIsT0FBTzhDO0lBQ1Q7SUFFQTs7Ozs7OztHQU9DLEdBQ0QsU0FBU0MsYUFBYUUsS0FBSyxFQUFFQyxTQUFTO1FBQ3BDdEUsMENBQU1BLENBQUNnQixXQUFXO1FBQ2xCLE1BQU11RCxTQUFTMUQsS0FBSzJELFdBQVcsQ0FBQ0g7UUFDaEMsSUFBSUMsV0FBV0MsT0FBT2QsSUFBSSxDQUFDO1FBQzNCWSxNQUFNSixRQUFRLEdBQUdoRDtRQUNqQixJQUFJQSxZQUFZQSxXQUFXd0QsSUFBSSxHQUFHSjtRQUNsQ3BELGFBQWFvRDtRQUNickQsVUFBVTBELFVBQVUsQ0FBQ0wsTUFBTWxELEtBQUs7UUFDaENILFVBQVUyRCxLQUFLLENBQUNKO1FBRWhCLHlDQUF5QztRQUN6QyxFQUFFO1FBQ0YsY0FBYztRQUNkLE1BQU07UUFDTixLQUFLO1FBQ0wsRUFBRTtRQUNGLE1BQU07UUFDTixFQUFFO1FBQ0YsU0FBUztRQUNULElBQUk7UUFDSixFQUFFO1FBQ0YsTUFBTTtRQUNOLEVBQUU7UUFDRixVQUFVO1FBQ1YsSUFBSTtRQUNKLE1BQU07UUFDTixFQUFFO1FBQ0YseUVBQXlFO1FBQ3pFLHVFQUF1RTtRQUN2RSx5Q0FBeUM7UUFDekMseUVBQXlFO1FBQ3pFLHdEQUF3RDtRQUN4RCxFQUFFO1FBQ0YscUVBQXFFO1FBQ3JFLHFCQUFxQjtRQUNyQixvRUFBb0U7UUFDcEUsdUJBQXVCO1FBQ3ZCLHlFQUF5RTtRQUN6RSw4Q0FBOEM7UUFDOUMsRUFBRTtRQUNGLHNFQUFzRTtRQUN0RSxrREFBa0Q7UUFDbEQseUVBQXlFO1FBQ3pFLElBQUkxRCxLQUFLc0MsTUFBTSxDQUFDQyxJQUFJLENBQUNpQixNQUFNbEQsS0FBSyxDQUFDbUMsSUFBSSxDQUFDLEVBQUU7WUFDdEMsSUFBSWYsUUFBUXZCLFVBQVVnQixNQUFNLENBQUNYLE1BQU07WUFFbkMsTUFBT2tCLFFBQVM7Z0JBQ2QsSUFDRSwyQ0FBMkM7Z0JBQzNDdkIsVUFBVWdCLE1BQU0sQ0FBQ08sTUFBTSxDQUFDLEVBQUUsQ0FBQ3BCLEtBQUssQ0FBQ29DLE1BQU0sR0FBR3JDLG1CQUMxQyxnQ0FBZ0M7Z0JBQy9CLEVBQUNGLFVBQVVnQixNQUFNLENBQUNPLE1BQU0sQ0FBQyxFQUFFLENBQUNGLEdBQUcsSUFDOUIscUJBQXFCO2dCQUNyQnJCLFVBQVVnQixNQUFNLENBQUNPLE1BQU0sQ0FBQyxFQUFFLENBQUNGLEdBQUcsQ0FBQ2tCLE1BQU0sR0FBR3JDLGVBQWMsR0FDeEQ7b0JBQ0EsbUVBQW1FO29CQUNuRSxxQkFBcUI7b0JBQ3JCO2dCQUNGO1lBQ0Y7WUFFQSxrRUFBa0U7WUFDbEUscURBQXFEO1lBQ3JELE1BQU1hLG1CQUFtQmxCLEtBQUttQixNQUFNLENBQUNYLE1BQU07WUFDM0MsSUFBSVksa0JBQWtCRjtZQUN0QixnQ0FBZ0MsR0FDaEMsSUFBSTZDO1lBQ0osOEJBQThCLEdBQzlCLElBQUkxQztZQUVKLDBEQUEwRDtZQUMxRCxNQUFPRCxrQkFBbUI7Z0JBQ3hCLElBQ0VwQixLQUFLbUIsTUFBTSxDQUFDQyxnQkFBZ0IsQ0FBQyxFQUFFLEtBQUssVUFDcENwQixLQUFLbUIsTUFBTSxDQUFDQyxnQkFBZ0IsQ0FBQyxFQUFFLENBQUNFLElBQUksS0FBSzdCLHdEQUFLQSxDQUFDOEIsU0FBUyxFQUN4RDtvQkFDQSxJQUFJd0MsTUFBTTt3QkFDUjFDLFFBQVFyQixLQUFLbUIsTUFBTSxDQUFDQyxnQkFBZ0IsQ0FBQyxFQUFFLENBQUNJLEdBQUc7d0JBQzNDO29CQUNGO29CQUVBdUMsT0FBTztnQkFDVDtZQUNGO1lBRUE1RSwwQ0FBTUEsQ0FBQ2tDLE9BQU87WUFFZEksZUFBZXZCO1lBRWYsaUJBQWlCO1lBQ2pCd0IsUUFBUVI7WUFFUixNQUFPUSxRQUFRMUIsS0FBS21CLE1BQU0sQ0FBQ1gsTUFBTSxDQUFFO2dCQUNqQ1IsS0FBS21CLE1BQU0sQ0FBQ08sTUFBTSxDQUFDLEVBQUUsQ0FBQ0YsR0FBRyxHQUFHO29CQUFDLEdBQUdILEtBQUs7Z0JBQUE7Z0JBQ3JDSztZQUNGO1lBRUEsNERBQTREO1lBQzVEcEMsOERBQU1BLENBQ0pVLEtBQUttQixNQUFNLEVBQ1hDLGtCQUFrQixHQUNsQixHQUNBcEIsS0FBS21CLE1BQU0sQ0FBQ1EsS0FBSyxDQUFDVDtZQUdwQiwrQkFBK0I7WUFDL0JsQixLQUFLbUIsTUFBTSxDQUFDWCxNQUFNLEdBQUdrQjtRQUN2QjtJQUNGO0lBRUE7Ozs7O0dBS0MsR0FDRCxTQUFTRCxlQUFldUMsSUFBSTtRQUMxQixJQUFJdEMsUUFBUXpCLE1BQU1PLE1BQU07UUFFeEIsd0JBQXdCO1FBQ3hCLE1BQU9rQixVQUFVc0MsS0FBTTtZQUNyQixNQUFNQyxRQUFRaEUsS0FBSyxDQUFDeUIsTUFBTTtZQUMxQjFCLEtBQUtVLGNBQWMsR0FBR3VELEtBQUssQ0FBQyxFQUFFO1lBQzlCOUUsMENBQU1BLENBQ0o4RSxLQUFLLENBQUMsRUFBRSxDQUFDVixJQUFJLEVBQ2I7WUFFRlUsS0FBSyxDQUFDLEVBQUUsQ0FBQ1YsSUFBSSxDQUFDVyxJQUFJLENBQUNsRSxNQUFNRDtRQUMzQjtRQUVBRSxNQUFNTyxNQUFNLEdBQUd3RDtJQUNqQjtJQUVBLFNBQVMvQztRQUNQOUIsMENBQU1BLENBQ0phLEtBQUtVLGNBQWMsRUFDbkI7UUFFRnZCLDBDQUFNQSxDQUFDZ0IsV0FBVztRQUNsQkEsVUFBVTJELEtBQUssQ0FBQztZQUFDdkUsd0RBQUtBLENBQUNzRCxHQUFHO1NBQUM7UUFDM0J6QyxhQUFhWTtRQUNiYixZQUFZYTtRQUNaaEIsS0FBS1UsY0FBYyxDQUFDSyxVQUFVLEdBQUdDO0lBQ25DO0FBQ0Y7QUFFQTs7Ozs7Q0FLQyxHQUNELFNBQVNsQixrQkFBa0JDLE9BQU8sRUFBRWIsRUFBRSxFQUFFaUYsR0FBRztJQUN6QyxnQ0FBZ0M7SUFDaENoRiwwQ0FBTUEsQ0FDSixJQUFJLENBQUNtRCxNQUFNLENBQUM4QixVQUFVLENBQUNDLE9BQU8sQ0FBQ0MsSUFBSSxFQUNuQztJQUVGLE9BQU9sRixxRUFBWUEsQ0FDakJXLFNBQ0FBLFFBQVFhLE9BQU8sQ0FBQyxJQUFJLENBQUMwQixNQUFNLENBQUM4QixVQUFVLENBQUMxRSxRQUFRLEVBQUVSLElBQUlpRixNQUNyRDFFLHdEQUFLQSxDQUFDOEUsVUFBVSxFQUNoQixJQUFJLENBQUNqQyxNQUFNLENBQUM4QixVQUFVLENBQUNDLE9BQU8sQ0FBQ0MsSUFBSSxDQUFDRSxRQUFRLENBQUMsa0JBQ3pDeEQsWUFDQXhCLDREQUFTQSxDQUFDaUYsT0FBTztBQUV6QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvZG9yYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGV2L2xpYi9pbml0aWFsaXplL2RvY3VtZW50LmpzP2ExOGEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtcbiAqICAgQ29uc3RydWN0LFxuICogICBDb250YWluZXJTdGF0ZSxcbiAqICAgSW5pdGlhbENvbnN0cnVjdCxcbiAqICAgSW5pdGlhbGl6ZXIsXG4gKiAgIFBvaW50LFxuICogICBTdGF0ZSxcbiAqICAgVG9rZW5pemVDb250ZXh0LFxuICogICBUb2tlbml6ZXIsXG4gKiAgIFRva2VuXG4gKiB9IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbi8qKlxuICogQHR5cGVkZWYge1tDb25zdHJ1Y3QsIENvbnRhaW5lclN0YXRlXX0gU3RhY2tJdGVtXG4gKiAgIENvbnN0cnVjdCBhbmQgaXRzIHN0YXRlLlxuICovXG5cbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICdkZXZsb3AnXG5pbXBvcnQge2ZhY3RvcnlTcGFjZX0gZnJvbSAnbWljcm9tYXJrLWZhY3Rvcnktc3BhY2UnXG5pbXBvcnQge21hcmtkb3duTGluZUVuZGluZ30gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2hhcmFjdGVyJ1xuaW1wb3J0IHtzcGxpY2V9IGZyb20gJ21pY3JvbWFyay11dGlsLWNodW5rZWQnXG5pbXBvcnQge2NvZGVzLCBjb25zdGFudHMsIHR5cGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wnXG5cbi8qKiBAdHlwZSB7SW5pdGlhbENvbnN0cnVjdH0gKi9cbmV4cG9ydCBjb25zdCBkb2N1bWVudCA9IHt0b2tlbml6ZTogaW5pdGlhbGl6ZURvY3VtZW50fVxuXG4vKiogQHR5cGUge0NvbnN0cnVjdH0gKi9cbmNvbnN0IGNvbnRhaW5lckNvbnN0cnVjdCA9IHt0b2tlbml6ZTogdG9rZW5pemVDb250YWluZXJ9XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqICAgU2VsZi5cbiAqIEB0eXBlIHtJbml0aWFsaXplcn1cbiAqICAgSW5pdGlhbGl6ZXIuXG4gKi9cbmZ1bmN0aW9uIGluaXRpYWxpemVEb2N1bWVudChlZmZlY3RzKSB7XG4gIGNvbnN0IHNlbGYgPSB0aGlzXG4gIC8qKiBAdHlwZSB7QXJyYXk8U3RhY2tJdGVtPn0gKi9cbiAgY29uc3Qgc3RhY2sgPSBbXVxuICBsZXQgY29udGludWVkID0gMFxuICAvKiogQHR5cGUge1Rva2VuaXplQ29udGV4dCB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IGNoaWxkRmxvd1xuICAvKiogQHR5cGUge1Rva2VuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgY2hpbGRUb2tlblxuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgbGV0IGxpbmVTdGFydE9mZnNldFxuXG4gIHJldHVybiBzdGFydFxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICAvLyBGaXJzdCB3ZSBpdGVyYXRlIHRocm91Z2ggdGhlIG9wZW4gYmxvY2tzLCBzdGFydGluZyB3aXRoIHRoZSByb290XG4gICAgLy8gZG9jdW1lbnQsIGFuZCBkZXNjZW5kaW5nIHRocm91Z2ggbGFzdCBjaGlsZHJlbiBkb3duIHRvIHRoZSBsYXN0IG9wZW5cbiAgICAvLyBibG9jay5cbiAgICAvLyBFYWNoIGJsb2NrIGltcG9zZXMgYSBjb25kaXRpb24gdGhhdCB0aGUgbGluZSBtdXN0IHNhdGlzZnkgaWYgdGhlIGJsb2NrIGlzXG4gICAgLy8gdG8gcmVtYWluIG9wZW4uXG4gICAgLy8gRm9yIGV4YW1wbGUsIGEgYmxvY2sgcXVvdGUgcmVxdWlyZXMgYSBgPmAgY2hhcmFjdGVyLlxuICAgIC8vIEEgcGFyYWdyYXBoIHJlcXVpcmVzIGEgbm9uLWJsYW5rIGxpbmUuXG4gICAgLy8gSW4gdGhpcyBwaGFzZSB3ZSBtYXkgbWF0Y2ggYWxsIG9yIGp1c3Qgc29tZSBvZiB0aGUgb3BlbiBibG9ja3MuXG4gICAgLy8gQnV0IHdlIGNhbm5vdCBjbG9zZSB1bm1hdGNoZWQgYmxvY2tzIHlldCwgYmVjYXVzZSB3ZSBtYXkgaGF2ZSBhIGxhenlcbiAgICAvLyBjb250aW51YXRpb24gbGluZS5cbiAgICBpZiAoY29udGludWVkIDwgc3RhY2subGVuZ3RoKSB7XG4gICAgICBjb25zdCBpdGVtID0gc3RhY2tbY29udGludWVkXVxuICAgICAgc2VsZi5jb250YWluZXJTdGF0ZSA9IGl0ZW1bMV1cbiAgICAgIGFzc2VydChcbiAgICAgICAgaXRlbVswXS5jb250aW51YXRpb24sXG4gICAgICAgICdleHBlY3RlZCBgY29udGludWF0aW9uYCB0byBiZSBkZWZpbmVkIG9uIGNvbnRhaW5lciBjb25zdHJ1Y3QnXG4gICAgICApXG4gICAgICByZXR1cm4gZWZmZWN0cy5hdHRlbXB0KFxuICAgICAgICBpdGVtWzBdLmNvbnRpbnVhdGlvbixcbiAgICAgICAgZG9jdW1lbnRDb250aW51ZSxcbiAgICAgICAgY2hlY2tOZXdDb250YWluZXJzXG4gICAgICApKGNvZGUpXG4gICAgfVxuXG4gICAgLy8gRG9uZS5cbiAgICByZXR1cm4gY2hlY2tOZXdDb250YWluZXJzKGNvZGUpXG4gIH1cblxuICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICBmdW5jdGlvbiBkb2N1bWVudENvbnRpbnVlKGNvZGUpIHtcbiAgICBhc3NlcnQoXG4gICAgICBzZWxmLmNvbnRhaW5lclN0YXRlLFxuICAgICAgJ2V4cGVjdGVkIGBjb250YWluZXJTdGF0ZWAgdG8gYmUgZGVmaW5lZCBhZnRlciBjb250aW51YXRpb24nXG4gICAgKVxuXG4gICAgY29udGludWVkKytcblxuICAgIC8vIE5vdGU6IHRoaXMgZmllbGQgaXMgY2FsbGVkIGBfY2xvc2VGbG93YCBidXQgaXQgYWxzbyBjbG9zZXMgY29udGFpbmVycy5cbiAgICAvLyBQZXJoYXBzIGEgZ29vZCBpZGVhIHRvIHJlbmFtZSBpdCBidXQgaXTigJlzIGFscmVhZHkgdXNlZCBpbiB0aGUgd2lsZCBieVxuICAgIC8vIGV4dGVuc2lvbnMuXG4gICAgaWYgKHNlbGYuY29udGFpbmVyU3RhdGUuX2Nsb3NlRmxvdykge1xuICAgICAgc2VsZi5jb250YWluZXJTdGF0ZS5fY2xvc2VGbG93ID0gdW5kZWZpbmVkXG5cbiAgICAgIGlmIChjaGlsZEZsb3cpIHtcbiAgICAgICAgY2xvc2VGbG93KClcbiAgICAgIH1cblxuICAgICAgLy8gTm90ZTogdGhpcyBhbGdvcml0aG0gZm9yIG1vdmluZyBldmVudHMgYXJvdW5kIGlzIHNpbWlsYXIgdG8gdGhlXG4gICAgICAvLyBhbGdvcml0aG0gd2hlbiBkZWFsaW5nIHdpdGggbGF6eSBsaW5lcyBpbiBgd3JpdGVUb0NoaWxkYC5cbiAgICAgIGNvbnN0IGluZGV4QmVmb3JlRXhpdHMgPSBzZWxmLmV2ZW50cy5sZW5ndGhcbiAgICAgIGxldCBpbmRleEJlZm9yZUZsb3cgPSBpbmRleEJlZm9yZUV4aXRzXG4gICAgICAvKiogQHR5cGUge1BvaW50IHwgdW5kZWZpbmVkfSAqL1xuICAgICAgbGV0IHBvaW50XG5cbiAgICAgIC8vIEZpbmQgdGhlIGZsb3cgY2h1bmsuXG4gICAgICB3aGlsZSAoaW5kZXhCZWZvcmVGbG93LS0pIHtcbiAgICAgICAgaWYgKFxuICAgICAgICAgIHNlbGYuZXZlbnRzW2luZGV4QmVmb3JlRmxvd11bMF0gPT09ICdleGl0JyAmJlxuICAgICAgICAgIHNlbGYuZXZlbnRzW2luZGV4QmVmb3JlRmxvd11bMV0udHlwZSA9PT0gdHlwZXMuY2h1bmtGbG93XG4gICAgICAgICkge1xuICAgICAgICAgIHBvaW50ID0gc2VsZi5ldmVudHNbaW5kZXhCZWZvcmVGbG93XVsxXS5lbmRcbiAgICAgICAgICBicmVha1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGFzc2VydChwb2ludCwgJ2NvdWxkIG5vdCBmaW5kIHByZXZpb3VzIGZsb3cgY2h1bmsnKVxuXG4gICAgICBleGl0Q29udGFpbmVycyhjb250aW51ZWQpXG5cbiAgICAgIC8vIEZpeCBwb3NpdGlvbnMuXG4gICAgICBsZXQgaW5kZXggPSBpbmRleEJlZm9yZUV4aXRzXG5cbiAgICAgIHdoaWxlIChpbmRleCA8IHNlbGYuZXZlbnRzLmxlbmd0aCkge1xuICAgICAgICBzZWxmLmV2ZW50c1tpbmRleF1bMV0uZW5kID0gey4uLnBvaW50fVxuICAgICAgICBpbmRleCsrXG4gICAgICB9XG5cbiAgICAgIC8vIEluamVjdCB0aGUgZXhpdHMgZWFybGllciAodGhleeKAmXJlIHN0aWxsIGFsc28gYXQgdGhlIGVuZCkuXG4gICAgICBzcGxpY2UoXG4gICAgICAgIHNlbGYuZXZlbnRzLFxuICAgICAgICBpbmRleEJlZm9yZUZsb3cgKyAxLFxuICAgICAgICAwLFxuICAgICAgICBzZWxmLmV2ZW50cy5zbGljZShpbmRleEJlZm9yZUV4aXRzKVxuICAgICAgKVxuXG4gICAgICAvLyBEaXNjYXJkIHRoZSBkdXBsaWNhdGUgZXhpdHMuXG4gICAgICBzZWxmLmV2ZW50cy5sZW5ndGggPSBpbmRleFxuXG4gICAgICByZXR1cm4gY2hlY2tOZXdDb250YWluZXJzKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIHN0YXJ0KGNvZGUpXG4gIH1cblxuICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICBmdW5jdGlvbiBjaGVja05ld0NvbnRhaW5lcnMoY29kZSkge1xuICAgIC8vIE5leHQsIGFmdGVyIGNvbnN1bWluZyB0aGUgY29udGludWF0aW9uIG1hcmtlcnMgZm9yIGV4aXN0aW5nIGJsb2Nrcywgd2VcbiAgICAvLyBsb29rIGZvciBuZXcgYmxvY2sgc3RhcnRzIChlLmcuIGA+YCBmb3IgYSBibG9jayBxdW90ZSkuXG4gICAgLy8gSWYgd2UgZW5jb3VudGVyIGEgbmV3IGJsb2NrIHN0YXJ0LCB3ZSBjbG9zZSBhbnkgYmxvY2tzIHVubWF0Y2hlZCBpblxuICAgIC8vIHN0ZXAgMSBiZWZvcmUgY3JlYXRpbmcgdGhlIG5ldyBibG9jayBhcyBhIGNoaWxkIG9mIHRoZSBsYXN0IG1hdGNoZWRcbiAgICAvLyBibG9jay5cbiAgICBpZiAoY29udGludWVkID09PSBzdGFjay5sZW5ndGgpIHtcbiAgICAgIC8vIE5vIG5lZWQgdG8gYGNoZWNrYCB3aGV0aGVyIHRoZXJl4oCZcyBhIGNvbnRhaW5lciwgb2YgYGV4aXRDb250YWluZXJzYFxuICAgICAgLy8gd291bGQgYmUgbW9vdC5cbiAgICAgIC8vIFdlIGNhbiBpbnN0ZWFkIGltbWVkaWF0ZWx5IGBhdHRlbXB0YCB0byBwYXJzZSBvbmUuXG4gICAgICBpZiAoIWNoaWxkRmxvdykge1xuICAgICAgICByZXR1cm4gZG9jdW1lbnRDb250aW51ZWQoY29kZSlcbiAgICAgIH1cblxuICAgICAgLy8gSWYgd2UgaGF2ZSBjb25jcmV0ZSBjb250ZW50LCBzdWNoIGFzIGJsb2NrIEhUTUwgb3IgZmVuY2VkIGNvZGUsXG4gICAgICAvLyB3ZSBjYW7igJl0IGhhdmUgY29udGFpbmVycyDigJxwaWVyY2XigJ0gaW50byB0aGVtLCBzbyB3ZSBjYW4gaW1tZWRpYXRlbHlcbiAgICAgIC8vIHN0YXJ0LlxuICAgICAgaWYgKGNoaWxkRmxvdy5jdXJyZW50Q29uc3RydWN0ICYmIGNoaWxkRmxvdy5jdXJyZW50Q29uc3RydWN0LmNvbmNyZXRlKSB7XG4gICAgICAgIHJldHVybiBmbG93U3RhcnQoY29kZSlcbiAgICAgIH1cblxuICAgICAgLy8gSWYgd2UgZG8gaGF2ZSBmbG93LCBpdCBjb3VsZCBzdGlsbCBiZSBhIGJsYW5rIGxpbmUsXG4gICAgICAvLyBidXQgd2XigJlkIGJlIGludGVycnVwdGluZyBpdCB3LyBhIG5ldyBjb250YWluZXIgaWYgdGhlcmXigJlzIGEgY3VycmVudFxuICAgICAgLy8gY29uc3RydWN0LlxuICAgICAgLy8gVG8gZG86IG5leHQgbWFqb3I6IHJlbW92ZSBgX2dmbVRhYmxlRHluYW1pY0ludGVycnVwdEhhY2tgIChubyBsb25nZXJcbiAgICAgIC8vIG5lZWRlZCBpbiBtaWNyb21hcmstZXh0ZW5zaW9uLWdmbS10YWJsZUAxLjAuNikuXG4gICAgICBzZWxmLmludGVycnVwdCA9IEJvb2xlYW4oXG4gICAgICAgIGNoaWxkRmxvdy5jdXJyZW50Q29uc3RydWN0ICYmICFjaGlsZEZsb3cuX2dmbVRhYmxlRHluYW1pY0ludGVycnVwdEhhY2tcbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiB0aGVyZSBpcyBhIG5ldyBjb250YWluZXIuXG4gICAgc2VsZi5jb250YWluZXJTdGF0ZSA9IHt9XG4gICAgcmV0dXJuIGVmZmVjdHMuY2hlY2soXG4gICAgICBjb250YWluZXJDb25zdHJ1Y3QsXG4gICAgICB0aGVyZUlzQU5ld0NvbnRhaW5lcixcbiAgICAgIHRoZXJlSXNOb05ld0NvbnRhaW5lclxuICAgICkoY29kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIHRoZXJlSXNBTmV3Q29udGFpbmVyKGNvZGUpIHtcbiAgICBpZiAoY2hpbGRGbG93KSBjbG9zZUZsb3coKVxuICAgIGV4aXRDb250YWluZXJzKGNvbnRpbnVlZClcbiAgICByZXR1cm4gZG9jdW1lbnRDb250aW51ZWQoY29kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIHRoZXJlSXNOb05ld0NvbnRhaW5lcihjb2RlKSB7XG4gICAgc2VsZi5wYXJzZXIubGF6eVtzZWxmLm5vdygpLmxpbmVdID0gY29udGludWVkICE9PSBzdGFjay5sZW5ndGhcbiAgICBsaW5lU3RhcnRPZmZzZXQgPSBzZWxmLm5vdygpLm9mZnNldFxuICAgIHJldHVybiBmbG93U3RhcnQoY29kZSlcbiAgfVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGZ1bmN0aW9uIGRvY3VtZW50Q29udGludWVkKGNvZGUpIHtcbiAgICAvLyBUcnkgbmV3IGNvbnRhaW5lcnMuXG4gICAgc2VsZi5jb250YWluZXJTdGF0ZSA9IHt9XG4gICAgcmV0dXJuIGVmZmVjdHMuYXR0ZW1wdChcbiAgICAgIGNvbnRhaW5lckNvbnN0cnVjdCxcbiAgICAgIGNvbnRhaW5lckNvbnRpbnVlLFxuICAgICAgZmxvd1N0YXJ0XG4gICAgKShjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gY29udGFpbmVyQ29udGludWUoY29kZSkge1xuICAgIGFzc2VydChcbiAgICAgIHNlbGYuY3VycmVudENvbnN0cnVjdCxcbiAgICAgICdleHBlY3RlZCBgY3VycmVudENvbnN0cnVjdGAgdG8gYmUgZGVmaW5lZCBvbiB0b2tlbml6ZXInXG4gICAgKVxuICAgIGFzc2VydChcbiAgICAgIHNlbGYuY29udGFpbmVyU3RhdGUsXG4gICAgICAnZXhwZWN0ZWQgYGNvbnRhaW5lclN0YXRlYCB0byBiZSBkZWZpbmVkIG9uIHRva2VuaXplcidcbiAgICApXG4gICAgY29udGludWVkKytcbiAgICBzdGFjay5wdXNoKFtzZWxmLmN1cnJlbnRDb25zdHJ1Y3QsIHNlbGYuY29udGFpbmVyU3RhdGVdKVxuICAgIC8vIFRyeSBhbm90aGVyLlxuICAgIHJldHVybiBkb2N1bWVudENvbnRpbnVlZChjb2RlKVxuICB9XG5cbiAgLyoqIEB0eXBlIHtTdGF0ZX0gKi9cbiAgZnVuY3Rpb24gZmxvd1N0YXJ0KGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gY29kZXMuZW9mKSB7XG4gICAgICBpZiAoY2hpbGRGbG93KSBjbG9zZUZsb3coKVxuICAgICAgZXhpdENvbnRhaW5lcnMoMClcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgY2hpbGRGbG93ID0gY2hpbGRGbG93IHx8IHNlbGYucGFyc2VyLmZsb3coc2VsZi5ub3coKSlcbiAgICBlZmZlY3RzLmVudGVyKHR5cGVzLmNodW5rRmxvdywge1xuICAgICAgX3Rva2VuaXplcjogY2hpbGRGbG93LFxuICAgICAgY29udGVudFR5cGU6IGNvbnN0YW50cy5jb250ZW50VHlwZUZsb3csXG4gICAgICBwcmV2aW91czogY2hpbGRUb2tlblxuICAgIH0pXG5cbiAgICByZXR1cm4gZmxvd0NvbnRpbnVlKGNvZGUpXG4gIH1cblxuICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICBmdW5jdGlvbiBmbG93Q29udGludWUoY29kZSkge1xuICAgIGlmIChjb2RlID09PSBjb2Rlcy5lb2YpIHtcbiAgICAgIHdyaXRlVG9DaGlsZChlZmZlY3RzLmV4aXQodHlwZXMuY2h1bmtGbG93KSwgdHJ1ZSlcbiAgICAgIGV4aXRDb250YWluZXJzKDApXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgd3JpdGVUb0NoaWxkKGVmZmVjdHMuZXhpdCh0eXBlcy5jaHVua0Zsb3cpKVxuICAgICAgLy8gR2V0IHJlYWR5IGZvciB0aGUgbmV4dCBsaW5lLlxuICAgICAgY29udGludWVkID0gMFxuICAgICAgc2VsZi5pbnRlcnJ1cHQgPSB1bmRlZmluZWRcbiAgICAgIHJldHVybiBzdGFydFxuICAgIH1cblxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIHJldHVybiBmbG93Q29udGludWVcbiAgfVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge1Rva2VufSB0b2tlblxuICAgKiAgIFRva2VuLlxuICAgKiBAcGFyYW0ge2Jvb2xlYW4gfCB1bmRlZmluZWR9IFtlbmRPZkZpbGVdXG4gICAqICAgV2hldGhlciB0aGUgdG9rZW4gaXMgYXQgdGhlIGVuZCBvZiB0aGUgZmlsZSAoZGVmYXVsdDogYGZhbHNlYCkuXG4gICAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gICAqICAgTm90aGluZy5cbiAgICovXG4gIGZ1bmN0aW9uIHdyaXRlVG9DaGlsZCh0b2tlbiwgZW5kT2ZGaWxlKSB7XG4gICAgYXNzZXJ0KGNoaWxkRmxvdywgJ2V4cGVjdGVkIGBjaGlsZEZsb3dgIHRvIGJlIGRlZmluZWQgd2hlbiBjb250aW51aW5nJylcbiAgICBjb25zdCBzdHJlYW0gPSBzZWxmLnNsaWNlU3RyZWFtKHRva2VuKVxuICAgIGlmIChlbmRPZkZpbGUpIHN0cmVhbS5wdXNoKG51bGwpXG4gICAgdG9rZW4ucHJldmlvdXMgPSBjaGlsZFRva2VuXG4gICAgaWYgKGNoaWxkVG9rZW4pIGNoaWxkVG9rZW4ubmV4dCA9IHRva2VuXG4gICAgY2hpbGRUb2tlbiA9IHRva2VuXG4gICAgY2hpbGRGbG93LmRlZmluZVNraXAodG9rZW4uc3RhcnQpXG4gICAgY2hpbGRGbG93LndyaXRlKHN0cmVhbSlcblxuICAgIC8vIEFscmlnaHQsIHNvIHdlIGp1c3QgYWRkZWQgYSBsYXp5IGxpbmU6XG4gICAgLy9cbiAgICAvLyBgYGBtYXJrZG93blxuICAgIC8vID4gYVxuICAgIC8vIGIuXG4gICAgLy9cbiAgICAvLyBPcjpcbiAgICAvL1xuICAgIC8vID4gfn5+Y1xuICAgIC8vIGRcbiAgICAvL1xuICAgIC8vIE9yOlxuICAgIC8vXG4gICAgLy8gPiB8IGUgfFxuICAgIC8vIGZcbiAgICAvLyBgYGBcbiAgICAvL1xuICAgIC8vIFRoZSBjb25zdHJ1Y3QgaW4gdGhlIHNlY29uZCBleGFtcGxlIChmZW5jZWQgY29kZSkgZG9lcyBub3QgYWNjZXB0IGxhenlcbiAgICAvLyBsaW5lcywgc28gaXQgbWFya2VkIGl0c2VsZiBhcyBkb25lIGF0IHRoZSBlbmQgb2YgaXRzIGZpcnN0IGxpbmUsIGFuZFxuICAgIC8vIHRoZW4gdGhlIGNvbnRlbnQgY29uc3RydWN0IHBhcnNlcyBgZGAuXG4gICAgLy8gTW9zdCBjb25zdHJ1Y3RzIGluIG1hcmtkb3duIG1hdGNoIG9uIHRoZSBmaXJzdCBsaW5lOiBpZiB0aGUgZmlyc3QgbGluZVxuICAgIC8vIGZvcm1zIGEgY29uc3RydWN0LCBhIG5vbi1sYXp5IGxpbmUgY2Fu4oCZdCDigJx1bm1ha2XigJ0gaXQuXG4gICAgLy9cbiAgICAvLyBUaGUgY29uc3RydWN0IGluIHRoZSB0aGlyZCBleGFtcGxlIGlzIHBvdGVudGlhbGx5IGEgR0ZNIHRhYmxlLCBhbmRcbiAgICAvLyB0aG9zZSBhcmUgKndlaXJkKi5cbiAgICAvLyBJdCAqY291bGQqIGJlIGEgdGFibGUsIGZyb20gdGhlIGZpcnN0IGxpbmUsIGlmIHRoZSBmb2xsb3dpbmcgbGluZVxuICAgIC8vIG1hdGNoZXMgYSBjb25kaXRpb24uXG4gICAgLy8gSW4gdGhpcyBjYXNlLCB0aGF0IHNlY29uZCBsaW5lIGlzIGxhenksIHdoaWNoIOKAnHVubWFrZXPigJ0gdGhlIGZpcnN0IGxpbmVcbiAgICAvLyBhbmQgdHVybnMgdGhlIHdob2xlIGludG8gb25lIGNvbnRlbnQgYmxvY2suXG4gICAgLy9cbiAgICAvLyBXZeKAmXZlIG5vdyBwYXJzZWQgdGhlIG5vbi1sYXp5IGFuZCB0aGUgbGF6eSBsaW5lLCBhbmQgY2FuIGZpZ3VyZSBvdXRcbiAgICAvLyB3aGV0aGVyIHRoZSBsYXp5IGxpbmUgc3RhcnRlZCBhIG5ldyBmbG93IGJsb2NrLlxuICAgIC8vIElmIGl0IGRpZCwgd2UgZXhpdCB0aGUgY3VycmVudCBjb250YWluZXJzIGJldHdlZW4gdGhlIHR3byBmbG93IGJsb2Nrcy5cbiAgICBpZiAoc2VsZi5wYXJzZXIubGF6eVt0b2tlbi5zdGFydC5saW5lXSkge1xuICAgICAgbGV0IGluZGV4ID0gY2hpbGRGbG93LmV2ZW50cy5sZW5ndGhcblxuICAgICAgd2hpbGUgKGluZGV4LS0pIHtcbiAgICAgICAgaWYgKFxuICAgICAgICAgIC8vIFRoZSB0b2tlbiBzdGFydHMgYmVmb3JlIHRoZSBsaW5lIGVuZGluZ+KAplxuICAgICAgICAgIGNoaWxkRmxvdy5ldmVudHNbaW5kZXhdWzFdLnN0YXJ0Lm9mZnNldCA8IGxpbmVTdGFydE9mZnNldCAmJlxuICAgICAgICAgIC8vIOKApmFuZCBlaXRoZXIgaXMgbm90IGVuZGVkIHlldOKAplxuICAgICAgICAgICghY2hpbGRGbG93LmV2ZW50c1tpbmRleF1bMV0uZW5kIHx8XG4gICAgICAgICAgICAvLyDigKZvciBlbmRzIGFmdGVyIGl0LlxuICAgICAgICAgICAgY2hpbGRGbG93LmV2ZW50c1tpbmRleF1bMV0uZW5kLm9mZnNldCA+IGxpbmVTdGFydE9mZnNldClcbiAgICAgICAgKSB7XG4gICAgICAgICAgLy8gRXhpdDogdGhlcmXigJlzIHN0aWxsIHNvbWV0aGluZyBvcGVuLCB3aGljaCBtZWFucyBpdOKAmXMgYSBsYXp5IGxpbmVcbiAgICAgICAgICAvLyBwYXJ0IG9mIHNvbWV0aGluZy5cbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBOb3RlOiB0aGlzIGFsZ29yaXRobSBmb3IgbW92aW5nIGV2ZW50cyBhcm91bmQgaXMgc2ltaWxhciB0byB0aGVcbiAgICAgIC8vIGFsZ29yaXRobSB3aGVuIGNsb3NpbmcgZmxvdyBpbiBgZG9jdW1lbnRDb250aW51ZWAuXG4gICAgICBjb25zdCBpbmRleEJlZm9yZUV4aXRzID0gc2VsZi5ldmVudHMubGVuZ3RoXG4gICAgICBsZXQgaW5kZXhCZWZvcmVGbG93ID0gaW5kZXhCZWZvcmVFeGl0c1xuICAgICAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICAgICAgbGV0IHNlZW5cbiAgICAgIC8qKiBAdHlwZSB7UG9pbnQgfCB1bmRlZmluZWR9ICovXG4gICAgICBsZXQgcG9pbnRcblxuICAgICAgLy8gRmluZCB0aGUgcHJldmlvdXMgY2h1bmsgKHRoZSBvbmUgYmVmb3JlIHRoZSBsYXp5IGxpbmUpLlxuICAgICAgd2hpbGUgKGluZGV4QmVmb3JlRmxvdy0tKSB7XG4gICAgICAgIGlmIChcbiAgICAgICAgICBzZWxmLmV2ZW50c1tpbmRleEJlZm9yZUZsb3ddWzBdID09PSAnZXhpdCcgJiZcbiAgICAgICAgICBzZWxmLmV2ZW50c1tpbmRleEJlZm9yZUZsb3ddWzFdLnR5cGUgPT09IHR5cGVzLmNodW5rRmxvd1xuICAgICAgICApIHtcbiAgICAgICAgICBpZiAoc2Vlbikge1xuICAgICAgICAgICAgcG9pbnQgPSBzZWxmLmV2ZW50c1tpbmRleEJlZm9yZUZsb3ddWzFdLmVuZFxuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBzZWVuID0gdHJ1ZVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGFzc2VydChwb2ludCwgJ2NvdWxkIG5vdCBmaW5kIHByZXZpb3VzIGZsb3cgY2h1bmsnKVxuXG4gICAgICBleGl0Q29udGFpbmVycyhjb250aW51ZWQpXG5cbiAgICAgIC8vIEZpeCBwb3NpdGlvbnMuXG4gICAgICBpbmRleCA9IGluZGV4QmVmb3JlRXhpdHNcblxuICAgICAgd2hpbGUgKGluZGV4IDwgc2VsZi5ldmVudHMubGVuZ3RoKSB7XG4gICAgICAgIHNlbGYuZXZlbnRzW2luZGV4XVsxXS5lbmQgPSB7Li4ucG9pbnR9XG4gICAgICAgIGluZGV4KytcbiAgICAgIH1cblxuICAgICAgLy8gSW5qZWN0IHRoZSBleGl0cyBlYXJsaWVyICh0aGV54oCZcmUgc3RpbGwgYWxzbyBhdCB0aGUgZW5kKS5cbiAgICAgIHNwbGljZShcbiAgICAgICAgc2VsZi5ldmVudHMsXG4gICAgICAgIGluZGV4QmVmb3JlRmxvdyArIDEsXG4gICAgICAgIDAsXG4gICAgICAgIHNlbGYuZXZlbnRzLnNsaWNlKGluZGV4QmVmb3JlRXhpdHMpXG4gICAgICApXG5cbiAgICAgIC8vIERpc2NhcmQgdGhlIGR1cGxpY2F0ZSBleGl0cy5cbiAgICAgIHNlbGYuZXZlbnRzLmxlbmd0aCA9IGluZGV4XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBzaXplXG4gICAqICAgU2l6ZS5cbiAgICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAgICogICBOb3RoaW5nLlxuICAgKi9cbiAgZnVuY3Rpb24gZXhpdENvbnRhaW5lcnMoc2l6ZSkge1xuICAgIGxldCBpbmRleCA9IHN0YWNrLmxlbmd0aFxuXG4gICAgLy8gRXhpdCBvcGVuIGNvbnRhaW5lcnMuXG4gICAgd2hpbGUgKGluZGV4LS0gPiBzaXplKSB7XG4gICAgICBjb25zdCBlbnRyeSA9IHN0YWNrW2luZGV4XVxuICAgICAgc2VsZi5jb250YWluZXJTdGF0ZSA9IGVudHJ5WzFdXG4gICAgICBhc3NlcnQoXG4gICAgICAgIGVudHJ5WzBdLmV4aXQsXG4gICAgICAgICdleHBlY3RlZCBgZXhpdGAgdG8gYmUgZGVmaW5lZCBvbiBjb250YWluZXIgY29uc3RydWN0J1xuICAgICAgKVxuICAgICAgZW50cnlbMF0uZXhpdC5jYWxsKHNlbGYsIGVmZmVjdHMpXG4gICAgfVxuXG4gICAgc3RhY2subGVuZ3RoID0gc2l6ZVxuICB9XG5cbiAgZnVuY3Rpb24gY2xvc2VGbG93KCkge1xuICAgIGFzc2VydChcbiAgICAgIHNlbGYuY29udGFpbmVyU3RhdGUsXG4gICAgICAnZXhwZWN0ZWQgYGNvbnRhaW5lclN0YXRlYCB0byBiZSBkZWZpbmVkIHdoZW4gY2xvc2luZyBmbG93J1xuICAgIClcbiAgICBhc3NlcnQoY2hpbGRGbG93LCAnZXhwZWN0ZWQgYGNoaWxkRmxvd2AgdG8gYmUgZGVmaW5lZCB3aGVuIGNsb3NpbmcgaXQnKVxuICAgIGNoaWxkRmxvdy53cml0ZShbY29kZXMuZW9mXSlcbiAgICBjaGlsZFRva2VuID0gdW5kZWZpbmVkXG4gICAgY2hpbGRGbG93ID0gdW5kZWZpbmVkXG4gICAgc2VsZi5jb250YWluZXJTdGF0ZS5fY2xvc2VGbG93ID0gdW5kZWZpbmVkXG4gIH1cbn1cblxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogICBDb250ZXh0LlxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqICAgVG9rZW5pemVyLlxuICovXG5mdW5jdGlvbiB0b2tlbml6ZUNvbnRhaW5lcihlZmZlY3RzLCBvaywgbm9rKSB7XG4gIC8vIEFsd2F5cyBwb3B1bGF0ZWQgYnkgZGVmYXVsdHMuXG4gIGFzc2VydChcbiAgICB0aGlzLnBhcnNlci5jb25zdHJ1Y3RzLmRpc2FibGUubnVsbCxcbiAgICAnZXhwZWN0ZWQgYGRpc2FibGUubnVsbGAgdG8gYmUgcG9wdWxhdGVkJ1xuICApXG4gIHJldHVybiBmYWN0b3J5U3BhY2UoXG4gICAgZWZmZWN0cyxcbiAgICBlZmZlY3RzLmF0dGVtcHQodGhpcy5wYXJzZXIuY29uc3RydWN0cy5kb2N1bWVudCwgb2ssIG5vayksXG4gICAgdHlwZXMubGluZVByZWZpeCxcbiAgICB0aGlzLnBhcnNlci5jb25zdHJ1Y3RzLmRpc2FibGUubnVsbC5pbmNsdWRlcygnY29kZUluZGVudGVkJylcbiAgICAgID8gdW5kZWZpbmVkXG4gICAgICA6IGNvbnN0YW50cy50YWJTaXplXG4gIClcbn1cbiJdLCJuYW1lcyI6WyJvayIsImFzc2VydCIsImZhY3RvcnlTcGFjZSIsIm1hcmtkb3duTGluZUVuZGluZyIsInNwbGljZSIsImNvZGVzIiwiY29uc3RhbnRzIiwidHlwZXMiLCJkb2N1bWVudCIsInRva2VuaXplIiwiaW5pdGlhbGl6ZURvY3VtZW50IiwiY29udGFpbmVyQ29uc3RydWN0IiwidG9rZW5pemVDb250YWluZXIiLCJlZmZlY3RzIiwic2VsZiIsInN0YWNrIiwiY29udGludWVkIiwiY2hpbGRGbG93IiwiY2hpbGRUb2tlbiIsImxpbmVTdGFydE9mZnNldCIsInN0YXJ0IiwiY29kZSIsImxlbmd0aCIsIml0ZW0iLCJjb250YWluZXJTdGF0ZSIsImNvbnRpbnVhdGlvbiIsImF0dGVtcHQiLCJkb2N1bWVudENvbnRpbnVlIiwiY2hlY2tOZXdDb250YWluZXJzIiwiX2Nsb3NlRmxvdyIsInVuZGVmaW5lZCIsImNsb3NlRmxvdyIsImluZGV4QmVmb3JlRXhpdHMiLCJldmVudHMiLCJpbmRleEJlZm9yZUZsb3ciLCJwb2ludCIsInR5cGUiLCJjaHVua0Zsb3ciLCJlbmQiLCJleGl0Q29udGFpbmVycyIsImluZGV4Iiwic2xpY2UiLCJkb2N1bWVudENvbnRpbnVlZCIsImN1cnJlbnRDb25zdHJ1Y3QiLCJjb25jcmV0ZSIsImZsb3dTdGFydCIsImludGVycnVwdCIsIkJvb2xlYW4iLCJfZ2ZtVGFibGVEeW5hbWljSW50ZXJydXB0SGFjayIsImNoZWNrIiwidGhlcmVJc0FOZXdDb250YWluZXIiLCJ0aGVyZUlzTm9OZXdDb250YWluZXIiLCJwYXJzZXIiLCJsYXp5Iiwibm93IiwibGluZSIsIm9mZnNldCIsImNvbnRhaW5lckNvbnRpbnVlIiwicHVzaCIsImVvZiIsImNvbnN1bWUiLCJmbG93IiwiZW50ZXIiLCJfdG9rZW5pemVyIiwiY29udGVudFR5cGUiLCJjb250ZW50VHlwZUZsb3ciLCJwcmV2aW91cyIsImZsb3dDb250aW51ZSIsIndyaXRlVG9DaGlsZCIsImV4aXQiLCJ0b2tlbiIsImVuZE9mRmlsZSIsInN0cmVhbSIsInNsaWNlU3RyZWFtIiwibmV4dCIsImRlZmluZVNraXAiLCJ3cml0ZSIsInNlZW4iLCJzaXplIiwiZW50cnkiLCJjYWxsIiwibm9rIiwiY29uc3RydWN0cyIsImRpc2FibGUiLCJudWxsIiwibGluZVByZWZpeCIsImluY2x1ZGVzIiwidGFiU2l6ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/flow.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const flow = {\n    tokenize: initializeFlow\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeFlow(effects) {\n    const self = this;\n    const initial = effects.attempt(// Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine, atBlankEnding, // Try to parse initial flow (essentially, only code).\n    effects.attempt(this.parser.constructs.flowInitial, afterConstruct, (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, effects.attempt(this.parser.constructs.flow, afterConstruct, effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)));\n    return initial;\n    /** @type {State} */ function atBlankEnding(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n    /** @type {State} */ function afterConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\nconst resolver = {\n    resolveAll: createResolver()\n};\nconst string = initializeFactory(\"string\");\nconst text = initializeFactory(\"text\");\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */ function initializeFactory(field) {\n    return {\n        resolveAll: createResolver(field === \"text\" ? resolveAllLineSuffixes : undefined),\n        tokenize: initializeText\n    };\n    /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */ function initializeText(effects) {\n        const self = this;\n        const constructs = this.parser.constructs[field];\n        const text = effects.attempt(constructs, start, notText);\n        return start;\n        /** @type {State} */ function start(code) {\n            return atBreak(code) ? text(code) : notText(code);\n        }\n        /** @type {State} */ function notText(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                effects.consume(code);\n                return;\n            }\n            effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n            effects.consume(code);\n            return data;\n        }\n        /** @type {State} */ function data(code) {\n            if (atBreak(code)) {\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n                return text(code);\n            }\n            // Data.\n            effects.consume(code);\n            return data;\n        }\n        /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */ function atBreak(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                return true;\n            }\n            const list = constructs[code];\n            let index = -1;\n            if (list) {\n                // Always populated by defaults.\n                (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), \"expected `disable.null` to be populated\");\n                while(++index < list.length){\n                    const item = list[index];\n                    if (!item.previous || item.previous.call(self, self.previous)) {\n                        return true;\n                    }\n                }\n            }\n            return false;\n        }\n    }\n}\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */ function createResolver(extraResolver) {\n    return resolveAllText;\n    /** @type {Resolver} */ function resolveAllText(events, context) {\n        let index = -1;\n        /** @type {number | undefined} */ let enter;\n        // A rather boring computation (to merge adjacent `data` events) which\n        // improves mm performance by 29%.\n        while(++index <= events.length){\n            if (enter === undefined) {\n                if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                    enter = index;\n                    index++;\n                }\n            } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                // Don’t do anything if there is one data token.\n                if (index !== enter + 2) {\n                    events[enter][1].end = events[index - 1][1].end;\n                    events.splice(enter + 2, index - enter - 2);\n                    index = enter + 2;\n                }\n                enter = undefined;\n            }\n        }\n        return extraResolver ? extraResolver(events, context) : events;\n    }\n}\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */ function resolveAllLineSuffixes(events, context) {\n    let eventIndex = 0 // Skip first.\n    ;\n    while(++eventIndex <= events.length){\n        if ((eventIndex === events.length || events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) && events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n            const data = events[eventIndex - 1][1];\n            const chunks = context.sliceStream(data);\n            let index = chunks.length;\n            let bufferIndex = -1;\n            let size = 0;\n            /** @type {boolean | undefined} */ let tabs;\n            while(index--){\n                const chunk = chunks[index];\n                if (typeof chunk === \"string\") {\n                    bufferIndex = chunk.length;\n                    while(chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space){\n                        size++;\n                        bufferIndex--;\n                    }\n                    if (bufferIndex) break;\n                    bufferIndex = -1;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n                    tabs = true;\n                    size++;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n                // Empty\n                } else {\n                    // Replacement character, exit.\n                    index++;\n                    break;\n                }\n            }\n            // Allow final trailing whitespace.\n            if (context._contentTypeTextTrailing && eventIndex === events.length) {\n                size = 0;\n            }\n            if (size) {\n                const token = {\n                    type: eventIndex === events.length || tabs || size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n                    start: {\n                        _bufferIndex: index ? bufferIndex : data.start._bufferIndex + bufferIndex,\n                        _index: data.start._index + index,\n                        line: data.end.line,\n                        column: data.end.column - size,\n                        offset: data.end.offset - size\n                    },\n                    end: {\n                        ...data.end\n                    }\n                };\n                data.end = {\n                    ...token.start\n                };\n                if (data.start.offset === data.end.offset) {\n                    Object.assign(data, token);\n                } else {\n                    events.splice(eventIndex, 0, [\n                        \"enter\",\n                        token,\n                        context\n                    ], [\n                        \"exit\",\n                        token,\n                        context\n                    ]);\n                    eventIndex += 2;\n                }\n            }\n            eventIndex++;\n        }\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/micromark/dev/lib/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(ssr)/./node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(ssr)/./node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */ \n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */ function parse(options) {\n    const settings = options || {};\n    const constructs = /** @type {FullNormalizedExtension} */ (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([\n        _constructs_js__WEBPACK_IMPORTED_MODULE_1__,\n        ...settings.extensions || []\n    ]);\n    /** @type {ParseContext} */ const parser = {\n        constructs,\n        content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n        defined: [],\n        document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n        flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n        lazy: {},\n        string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n        text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n    };\n    return parser;\n    /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */ function create(initial) {\n        return creator;\n        /** @type {Create} */ function creator(from) {\n            return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/postprocess.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/postprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ \n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */ function postprocess(events) {\n    while(!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)){\n    // Empty\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7Q0FFQyxHQUVxRDtBQUV0RDs7Ozs7Q0FLQyxHQUNNLFNBQVNDLFlBQVlDLE1BQU07SUFDaEMsTUFBTyxDQUFDRix1RUFBV0EsQ0FBQ0UsUUFBUztJQUMzQixRQUFRO0lBQ1Y7SUFFQSxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21pY3JvbWFyay9kZXYvbGliL3Bvc3Rwcm9jZXNzLmpzPzdiMDciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtFdmVudH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtzdWJ0b2tlbml6ZX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3VidG9rZW5pemUnXG5cbi8qKlxuICogQHBhcmFtIHtBcnJheTxFdmVudD59IGV2ZW50c1xuICogICBFdmVudHMuXG4gKiBAcmV0dXJucyB7QXJyYXk8RXZlbnQ+fVxuICogICBFdmVudHMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwb3N0cHJvY2VzcyhldmVudHMpIHtcbiAgd2hpbGUgKCFzdWJ0b2tlbml6ZShldmVudHMpKSB7XG4gICAgLy8gRW1wdHlcbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cbiJdLCJuYW1lcyI6WyJzdWJ0b2tlbml6ZSIsInBvc3Rwcm9jZXNzIiwiZXZlbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/preprocess.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/preprocess.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */ /**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */ \nconst search = /[\\0\\t\\n\\r]/g;\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */ function preprocess() {\n    let column = 1;\n    let buffer = \"\";\n    /** @type {boolean | undefined} */ let start = true;\n    /** @type {boolean | undefined} */ let atCarriageReturn;\n    return preprocessor;\n    /** @type {Preprocessor} */ // eslint-disable-next-line complexity\n    function preprocessor(value, encoding, end) {\n        /** @type {Array<Chunk>} */ const chunks = [];\n        /** @type {RegExpMatchArray | null} */ let match;\n        /** @type {number} */ let next;\n        /** @type {number} */ let startPosition;\n        /** @type {number} */ let endPosition;\n        /** @type {Code} */ let code;\n        value = buffer + (typeof value === \"string\" ? value.toString() : new TextDecoder(encoding || undefined).decode(value));\n        startPosition = 0;\n        buffer = \"\";\n        if (start) {\n            // To do: `markdown-rs` actually parses BOMs (byte order mark).\n            if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n                startPosition++;\n            }\n            start = undefined;\n        }\n        while(startPosition < value.length){\n            search.lastIndex = startPosition;\n            match = search.exec(value);\n            endPosition = match && match.index !== undefined ? match.index : value.length;\n            code = value.charCodeAt(endPosition);\n            if (!match) {\n                buffer = value.slice(startPosition);\n                break;\n            }\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf && startPosition === endPosition && atCarriageReturn) {\n                chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed);\n                atCarriageReturn = undefined;\n            } else {\n                if (atCarriageReturn) {\n                    chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n                    atCarriageReturn = undefined;\n                }\n                if (startPosition < endPosition) {\n                    chunks.push(value.slice(startPosition, endPosition));\n                    column += endPosition - startPosition;\n                }\n                switch(code){\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter);\n                            column++;\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht:\n                        {\n                            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize;\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab);\n                            while(column++ < next)chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace);\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed);\n                            column = 1;\n                            break;\n                        }\n                    default:\n                        {\n                            atCarriageReturn = true;\n                            column = 1;\n                        }\n                }\n            }\n            startPosition = endPosition + 1;\n        }\n        if (end) {\n            if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n            if (buffer) chunks.push(buffer);\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof);\n        }\n        return chunks;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcHJlcHJvY2Vzcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Ozs7Ozs7OztDQVdDLEdBRXFEO0FBRXRELE1BQU1FLFNBQVM7QUFFZjs7O0NBR0MsR0FDTSxTQUFTQztJQUNkLElBQUlDLFNBQVM7SUFDYixJQUFJQyxTQUFTO0lBQ2IsZ0NBQWdDLEdBQ2hDLElBQUlDLFFBQVE7SUFDWixnQ0FBZ0MsR0FDaEMsSUFBSUM7SUFFSixPQUFPQztJQUVQLHlCQUF5QixHQUN6QixzQ0FBc0M7SUFDdEMsU0FBU0EsYUFBYUMsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLEdBQUc7UUFDeEMseUJBQXlCLEdBQ3pCLE1BQU1DLFNBQVMsRUFBRTtRQUNqQixvQ0FBb0MsR0FDcEMsSUFBSUM7UUFDSixtQkFBbUIsR0FDbkIsSUFBSUM7UUFDSixtQkFBbUIsR0FDbkIsSUFBSUM7UUFDSixtQkFBbUIsR0FDbkIsSUFBSUM7UUFDSixpQkFBaUIsR0FDakIsSUFBSUM7UUFFSlIsUUFDRUosU0FDQyxRQUFPSSxVQUFVLFdBQ2RBLE1BQU1TLFFBQVEsS0FDZCxJQUFJQyxZQUFZVCxZQUFZVSxXQUFXQyxNQUFNLENBQUNaLE1BQUs7UUFFekRNLGdCQUFnQjtRQUNoQlYsU0FBUztRQUVULElBQUlDLE9BQU87WUFDVCwrREFBK0Q7WUFDL0QsSUFBSUcsTUFBTWEsVUFBVSxDQUFDLE9BQU90Qix3REFBS0EsQ0FBQ3VCLGVBQWUsRUFBRTtnQkFDakRSO1lBQ0Y7WUFFQVQsUUFBUWM7UUFDVjtRQUVBLE1BQU9MLGdCQUFnQk4sTUFBTWUsTUFBTSxDQUFFO1lBQ25DdEIsT0FBT3VCLFNBQVMsR0FBR1Y7WUFDbkJGLFFBQVFYLE9BQU93QixJQUFJLENBQUNqQjtZQUNwQk8sY0FDRUgsU0FBU0EsTUFBTWMsS0FBSyxLQUFLUCxZQUFZUCxNQUFNYyxLQUFLLEdBQUdsQixNQUFNZSxNQUFNO1lBQ2pFUCxPQUFPUixNQUFNYSxVQUFVLENBQUNOO1lBRXhCLElBQUksQ0FBQ0gsT0FBTztnQkFDVlIsU0FBU0ksTUFBTW1CLEtBQUssQ0FBQ2I7Z0JBQ3JCO1lBQ0Y7WUFFQSxJQUNFRSxTQUFTakIsd0RBQUtBLENBQUM2QixFQUFFLElBQ2pCZCxrQkFBa0JDLGVBQ2xCVCxrQkFDQTtnQkFDQUssT0FBT2tCLElBQUksQ0FBQzlCLHdEQUFLQSxDQUFDK0Isc0JBQXNCO2dCQUN4Q3hCLG1CQUFtQmE7WUFDckIsT0FBTztnQkFDTCxJQUFJYixrQkFBa0I7b0JBQ3BCSyxPQUFPa0IsSUFBSSxDQUFDOUIsd0RBQUtBLENBQUNnQyxjQUFjO29CQUNoQ3pCLG1CQUFtQmE7Z0JBQ3JCO2dCQUVBLElBQUlMLGdCQUFnQkMsYUFBYTtvQkFDL0JKLE9BQU9rQixJQUFJLENBQUNyQixNQUFNbUIsS0FBSyxDQUFDYixlQUFlQztvQkFDdkNaLFVBQVVZLGNBQWNEO2dCQUMxQjtnQkFFQSxPQUFRRTtvQkFDTixLQUFLakIsd0RBQUtBLENBQUNpQyxHQUFHO3dCQUFFOzRCQUNkckIsT0FBT2tCLElBQUksQ0FBQzlCLHdEQUFLQSxDQUFDa0Msb0JBQW9COzRCQUN0QzlCOzRCQUVBO3dCQUNGO29CQUVBLEtBQUtKLHdEQUFLQSxDQUFDbUMsRUFBRTt3QkFBRTs0QkFDYnJCLE9BQU9zQixLQUFLQyxJQUFJLENBQUNqQyxTQUFTSCw0REFBU0EsQ0FBQ3FDLE9BQU8sSUFBSXJDLDREQUFTQSxDQUFDcUMsT0FBTzs0QkFDaEUxQixPQUFPa0IsSUFBSSxDQUFDOUIsd0RBQUtBLENBQUN1QyxhQUFhOzRCQUMvQixNQUFPbkMsV0FBV1UsS0FBTUYsT0FBT2tCLElBQUksQ0FBQzlCLHdEQUFLQSxDQUFDd0MsWUFBWTs0QkFFdEQ7d0JBQ0Y7b0JBRUEsS0FBS3hDLHdEQUFLQSxDQUFDNkIsRUFBRTt3QkFBRTs0QkFDYmpCLE9BQU9rQixJQUFJLENBQUM5Qix3REFBS0EsQ0FBQ3lDLFFBQVE7NEJBQzFCckMsU0FBUzs0QkFFVDt3QkFDRjtvQkFFQTt3QkFBUzs0QkFDUEcsbUJBQW1COzRCQUNuQkgsU0FBUzt3QkFDWDtnQkFDRjtZQUNGO1lBRUFXLGdCQUFnQkMsY0FBYztRQUNoQztRQUVBLElBQUlMLEtBQUs7WUFDUCxJQUFJSixrQkFBa0JLLE9BQU9rQixJQUFJLENBQUM5Qix3REFBS0EsQ0FBQ2dDLGNBQWM7WUFDdEQsSUFBSTNCLFFBQVFPLE9BQU9rQixJQUFJLENBQUN6QjtZQUN4Qk8sT0FBT2tCLElBQUksQ0FBQzlCLHdEQUFLQSxDQUFDMEMsR0FBRztRQUN2QjtRQUVBLE9BQU85QjtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcHJlcHJvY2Vzcy5qcz9mZDcwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7Q2h1bmssIENvZGUsIEVuY29kaW5nLCBWYWx1ZX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgUHJlcHJvY2Vzc29yXG4gKiAgIFByZXByb2Nlc3MgYSB2YWx1ZS5cbiAqIEBwYXJhbSB7VmFsdWV9IHZhbHVlXG4gKiAgIFZhbHVlLlxuICogQHBhcmFtIHtFbmNvZGluZyB8IG51bGwgfCB1bmRlZmluZWR9IFtlbmNvZGluZ11cbiAqICAgRW5jb2Rpbmcgd2hlbiBgdmFsdWVgIGlzIGEgdHlwZWQgYXJyYXkgKG9wdGlvbmFsKS5cbiAqIEBwYXJhbSB7Ym9vbGVhbiB8IG51bGwgfCB1bmRlZmluZWR9IFtlbmQ9ZmFsc2VdXG4gKiAgIFdoZXRoZXIgdGhpcyBpcyB0aGUgbGFzdCBjaHVuayAoZGVmYXVsdDogYGZhbHNlYCkuXG4gKiBAcmV0dXJucyB7QXJyYXk8Q2h1bms+fVxuICogICBDaHVua3MuXG4gKi9cblxuaW1wb3J0IHtjb2RlcywgY29uc3RhbnRzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wnXG5cbmNvbnN0IHNlYXJjaCA9IC9bXFwwXFx0XFxuXFxyXS9nXG5cbi8qKlxuICogQHJldHVybnMge1ByZXByb2Nlc3Nvcn1cbiAqICAgUHJlcHJvY2VzcyBhIHZhbHVlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcHJlcHJvY2VzcygpIHtcbiAgbGV0IGNvbHVtbiA9IDFcbiAgbGV0IGJ1ZmZlciA9ICcnXG4gIC8qKiBAdHlwZSB7Ym9vbGVhbiB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IHN0YXJ0ID0gdHJ1ZVxuICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gIGxldCBhdENhcnJpYWdlUmV0dXJuXG5cbiAgcmV0dXJuIHByZXByb2Nlc3NvclxuXG4gIC8qKiBAdHlwZSB7UHJlcHJvY2Vzc29yfSAqL1xuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgY29tcGxleGl0eVxuICBmdW5jdGlvbiBwcmVwcm9jZXNzb3IodmFsdWUsIGVuY29kaW5nLCBlbmQpIHtcbiAgICAvKiogQHR5cGUge0FycmF5PENodW5rPn0gKi9cbiAgICBjb25zdCBjaHVua3MgPSBbXVxuICAgIC8qKiBAdHlwZSB7UmVnRXhwTWF0Y2hBcnJheSB8IG51bGx9ICovXG4gICAgbGV0IG1hdGNoXG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgbGV0IG5leHRcbiAgICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgICBsZXQgc3RhcnRQb3NpdGlvblxuICAgIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICAgIGxldCBlbmRQb3NpdGlvblxuICAgIC8qKiBAdHlwZSB7Q29kZX0gKi9cbiAgICBsZXQgY29kZVxuXG4gICAgdmFsdWUgPVxuICAgICAgYnVmZmVyICtcbiAgICAgICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnXG4gICAgICAgID8gdmFsdWUudG9TdHJpbmcoKVxuICAgICAgICA6IG5ldyBUZXh0RGVjb2RlcihlbmNvZGluZyB8fCB1bmRlZmluZWQpLmRlY29kZSh2YWx1ZSkpXG5cbiAgICBzdGFydFBvc2l0aW9uID0gMFxuICAgIGJ1ZmZlciA9ICcnXG5cbiAgICBpZiAoc3RhcnQpIHtcbiAgICAgIC8vIFRvIGRvOiBgbWFya2Rvd24tcnNgIGFjdHVhbGx5IHBhcnNlcyBCT01zIChieXRlIG9yZGVyIG1hcmspLlxuICAgICAgaWYgKHZhbHVlLmNoYXJDb2RlQXQoMCkgPT09IGNvZGVzLmJ5dGVPcmRlck1hcmtlcikge1xuICAgICAgICBzdGFydFBvc2l0aW9uKytcbiAgICAgIH1cblxuICAgICAgc3RhcnQgPSB1bmRlZmluZWRcbiAgICB9XG5cbiAgICB3aGlsZSAoc3RhcnRQb3NpdGlvbiA8IHZhbHVlLmxlbmd0aCkge1xuICAgICAgc2VhcmNoLmxhc3RJbmRleCA9IHN0YXJ0UG9zaXRpb25cbiAgICAgIG1hdGNoID0gc2VhcmNoLmV4ZWModmFsdWUpXG4gICAgICBlbmRQb3NpdGlvbiA9XG4gICAgICAgIG1hdGNoICYmIG1hdGNoLmluZGV4ICE9PSB1bmRlZmluZWQgPyBtYXRjaC5pbmRleCA6IHZhbHVlLmxlbmd0aFxuICAgICAgY29kZSA9IHZhbHVlLmNoYXJDb2RlQXQoZW5kUG9zaXRpb24pXG5cbiAgICAgIGlmICghbWF0Y2gpIHtcbiAgICAgICAgYnVmZmVyID0gdmFsdWUuc2xpY2Uoc3RhcnRQb3NpdGlvbilcbiAgICAgICAgYnJlYWtcbiAgICAgIH1cblxuICAgICAgaWYgKFxuICAgICAgICBjb2RlID09PSBjb2Rlcy5sZiAmJlxuICAgICAgICBzdGFydFBvc2l0aW9uID09PSBlbmRQb3NpdGlvbiAmJlxuICAgICAgICBhdENhcnJpYWdlUmV0dXJuXG4gICAgICApIHtcbiAgICAgICAgY2h1bmtzLnB1c2goY29kZXMuY2FycmlhZ2VSZXR1cm5MaW5lRmVlZClcbiAgICAgICAgYXRDYXJyaWFnZVJldHVybiA9IHVuZGVmaW5lZFxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKGF0Q2FycmlhZ2VSZXR1cm4pIHtcbiAgICAgICAgICBjaHVua3MucHVzaChjb2Rlcy5jYXJyaWFnZVJldHVybilcbiAgICAgICAgICBhdENhcnJpYWdlUmV0dXJuID0gdW5kZWZpbmVkXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoc3RhcnRQb3NpdGlvbiA8IGVuZFBvc2l0aW9uKSB7XG4gICAgICAgICAgY2h1bmtzLnB1c2godmFsdWUuc2xpY2Uoc3RhcnRQb3NpdGlvbiwgZW5kUG9zaXRpb24pKVxuICAgICAgICAgIGNvbHVtbiArPSBlbmRQb3NpdGlvbiAtIHN0YXJ0UG9zaXRpb25cbiAgICAgICAgfVxuXG4gICAgICAgIHN3aXRjaCAoY29kZSkge1xuICAgICAgICAgIGNhc2UgY29kZXMubnVsOiB7XG4gICAgICAgICAgICBjaHVua3MucHVzaChjb2Rlcy5yZXBsYWNlbWVudENoYXJhY3RlcilcbiAgICAgICAgICAgIGNvbHVtbisrXG5cbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY2FzZSBjb2Rlcy5odDoge1xuICAgICAgICAgICAgbmV4dCA9IE1hdGguY2VpbChjb2x1bW4gLyBjb25zdGFudHMudGFiU2l6ZSkgKiBjb25zdGFudHMudGFiU2l6ZVxuICAgICAgICAgICAgY2h1bmtzLnB1c2goY29kZXMuaG9yaXpvbnRhbFRhYilcbiAgICAgICAgICAgIHdoaWxlIChjb2x1bW4rKyA8IG5leHQpIGNodW5rcy5wdXNoKGNvZGVzLnZpcnR1YWxTcGFjZSlcblxuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjYXNlIGNvZGVzLmxmOiB7XG4gICAgICAgICAgICBjaHVua3MucHVzaChjb2Rlcy5saW5lRmVlZClcbiAgICAgICAgICAgIGNvbHVtbiA9IDFcblxuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICBhdENhcnJpYWdlUmV0dXJuID0gdHJ1ZVxuICAgICAgICAgICAgY29sdW1uID0gMVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBzdGFydFBvc2l0aW9uID0gZW5kUG9zaXRpb24gKyAxXG4gICAgfVxuXG4gICAgaWYgKGVuZCkge1xuICAgICAgaWYgKGF0Q2FycmlhZ2VSZXR1cm4pIGNodW5rcy5wdXNoKGNvZGVzLmNhcnJpYWdlUmV0dXJuKVxuICAgICAgaWYgKGJ1ZmZlcikgY2h1bmtzLnB1c2goYnVmZmVyKVxuICAgICAgY2h1bmtzLnB1c2goY29kZXMuZW9mKVxuICAgIH1cblxuICAgIHJldHVybiBjaHVua3NcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNvZGVzIiwiY29uc3RhbnRzIiwic2VhcmNoIiwicHJlcHJvY2VzcyIsImNvbHVtbiIsImJ1ZmZlciIsInN0YXJ0IiwiYXRDYXJyaWFnZVJldHVybiIsInByZXByb2Nlc3NvciIsInZhbHVlIiwiZW5jb2RpbmciLCJlbmQiLCJjaHVua3MiLCJtYXRjaCIsIm5leHQiLCJzdGFydFBvc2l0aW9uIiwiZW5kUG9zaXRpb24iLCJjb2RlIiwidG9TdHJpbmciLCJUZXh0RGVjb2RlciIsInVuZGVmaW5lZCIsImRlY29kZSIsImNoYXJDb2RlQXQiLCJieXRlT3JkZXJNYXJrZXIiLCJsZW5ndGgiLCJsYXN0SW5kZXgiLCJleGVjIiwiaW5kZXgiLCJzbGljZSIsImxmIiwicHVzaCIsImNhcnJpYWdlUmV0dXJuTGluZUZlZWQiLCJjYXJyaWFnZVJldHVybiIsIm51bCIsInJlcGxhY2VtZW50Q2hhcmFjdGVyIiwiaHQiLCJNYXRoIiwiY2VpbCIsInRhYlNpemUiLCJob3Jpem9udGFsVGFiIiwidmlydHVhbFNwYWNlIiwibGluZUZlZWQiLCJlb2YiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;