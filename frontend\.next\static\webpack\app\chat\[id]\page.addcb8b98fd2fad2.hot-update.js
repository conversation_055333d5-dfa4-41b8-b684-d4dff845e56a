"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/custom-file-tree.tsx":
/*!*****************************************!*\
  !*** ./components/custom-file-tree.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomFileTree: function() { return /* binding */ CustomFileTree; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_arborist__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-arborist */ \"(app-pages-browser)/./node_modules/react-arborist/dist/module/components/tree.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* __next_internal_client_entry_do_not_use__ CustomFileTree auto */ \n\n\nfunction Node(param) {\n    let { node, style, dragHandle } = param;\n    const getFileIcon = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#f7df1e]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 16\n                }, this);\n            case \"ts\":\n            case \"tsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#3178c6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, this);\n            case \"html\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#e34c26]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 16\n                }, this);\n            case \"css\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#1572b6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, this);\n            case \"json\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#cbcb41]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 16\n                }, this);\n            case \"md\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-[#083fa1]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 16\n                }, this);\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n            case \"gif\":\n            case \"svg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-[#4caf50]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-[#858585]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        ref: dragHandle,\n        className: \"flex items-center py-1 px-2 cursor-pointer hover:bg-[#2a2d2e] transition-colors text-sm \".concat(node.isSelected ? \"bg-[#37373d]\" : \"\"),\n        onClick: ()=>node.isInternal ? node.toggle() : node.select(),\n        children: [\n            node.isInternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 flex items-center justify-center mr-1\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-3 h-3 text-[#cccccc]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-3 h-3 text-[#cccccc]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4 text-[#dcb67a]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-[#dcb67a]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: getFileIcon(node.data.name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-[#cccccc] truncate font-mono\",\n                children: node.data.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            node.data.type === \"file\" && node.data.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-auto text-xs text-[#858585]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = Node;\nfunction CustomFileTree(param) {\n    let { data, onFileSelect, selectedPath } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-[#252526]\",\n        style: {\n            height: \"100%\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_arborist__WEBPACK_IMPORTED_MODULE_10__.Tree, {\n            data: data,\n            openByDefault: false,\n            width: 300,\n            height: 600,\n            indent: 16,\n            rowHeight: 24,\n            onSelect: (nodes)=>{\n                const node = nodes[0];\n                if (node && node.data.file && onFileSelect) {\n                    onFileSelect(node.data.file);\n                }\n            },\n            children: Node\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CustomFileTree;\nvar _c, _c1;\n$RefreshReg$(_c, \"Node\");\n$RefreshReg$(_c1, \"CustomFileTree\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/custom-file-tree.tsx\n"));

/***/ })

});