"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/custom-file-tree.tsx":
/*!*****************************************!*\
  !*** ./components/custom-file-tree.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomFileTree: function() { return /* binding */ CustomFileTree; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_arborist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-arborist */ \"(app-pages-browser)/./node_modules/react-arborist/dist/module/components/tree.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* __next_internal_client_entry_do_not_use__ CustomFileTree auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Node(param) {\n    let { node, style, dragHandle } = param;\n    const getFileIcon = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#f7df1e]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case \"ts\":\n            case \"tsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#3178c6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case \"html\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#e34c26]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            case \"css\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#1572b6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n            case \"json\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-[#cbcb41]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case \"md\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-[#083fa1]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n            case \"gif\":\n            case \"svg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-[#4caf50]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-[#858585]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        ref: dragHandle,\n        className: \"flex items-center py-1 px-2 cursor-pointer hover:bg-[#2a2d2e] transition-colors text-sm \".concat(node.isSelected ? \"bg-[#37373d]\" : \"\"),\n        onClick: ()=>node.isInternal ? node.toggle() : node.select(),\n        children: [\n            node.isInternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 flex items-center justify-center mr-1\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-3 h-3 text-[#cccccc]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-3 h-3 text-[#cccccc]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-[#dcb67a]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4 text-[#dcb67a]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: getFileIcon(node.data.name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-[#cccccc] truncate font-mono\",\n                children: node.data.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            node.data.type === \"file\" && node.data.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-auto text-xs text-[#858585]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = Node;\nfunction CustomFileTree(param) {\n    let { data, onFileSelect, selectedPath } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [dimensions, setDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 300,\n        height: 400\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateDimensions = ()=>{\n            if (containerRef.current) {\n                const { width, height } = containerRef.current.getBoundingClientRect();\n                setDimensions({\n                    width: Math.max(width, 200),\n                    height: Math.max(height, 200)\n                });\n            }\n        };\n        updateDimensions();\n        window.addEventListener(\"resize\", updateDimensions);\n        return ()=>window.removeEventListener(\"resize\", updateDimensions);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full bg-[#252526]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_arborist__WEBPACK_IMPORTED_MODULE_11__.Tree, {\n            data: data,\n            openByDefault: false,\n            width: dimensions.width,\n            height: dimensions.height,\n            indent: 16,\n            rowHeight: 24,\n            onSelect: (nodes)=>{\n                const node = nodes[0];\n                if (node && node.data.file && onFileSelect) {\n                    onFileSelect(node.data.file);\n                }\n            },\n            children: Node\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomFileTree, \"VOBevdEWdqvQFqks1QON/ZYTZDc=\");\n_c1 = CustomFileTree;\nvar _c, _c1;\n$RefreshReg$(_c, \"Node\");\n$RefreshReg$(_c1, \"CustomFileTree\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/custom-file-tree.tsx\n"));

/***/ })

});