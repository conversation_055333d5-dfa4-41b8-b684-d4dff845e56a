pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Silk Dark
  Author: <PERSON> (https://github.com/Misterio77)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme silk-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #0e3c46  Default Background
base01  #1D494E  Lighter Background (Used for status bars, line number and folding marks)
base02  #2A5054  Selection Background
base03  #587073  Comments, Invisibles, Line Highlighting
base04  #9DC8CD  Dark Foreground (Used for status bars)
base05  #C7DBDD  Default Foreground, Caret, Delimiters, Operators
base06  #CBF2F7  Light Foreground (Not often used)
base07  #D2FAFF  Light Background (Not often used)
base08  #fb6953  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fcab74  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fce380  Classes, Markup Bold, Search Text Background
base0B  #73d8ad  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #3fb2b9  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #46bddd  Functions, Methods, Attribute IDs, Headings
base0E  #756b8a  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #9b647b  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #C7DBDD;
  background: #0e3c46
}
.hljs::selection,
.hljs ::selection {
  background-color: #2A5054;
  color: #C7DBDD
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #587073 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #587073
}
/* base04 - #9DC8CD -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9DC8CD
}
/* base05 - #C7DBDD -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #C7DBDD
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #fb6953
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fcab74
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fce380
}
.hljs-strong {
  font-weight: bold;
  color: #fce380
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #73d8ad
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #3fb2b9
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #46bddd
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #756b8a
}
.hljs-emphasis {
  color: #756b8a;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #9b647b
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}