"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/custom-file-tree.tsx":
/*!*****************************************!*\
  !*** ./components/custom-file-tree.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomFileTree: function() { return /* binding */ CustomFileTree; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_arborist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-arborist */ \"(app-pages-browser)/./node_modules/react-arborist/dist/module/components/tree.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* __next_internal_client_entry_do_not_use__ CustomFileTree auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Node(param) {\n    let { node, style, dragHandle } = param;\n    const getFileIcon = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#f7df1e]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case \"ts\":\n            case \"tsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#3178c6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case \"html\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#e34c26]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            case \"css\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#1572b6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n            case \"json\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-[#cbcb41]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case \"md\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-[#083fa1]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n            case \"gif\":\n            case \"svg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-[#4caf50]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-[#858585]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        ref: dragHandle,\n        className: \"flex items-center py-1 px-2 cursor-pointer hover:bg-[#161b22] transition-colors text-sm \".concat(node.isSelected ? \"bg-[#21262d]\" : \"\"),\n        onClick: ()=>node.isInternal ? node.toggle() : node.select(),\n        children: [\n            node.isInternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 flex items-center justify-center mr-1\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-3 h-3 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-3 h-3 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: getFileIcon(node.data.name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-[#f0f6fc] truncate text-sm\",\n                children: node.data.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = Node;\nfunction CustomFileTree(param) {\n    let { data, onFileSelect, selectedPath } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [dimensions, setDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 300,\n        height: 400\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateDimensions = ()=>{\n            if (containerRef.current) {\n                const { width, height } = containerRef.current.getBoundingClientRect();\n                setDimensions({\n                    width: Math.max(width, 200),\n                    height: Math.max(height, 200)\n                });\n            }\n        };\n        updateDimensions();\n        window.addEventListener(\"resize\", updateDimensions);\n        return ()=>window.removeEventListener(\"resize\", updateDimensions);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full bg-[#0d1117]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_arborist__WEBPACK_IMPORTED_MODULE_11__.Tree, {\n            data: data,\n            openByDefault: false,\n            width: dimensions.width,\n            height: dimensions.height,\n            indent: 16,\n            rowHeight: 28,\n            onSelect: (nodes)=>{\n                const node = nodes[0];\n                if (node && node.data.file && onFileSelect) {\n                    onFileSelect(node.data.file);\n                }\n            },\n            children: Node\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomFileTree, \"VOBevdEWdqvQFqks1QON/ZYTZDc=\");\n_c1 = CustomFileTree;\nvar _c, _c1;\n$RefreshReg$(_c, \"Node\");\n$RefreshReg$(_c1, \"CustomFileTree\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/custom-file-tree.tsx\n"));

/***/ })

});