'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { chatAPI } from '@/lib/api'
import { Loader2 } from 'lucide-react'
import { ChatInterface } from '@/components/chat-interface'
import { FileExplorer } from '@/components/file-explorer'
import { WebsitePreview } from '@/components/website-preview'

export default function ChatPage({ params }: { params: { id: string } }) {
  const [chat, setChat] = useState(null)
  const [messages, setMessages] = useState([])
  const [versions, setVersions] = useState([])
  const [currentVersion, setCurrentVersion] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showPreview, setShowPreview] = useState(false)
  
  const { user, isLoading: authLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (authLoading) return
    if (!user) {
      router.push('/')
      return
    }
    loadData()
  }, [user, authLoading, params.id])

  const loadData = async () => {
    try {
      const chatData = await chatAPI.getChat(parseInt(params.id))
      setChat(chatData)
      
      const history = await chatAPI.getChatHistory(parseInt(params.id))
      setMessages(history.messages || [])
      
      const versionData = await chatAPI.getChatVersions(parseInt(params.id))
      setVersions(versionData)
      if (versionData.length > 0) {
        setCurrentVersion(versionData[versionData.length - 1])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="h-screen bg-[#0d1117] flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-[#7d8590]" />
      </div>
    )
  }

  if (!chat) {
    return (
      <div className="h-screen bg-[#0d1117] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-xl text-[#f0f6fc] mb-4">Chat not found</h1>
          <button 
            onClick={() => router.push('/dashboard')}
            className="bg-[#0969da] text-white px-4 py-2 rounded"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-[#0d1117] flex">
      {/* Chat Sidebar */}
      <div className="w-96 border-r border-[#21262d]">
        <ChatInterface 
          chat={chat}
          messages={messages}
          onNewMessage={(msg) => setMessages(prev => [...prev, msg])}
          onNewVersion={(ver) => {
            setVersions(prev => [...prev, ver])
            setCurrentVersion(ver)
          }}
        />
      </div>

      {/* Main Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4">
          <div className="flex space-x-2">
            <button
              onClick={() => setShowPreview(false)}
              className={`px-3 py-1 text-xs rounded ${!showPreview ? 'bg-[#0969da] text-white' : 'text-[#7d8590]'}`}
            >
              Code
            </button>
            <button
              onClick={() => setShowPreview(true)}
              className={`px-3 py-1 text-xs rounded ${showPreview ? 'bg-[#0969da] text-white' : 'text-[#7d8590]'}`}
            >
              Preview
            </button>
          </div>
          
          {versions.length > 0 && (
            <select
              value={currentVersion?.id || ''}
              onChange={(e) => {
                const version = versions.find(v => v.id.toString() === e.target.value)
                if (version) setCurrentVersion(version)
              }}
              className="bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]"
            >
              {versions.map((version) => (
                <option key={version.id} value={version.id}>
                  v{version.version_number}
                </option>
              ))}
            </select>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          <div className="w-80 border-r border-[#21262d]">
            <FileExplorer version={currentVersion} />
          </div>
          <div className="flex-1">
            {showPreview ? (
              <WebsitePreview version={currentVersion} />
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center text-[#7d8590]">
                  <div className="w-16 h-16 mx-auto mb-4 bg-[#21262d] rounded flex items-center justify-center">
                    <span className="text-2xl">📝</span>
                  </div>
                  <h3 className="text-lg text-[#f0f6fc] mb-2">Code Editor</h3>
                  <p>Select a file to view its content</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
