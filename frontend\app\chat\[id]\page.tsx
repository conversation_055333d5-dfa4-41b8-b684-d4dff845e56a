'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { chatAPI, type Chat, type Message, type Version, type File } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import { ChatInterface } from '@/components/chat-interface'
import { FileExplorer } from '@/components/file-explorer'
import { WebsitePreview } from '@/components/website-preview'
import { ChatHeader } from '@/components/chat-header'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import { Loader2 } from 'lucide-react'
import Editor from '@monaco-editor/react'

interface ChatPageProps {
  params: { id: string }
}

export default function ChatPage({ params }: ChatPageProps) {
  const [chat, setChat] = useState<Chat | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [versions, setVersions] = useState<Version[]>([])
  const [currentVersion, setCurrentVersion] = useState<Version | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showPreview, setShowPreview] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const { user, isLoading: authLoading } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()
  const initialPromptSent = useRef(false)

  useEffect(() => {
    // Wait for auth to finish loading
    if (authLoading) return

    if (!user) {
      router.push('/')
      return
    }
    loadChatData()
  }, [user, authLoading, params.id])

  useEffect(() => {
    // Send initial prompt if provided in URL
    const prompt = searchParams.get('prompt')
    if (prompt && chat && !initialPromptSent.current) {
      initialPromptSent.current = true
      // This will be handled by the ChatInterface component
    }
  }, [chat, searchParams])

  const loadChatData = async () => {
    try {
      const chatId = parseInt(params.id)

      // Load chat details
      const chatData = await chatAPI.getChat(chatId)
      setChat(chatData)

      // Load chat history
      const historyData = await chatAPI.getChatHistory(chatId)
      setMessages(historyData.messages || [])

      // Load versions
      const versionsData = await chatAPI.getChatVersions(chatId)
      setVersions(versionsData)

      // Set current version to the latest one
      if (versionsData.length > 0) {
        setCurrentVersion(versionsData[versionsData.length - 1])
      }
    } catch (error) {
      console.error('Error loading chat data:', error)
      toast({
        title: "Error",
        description: "Failed to load chat data. Please try refreshing the page.",
        variant: "destructive"
      })
      // Don't redirect on error, just show error state
    } finally {
      setIsLoading(false)
    }
  }

  const handleNewMessage = (message: Message) => {
    setMessages(prev => [...prev, message])
  }

  const handleNewVersion = (version: Version) => {
    setVersions(prev => [...prev, version])
    setCurrentVersion(version)
  }

  const handleVersionChange = (version: Version) => {
    setCurrentVersion(version)
  }

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
  }

  const getFileLanguage = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'js': case 'jsx': return 'javascript'
      case 'ts': case 'tsx': return 'typescript'
      case 'html': return 'html'
      case 'css': return 'css'
      case 'json': return 'json'
      case 'md': return 'markdown'
      case 'py': return 'python'
      case 'php': return 'php'
      case 'sql': return 'sql'
      case 'xml': return 'xml'
      case 'yaml': case 'yml': return 'yaml'
      default: return 'plaintext'
    }
  }

  if (authLoading || (!user && !authLoading)) {
    return (
      <div className="min-h-screen bg-[#0d1117] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-[#7d8590] mb-2" />
          <p className="text-[#7d8590]">Loading...</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0d1117] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-[#7d8590] mb-2" />
          <p className="text-[#7d8590]">Loading chat...</p>
        </div>
      </div>
    )
  }

  if (!chat && !isLoading) {
    return (
      <div className="min-h-screen bg-[#0d1117] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2 text-[#f0f6fc]">Chat not found</h1>
          <p className="text-[#7d8590] mb-4">The chat you're looking for doesn't exist or failed to load.</p>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-[#0d1117] flex">
      {/* Left Sidebar - Chat */}
      <div className="w-96 bg-[#0d1117] border-r border-[#21262d] flex flex-col">
        <ChatInterface
          chat={chat}
          messages={messages}
          onNewMessage={handleNewMessage}
          onNewVersion={handleNewVersion}
          initialPrompt={searchParams.get('prompt')}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Header */}
        <div className="h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4">
          {/* Left - Toggle Buttons */}
          <div className="flex items-center space-x-2">
            <button className="p-1 text-[#7d8590] hover:text-[#f0f6fc]">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 16 16">
                <path d="M1.75 2.5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5zm0 5h12.5a.75.75 0 110 1.5H1.75a.75.75 0 010-1.5z"/>
              </svg>
            </button>

            <div className="flex items-center space-x-1 bg-[#21262d] rounded-md p-1">
              <button
                onClick={() => setShowPreview(true)}
                className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
                  showPreview
                    ? 'bg-[#0969da] text-white'
                    : 'text-[#7d8590] hover:text-[#f0f6fc]'
                }`}
              >
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
                  <path fillRule="evenodd" d="M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z"/>
                </svg>
                <span>Preview</span>
              </button>

              <button
                onClick={() => setShowPreview(false)}
                className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
                  !showPreview
                    ? 'bg-[#0969da] text-white'
                    : 'text-[#7d8590] hover:text-[#f0f6fc]'
                }`}
              >
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z"/>
                </svg>
                <span>Code</span>
              </button>
            </div>
          </div>

          {/* Right - Actions */}
          <div className="flex items-center space-x-2">
            {versions.length > 0 && (
              <select
                value={currentVersion?.id || ''}
                onChange={(e) => {
                  const version = versions.find(v => v.id === e.target.value)
                  if (version) handleVersionChange(version)
                }}
                className="bg-[#21262d] text-[#f0f6fc] text-xs px-2 py-1 rounded border border-[#30363d]"
              >
                {versions.map((version) => (
                  <option key={version.id} value={version.id}>
                    v{version.version_number}
                  </option>
                ))}
              </select>
            )}

            <button className="p-1 text-[#7d8590] hover:text-[#f0f6fc]">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex">
          <PanelGroup direction="horizontal">
            {/* File Explorer */}
            <Panel defaultSize={35} minSize={25} maxSize={50}>
              <div className="h-full bg-[#0d1117]">
                <FileExplorer
                  version={currentVersion}
                  onFileSelect={handleFileSelect}
                />
              </div>
            </Panel>

            {/* Resize Handle */}
            <PanelResizeHandle className="w-1 bg-[#21262d] hover:bg-[#0969da] transition-colors cursor-col-resize" />

            {/* Main Content - Code Editor or Website Preview */}
            <Panel defaultSize={65} minSize={50}>
              <div className="h-full bg-[#0d1117] flex flex-col">
                {showPreview ? (
                  <WebsitePreview version={currentVersion} />
                ) : (
                  <>
                    {/* Code Editor Header */}
                    <div className="h-12 bg-[#161b22] border-b border-[#21262d] flex items-center px-4">
                      <div className="flex items-center space-x-2">
                        <svg className="w-4 h-4 text-[#7d8590]" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z" clipRule="evenodd" />
                        </svg>
                        <span className="text-[#f0f6fc] text-sm font-medium">
                          {selectedFile ? selectedFile.path : 'No file selected'}
                        </span>
                        {selectedFile && (
                          <span className="text-xs text-[#7d8590] ml-auto">
                            +5 / -5
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Code Editor Content */}
                    <div className="flex-1">
                      {selectedFile ? (
                        <Editor
                          height="100%"
                          language={getFileLanguage(selectedFile.path)}
                          value={selectedFile.content || '// No content available'}
                          theme="vs-dark"
                          options={{
                            readOnly: true,
                            minimap: { enabled: false },
                            scrollBeyondLastLine: false,
                            fontSize: 14,
                            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                            lineNumbers: 'on',
                            glyphMargin: false,
                            folding: true,
                            lineDecorationsWidth: 0,
                            lineNumbersMinChars: 3,
                            renderLineHighlight: 'line',
                            selectOnLineNumbers: true,
                            wordWrap: 'on',
                            automaticLayout: true,
                          }}
                        />
                      ) : (
                        <div className="h-full flex items-center justify-center">
                          <div className="text-center">
                            <svg className="w-16 h-16 mx-auto mb-4 text-[#7d8590]" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06L11.28 3.22z" clipRule="evenodd" />
                            </svg>
                            <h3 className="text-lg font-medium text-[#f0f6fc] mb-2">Code Editor</h3>
                            <p className="text-[#7d8590]">Select a file from the explorer to view its content</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </Panel>
          </PanelGroup>
        </div>
      </div>
    </div>
  )
}
