'use client'

import { useState, useEffect } from 'react'
import { chatAPI, type Version, type File } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import Editor from "@monaco-editor/react"
import { CustomFileTree } from './custom-file-tree'
import {
  Loader2,
  FileText,
  Code2,
  Download,
  Folder
} from 'lucide-react'

interface FileExplorerProps {
  version: Version | null
  onFileSelect?: (file: File) => void
}

interface TreeNode {
  id: string
  name: string
  isOpen?: boolean
  children?: TreeNode[]
  file?: File
}

export function FileExplorer({ version, onFileSelect }: FileExplorerProps) {
  const [files, setFiles] = useState<File[]>([])
  const [treeData, setTreeData] = useState<TreeNode[]>([])
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (version) {
      loadFiles()
    } else {
      setFiles([])
      setTreeData([])
      setSelectedFile(null)
    }
  }, [version])

  const loadFiles = async () => {
    if (!version) return

    setIsLoading(true)
    try {
      const data = await chatAPI.getVersionPreview(version.chat_id, version.id)
      setFiles(data.files || [])
      buildFileTree(data.files || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load files",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const buildFileTree = (files: File[]) => {
    const nodeMap = new Map<string, TreeNode>()
    const rootNodes: TreeNode[] = []

    // Sort files by path
    const sortedFiles = files.sort((a, b) => a.path.localeCompare(b.path))

    for (const file of sortedFiles) {
      const pathParts = file.path.split('/').filter(Boolean)
      let currentPath = ''

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i]
        const parentPath = currentPath
        currentPath = currentPath ? `${currentPath}/${part}` : part
        const isFile = i === pathParts.length - 1

        if (!nodeMap.has(currentPath)) {
          const newNode: TreeNode = {
            id: currentPath,
            name: part,
            type: isFile ? 'file' : 'folder',
            path: currentPath,
            children: isFile ? undefined : [],
            file: isFile ? file : undefined
          }

          if (parentPath === '') {
            // Root level
            rootNodes.push(newNode)
          } else {
            const parentNode = nodeMap.get(parentPath)
            if (parentNode && parentNode.children) {
              parentNode.children.push(newNode)
            }
          }

          nodeMap.set(currentPath, newNode)
        }
      }
    }

    setTreeData(rootNodes)
  }

  const getFileLanguage = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase()

    switch (ext) {
      case 'js': return 'javascript'
      case 'ts': return 'typescript'
      case 'jsx': return 'javascript'
      case 'tsx': return 'typescript'
      case 'html': return 'html'
      case 'css': return 'css'
      case 'json': return 'json'
      case 'md': return 'markdown'
      case 'py': return 'python'
      case 'java': return 'java'
      case 'cpp': case 'c': return 'cpp'
      case 'php': return 'php'
      case 'sql': return 'sql'
      case 'xml': return 'xml'
      case 'yaml': case 'yml': return 'yaml'
      default: return 'plaintext'
    }
  }

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    if (onFileSelect) {
      onFileSelect(file)
    }
  }



  if (!version) {
    return (
      <div className="h-full flex items-center justify-center text-muted-foreground">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>No version selected</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-[#0d1117]">
      {/* Header */}
      <div className="p-3 border-b border-[#21262d]">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm text-[#f0f6fc] flex items-center space-x-2">
            <Folder className="w-4 h-4" />
            <span>app</span>
          </h3>
          <div className="flex items-center space-x-1">
            <button className="p-1 text-[#7d8590] hover:text-[#f0f6fc] rounded">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 0a8.2 8.2 0 0 1 .701.031C9.444.095 9.99.645 10.16 1.29l.288 1.107c.018.066.079.158.212.224.231.114.454.243.668.386.123.082.233.09.299.071l1.103-.303c.644-.176 1.392.021 1.82.63.27.385.506.792.704 1.218.315.675.111 1.422-.364 1.891l-.814.806c-.049.048-.098.147-.088.294.016.257.016.515 0 .772-.01.147.039.246.088.294l.814.806c.475.469.679 1.216.364 1.891a7.977 7.977 0 0 1-.704 1.218c-.428.609-1.176.806-1.82.63l-1.103-.303c-.066-.019-.176-.011-.299.071a4.909 4.909 0 0 1-.668.386c-.133.066-.194.158-.212.224l-.288 1.107c-.17.645-.716 1.195-1.459 1.26a8.006 8.006 0 0 1-1.402 0c-.743-.065-1.289-.615-1.459-1.26L5.482 11.3c-.018-.066-.079-.158-.212-.224a4.738 4.738 0 0 1-.668-.386c-.123-.082-.233-.09-.299-.071l-1.103.303c-.644.176-1.392-.021-1.82-.63a8.12 8.12 0 0 1-.704-1.218c-.315-.675-.111-1.422.363-1.891l.815-.806c.05-.048.098-.147.088-.294a6.214 6.214 0 0 1 0-.772c.01-.147-.038-.246-.088-.294l-.815-.806C.635 6.045.431 5.298.746 4.623a7.92 7.92 0 0 1 .704-1.217c.428-.61 1.176-.807 1.82-.63l1.103.302c.066.019.176.011.299-.071.214-.143.437-.272.668-.386.133-.066.194-.158.212-.224L5.84 1.29C6.009.645 6.556.095 7.299.03 7.53.01 7.764 0 8 0Zm-.571 1.525c-.036.003-.108.036-.137.146l-.289 1.105c-.147.561-.549.967-.998 1.189-.173.086-.34.183-.5.29-.417.278-.97.423-1.529.27l-1.103-.303c-.109-.03-.175.016-.195.045-.22.312-.412.644-.573.99-.014.031-.021.11.059.19l.815.806c.411.406.562.957.53 1.456a4.709 4.709 0 0 0 0 .582c.032.499-.119 1.05-.53 1.456l-.815.806c-.081.08-.073.159-.059.19.161.346.353.677.573.989.02.03.085.076.195.046l1.103-.303c.559-.153 1.112-.008 1.529.27.16.107.327.204.5.29.449.222.851.628.998 1.189l.289 1.105c.029.109.101.143.137.146a6.6 6.6 0 0 0 1.142 0c.036-.003.108-.036.137-.146l.289-1.105c.147-.561.549-.967.998-1.189.173-.086.34-.183.5-.29.417-.278.97-.423 1.529-.27l1.103.303c.109.029.175-.016.195-.045.22-.313.411-.644.573-.99.014-.031.021-.11-.059-.19l-.815-.806c-.411-.406-.562-.957-.53-1.456a4.709 4.709 0 0 0 0-.582c-.032-.499.119-1.05.53-1.456l.815-.806c.081-.08.073-.159.059-.19a6.464 6.464 0 0 0-.573-.989c-.02-.03-.085-.076-.195-.046l-1.103.303c-.559.153-1.112.008-1.529-.27a4.44 4.44 0 0 0-.5-.29c-.449-.222-.851-.628-.998-1.189L8.708 1.67c-.029-.109-.101-.143-.137-.146a6.6 6.6 0 0 0-1.142 0ZM8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6Zm0-1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full p-2">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-[#7d8590]" />
            </div>
          ) : !treeData || treeData.length === 0 ? (
            <div className="text-center py-8 text-[#7d8590]">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No files yet</p>
            </div>
          ) : (
            <CustomFileTree
              data={treeData}
              onFileSelect={handleFileSelect}
              selectedPath={selectedFile?.path}
            />
          )}
        </div>
      </div>


    </div>
  )
}
