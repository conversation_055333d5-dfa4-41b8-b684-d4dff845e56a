'use client'

import { useState, useEffect } from 'react'
import { chatAPI, type Version, type File } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import { 
  Folder, 
  FolderOpen, 
  FileText, 
  Loader2,
  ChevronRight,
  ChevronDown
} from 'lucide-react'

interface FileExplorerProps {
  version: Version | null
}

interface TreeNode {
  id: string
  name: string
  type: 'file' | 'folder'
  path: string
  children?: TreeNode[]
  isOpen?: boolean
  file?: File
}

export function FileExplorer({ version }: FileExplorerProps) {
  const [files, setFiles] = useState<File[]>([])
  const [treeData, setTreeData] = useState<TreeNode[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    if (version) {
      loadFiles()
    }
  }, [version])

  const loadFiles = async () => {
    if (!version) return

    setIsLoading(true)
    try {
      const filesData = await chatAPI.getVersionFiles(version.id)
      setFiles(filesData)
      setTreeData(buildFileTree(filesData))
    } catch (error) {
      console.error('Error loading files:', error)
      toast({
        title: "Error",
        description: "Failed to load files",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const buildFileTree = (files: File[]): TreeNode[] => {
    const tree: TreeNode[] = []
    const pathMap = new Map<string, TreeNode>()

    files.forEach(file => {
      const pathParts = file.path.split('/')
      let currentPath = ''

      pathParts.forEach((part, index) => {
        const parentPath = currentPath
        currentPath = currentPath ? `${currentPath}/${part}` : part

        if (!pathMap.has(currentPath)) {
          const isFile = index === pathParts.length - 1
          const node: TreeNode = {
            id: currentPath,
            name: part,
            type: isFile ? 'file' : 'folder',
            path: currentPath,
            children: isFile ? undefined : [],
            isOpen: false,
            file: isFile ? file : undefined
          }

          pathMap.set(currentPath, node)

          if (parentPath) {
            const parent = pathMap.get(parentPath)
            if (parent && parent.children) {
              parent.children.push(node)
            }
          } else {
            tree.push(node)
          }
        }
      })
    })

    return tree
  }

  const toggleFolder = (path: string) => {
    const updateTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map(node => {
        if (node.path === path) {
          return { ...node, isOpen: !node.isOpen }
        }
        if (node.children) {
          return { ...node, children: updateTree(node.children) }
        }
        return node
      })
    }
    setTreeData(updateTree(treeData))
  }

  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <FileText className="w-4 h-4 text-[#f7df1e]" />
      case 'html':
        return <FileText className="w-4 h-4 text-[#e34c26]" />
      case 'css':
        return <FileText className="w-4 h-4 text-[#1572b6]" />
      case 'json':
        return <FileText className="w-4 h-4 text-[#000000]" />
      case 'md':
        return <FileText className="w-4 h-4 text-[#083fa1]" />
      default:
        return <FileText className="w-4 h-4 text-[#7d8590]" />
    }
  }

  const renderTree = (nodes: TreeNode[], level = 0) => {
    return nodes.map(node => (
      <div key={node.id}>
        <div
          className="flex items-center py-1 px-2 hover:bg-[#161b22] cursor-pointer text-sm"
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => node.type === 'folder' ? toggleFolder(node.path) : null}
        >
          {node.type === 'folder' ? (
            <>
              <div className="w-4 h-4 flex items-center justify-center mr-1">
                {node.isOpen ? (
                  <ChevronDown className="w-3 h-3 text-[#7d8590]" />
                ) : (
                  <ChevronRight className="w-3 h-3 text-[#7d8590]" />
                )}
              </div>
              <div className="mr-2">
                {node.isOpen ? (
                  <FolderOpen className="w-4 h-4 text-[#7d8590]" />
                ) : (
                  <Folder className="w-4 h-4 text-[#7d8590]" />
                )}
              </div>
            </>
          ) : (
            <>
              <div className="w-4 mr-1" />
              <div className="mr-2">
                {getFileIcon(node.name)}
              </div>
            </>
          )}
          <span className="text-[#f0f6fc] truncate text-sm">
            {node.name}
          </span>
        </div>
        {node.type === 'folder' && node.isOpen && node.children && (
          <div>
            {renderTree(node.children, level + 1)}
          </div>
        )}
      </div>
    ))
  }

  if (!version) {
    return (
      <div className="h-full flex items-center justify-center text-[#7d8590]">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>No version selected</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-[#0d1117]">
      {/* Header */}
      <div className="p-3 border-b border-[#21262d]">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm text-[#f0f6fc] flex items-center space-x-2">
            <Folder className="w-4 h-4" />
            <span>app</span>
          </h3>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto p-2">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-[#7d8590]" />
          </div>
        ) : !treeData || treeData.length === 0 ? (
          <div className="text-center py-8 text-[#7d8590]">
            <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No files yet</p>
          </div>
        ) : (
          <div className="space-y-1">
            {renderTree(treeData)}
          </div>
        )}
      </div>
    </div>
  )
}
