{"c": ["app/layout", "app/chat/[id]/page", "webpack"], "r": ["app/not-found"], "m": ["(app-pages-browser)/./components/chat-header.tsx", "(app-pages-browser)/./components/ui/dropdown-menu.tsx", "(app-pages-browser)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "(app-pages-browser)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-arrow/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-popper/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CASUS%5CDesktop%5CCodora%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2Fnot-found!", "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js"]}