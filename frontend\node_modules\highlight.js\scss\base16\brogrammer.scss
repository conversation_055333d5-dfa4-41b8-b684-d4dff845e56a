pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Brogrammer
  Author: <PERSON><PERSON> (http://github.com/piggyslasher)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme brogrammer
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1f1f1f  Default Background
base01  #f81118  Lighter Background (Used for status bars, line number and folding marks)
base02  #2dc55e  Selection Background
base03  #ecba0f  Comments, Invisibles, Line Highlighting
base04  #2a84d2  Dark Foreground (Used for status bars)
base05  #4e5ab7  Default Foreground, Caret, Delimiters, Operators
base06  #1081d6  Light Foreground (Not often used)
base07  #d6dbe5  Light Background (Not often used)
base08  #d6dbe5  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #de352e  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #1dd361  Classes, Markup Bold, Search Text Background
base0B  #f3bd09  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #1081d6  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #5350b9  Functions, Methods, Attribute IDs, Headings
base0E  #0f7ddb  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #ffffff  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #4e5ab7;
  background: #1f1f1f
}
.hljs::selection,
.hljs ::selection {
  background-color: #2dc55e;
  color: #4e5ab7
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #ecba0f -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #ecba0f
}
/* base04 - #2a84d2 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #2a84d2
}
/* base05 - #4e5ab7 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #4e5ab7
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d6dbe5
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #de352e
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #1dd361
}
.hljs-strong {
  font-weight: bold;
  color: #1dd361
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #f3bd09
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #1081d6
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #5350b9
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #0f7ddb
}
.hljs-emphasis {
  color: #0f7ddb;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #ffffff
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}