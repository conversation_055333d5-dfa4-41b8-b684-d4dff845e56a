"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-resizable-panels";
exports.ids = ["vendor-chunks/react-resizable-panels"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Panel: () => (/* binding */ Panel),\n/* harmony export */   PanelGroup: () => (/* binding */ PanelGroup),\n/* harmony export */   PanelResizeHandle: () => (/* binding */ PanelResizeHandle),\n/* harmony export */   getAvailableGroupSizePixels: () => (/* binding */ getAvailableGroupSizePixels)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// This module exists to work around Webpack issue https://github.com/webpack/webpack/issues/14814\n// eslint-disable-next-line no-restricted-imports\nconst { createElement, createContext, forwardRef, useCallback, useContext, useEffect, useImperativeHandle, useLayoutEffect, useMemo, useRef, useState } = react__WEBPACK_IMPORTED_MODULE_0__;\n// `toString()` prevents bundlers from trying to `import { useId } from 'react'`\nconst useId = react__WEBPACK_IMPORTED_MODULE_0__[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : ()=>null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n    const idFromUseId = wrappedUseId();\n    const idRef = useRef(idFromParams || idFromUseId || null);\n    if (idRef.current === null) {\n        idRef.current = \"\" + counter++;\n    }\n    return idRef.current;\n}\nconst PanelGroupContext = createContext(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\nfunction PanelWithForwardedRef({ children = null, className: classNameFromProps = \"\", collapsedSize = 0, collapsible = false, defaultSize = null, forwardedRef, id: idFromProps = null, maxSize = null, minSize, onCollapse = null, onResize = null, order = null, style: styleFromProps = {}, tagName: Type = \"div\" }) {\n    const context = useContext(PanelGroupContext);\n    if (context === null) {\n        throw Error(`Panel components must be rendered within a PanelGroup container`);\n    }\n    const panelId = useUniqueId(idFromProps);\n    const { collapsePanel, expandPanel, getPanelSize, getPanelStyle, registerPanel, resizePanel, units, unregisterPanel } = context;\n    if (minSize == null) {\n        if (units === \"percentages\") {\n            // Mimics legacy default value for percentage based panel groups\n            minSize = 10;\n        } else {\n            // There is no meaningful minimum pixel default we can provide\n            minSize = 0;\n        }\n    }\n    // Use a ref to guard against users passing inline props\n    const callbacksRef = useRef({\n        onCollapse,\n        onResize\n    });\n    useEffect(()=>{\n        callbacksRef.current.onCollapse = onCollapse;\n        callbacksRef.current.onResize = onResize;\n    });\n    const style = getPanelStyle(panelId, defaultSize);\n    const committedValuesRef = useRef({\n        size: parseSizeFromStyle(style)\n    });\n    useRef({\n        callbacksRef,\n        collapsedSize,\n        collapsible,\n        defaultSize,\n        id: panelId,\n        idWasAutoGenerated: idFromProps == null,\n        maxSize,\n        minSize,\n        order\n    });\n    useImperativeHandle(forwardedRef, ()=>({\n            collapse: ()=>collapsePanel(panelId),\n            expand: ()=>expandPanel(panelId),\n            getCollapsed () {\n                return committedValuesRef.current.size === 0;\n            },\n            getId () {\n                return panelId;\n            },\n            getSize (units) {\n                return getPanelSize(panelId, units);\n            },\n            resize: (percentage, units)=>resizePanel(panelId, percentage, units)\n        }), [\n        collapsePanel,\n        expandPanel,\n        getPanelSize,\n        panelId,\n        resizePanel\n    ]);\n    return createElement(Type, {\n        children,\n        className: classNameFromProps,\n        \"data-panel\": \"\",\n        \"data-panel-collapsible\": collapsible || undefined,\n        \"data-panel-id\": panelId,\n        \"data-panel-size\": parseFloat(\"\" + style.flexGrow).toFixed(1),\n        id: `data-panel-id-${panelId}`,\n        style: {\n            ...style,\n            ...styleFromProps\n        }\n    });\n}\nconst Panel = forwardRef((props, ref)=>createElement(PanelWithForwardedRef, {\n        ...props,\n        forwardedRef: ref\n    }));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n// HACK\nfunction parseSizeFromStyle(style) {\n    const { flexGrow } = style;\n    if (typeof flexGrow === \"string\") {\n        return parseFloat(flexGrow);\n    } else {\n        return flexGrow;\n    }\n}\nconst PRECISION = 10;\nfunction adjustByDelta(event, committedValues, idBefore, idAfter, deltaPixels, prevSizes, panelSizeBeforeCollapse, initialDragState) {\n    const { id: groupId, panels, units } = committedValues;\n    const groupSizePixels = units === \"pixels\" ? getAvailableGroupSizePixels(groupId) : NaN;\n    const { sizes: initialSizes } = initialDragState || {};\n    // If we're resizing by mouse or touch, use the initial sizes as a base.\n    // This has the benefit of causing force-collapsed panels to spring back open if drag is reversed.\n    const baseSizes = initialSizes || prevSizes;\n    const panelsArray = panelsMapToSortedArray(panels);\n    const nextSizes = baseSizes.concat();\n    let deltaApplied = 0;\n    // A resizing panel affects the panels before or after it.\n    //\n    // A negative delta means the panel immediately after the resizer should grow/expand by decreasing its offset.\n    // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n    //\n    // A positive delta means the panel immediately before the resizer should \"expand\".\n    // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resizer.\n    // Max-bounds check the panel being expanded first.\n    {\n        const pivotId = deltaPixels < 0 ? idAfter : idBefore;\n        const index = panelsArray.findIndex((panel)=>panel.current.id === pivotId);\n        const panel = panelsArray[index];\n        const baseSize = baseSizes[index];\n        const nextSize = safeResizePanel(units, groupSizePixels, panel, baseSize, baseSize + Math.abs(deltaPixels), event);\n        if (baseSize === nextSize) {\n            // If there's no room for the pivot panel to grow, we can ignore this drag update.\n            return baseSizes;\n        } else {\n            if (nextSize === 0 && baseSize > 0) {\n                panelSizeBeforeCollapse.set(pivotId, baseSize);\n            }\n            deltaPixels = deltaPixels < 0 ? baseSize - nextSize : nextSize - baseSize;\n        }\n    }\n    let pivotId = deltaPixels < 0 ? idBefore : idAfter;\n    let index = panelsArray.findIndex((panel)=>panel.current.id === pivotId);\n    while(true){\n        const panel = panelsArray[index];\n        const baseSize = baseSizes[index];\n        const deltaRemaining = Math.abs(deltaPixels) - Math.abs(deltaApplied);\n        const nextSize = safeResizePanel(units, groupSizePixels, panel, baseSize, baseSize - deltaRemaining, event);\n        if (baseSize !== nextSize) {\n            if (nextSize === 0 && baseSize > 0) {\n                panelSizeBeforeCollapse.set(panel.current.id, baseSize);\n            }\n            deltaApplied += baseSize - nextSize;\n            nextSizes[index] = nextSize;\n            if (deltaApplied.toPrecision(PRECISION).localeCompare(Math.abs(deltaPixels).toPrecision(PRECISION), undefined, {\n                numeric: true\n            }) >= 0) {\n                break;\n            }\n        }\n        if (deltaPixels < 0) {\n            if (--index < 0) {\n                break;\n            }\n        } else {\n            if (++index >= panelsArray.length) {\n                break;\n            }\n        }\n    }\n    // If we were unable to resize any of the panels panels, return the previous state.\n    // This will essentially bailout and ignore the \"mousemove\" event.\n    if (deltaApplied === 0) {\n        return baseSizes;\n    }\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    pivotId = deltaPixels < 0 ? idAfter : idBefore;\n    index = panelsArray.findIndex((panel)=>panel.current.id === pivotId);\n    nextSizes[index] = baseSizes[index] + deltaApplied;\n    return nextSizes;\n}\nfunction callPanelCallbacks(panelsArray, sizes, panelIdToLastNotifiedSizeMap) {\n    sizes.forEach((size, index)=>{\n        const panelRef = panelsArray[index];\n        if (!panelRef) {\n            // Handle initial mount (when panels are registered too late to be in the panels array)\n            // The subsequent render+effects will handle the resize notification\n            return;\n        }\n        const { callbacksRef, collapsedSize, collapsible, id } = panelRef.current;\n        const lastNotifiedSize = panelIdToLastNotifiedSizeMap[id];\n        if (lastNotifiedSize !== size) {\n            panelIdToLastNotifiedSizeMap[id] = size;\n            const { onCollapse, onResize } = callbacksRef.current;\n            if (onResize) {\n                onResize(size, lastNotifiedSize);\n            }\n            if (collapsible && onCollapse) {\n                if ((lastNotifiedSize == null || lastNotifiedSize === collapsedSize) && size !== collapsedSize) {\n                    onCollapse(false);\n                } else if (lastNotifiedSize !== collapsedSize && size === collapsedSize) {\n                    onCollapse(true);\n                }\n            }\n        }\n    });\n}\nfunction getBeforeAndAfterIds(id, panelsArray) {\n    if (panelsArray.length < 2) {\n        return [\n            null,\n            null\n        ];\n    }\n    const index = panelsArray.findIndex((panel)=>panel.current.id === id);\n    if (index < 0) {\n        return [\n            null,\n            null\n        ];\n    }\n    const isLastPanel = index === panelsArray.length - 1;\n    const idBefore = isLastPanel ? panelsArray[index - 1].current.id : id;\n    const idAfter = isLastPanel ? id : panelsArray[index + 1].current.id;\n    return [\n        idBefore,\n        idAfter\n    ];\n}\nfunction getAvailableGroupSizePixels(groupId) {\n    const panelGroupElement = getPanelGroup(groupId);\n    if (panelGroupElement == null) {\n        return NaN;\n    }\n    const direction = panelGroupElement.getAttribute(\"data-panel-group-direction\");\n    const resizeHandles = getResizeHandlesForGroup(groupId);\n    if (direction === \"horizontal\") {\n        return panelGroupElement.offsetWidth - resizeHandles.reduce((accumulated, handle)=>{\n            return accumulated + handle.offsetWidth;\n        }, 0);\n    } else {\n        return panelGroupElement.offsetHeight - resizeHandles.reduce((accumulated, handle)=>{\n            return accumulated + handle.offsetHeight;\n        }, 0);\n    }\n}\n// This method returns a number between 1 and 100 representing\n// the % of the group's overall space this panel should occupy.\nfunction getFlexGrow(panels, id, sizes) {\n    if (panels.size === 1) {\n        return \"100\";\n    }\n    const panelsArray = panelsMapToSortedArray(panels);\n    const index = panelsArray.findIndex((panel)=>panel.current.id === id);\n    const size = sizes[index];\n    if (size == null) {\n        return \"0\";\n    }\n    return size.toPrecision(PRECISION);\n}\nfunction getPanel(id) {\n    const element = document.querySelector(`[data-panel-id=\"${id}\"]`);\n    if (element) {\n        return element;\n    }\n    return null;\n}\nfunction getPanelGroup(id) {\n    const element = document.querySelector(`[data-panel-group-id=\"${id}\"]`);\n    if (element) {\n        return element;\n    }\n    return null;\n}\nfunction getResizeHandle(id) {\n    const element = document.querySelector(`[data-panel-resize-handle-id=\"${id}\"]`);\n    if (element) {\n        return element;\n    }\n    return null;\n}\nfunction getResizeHandleIndex(id) {\n    const handles = getResizeHandles();\n    const index = handles.findIndex((handle)=>handle.getAttribute(\"data-panel-resize-handle-id\") === id);\n    return index ?? null;\n}\nfunction getResizeHandles() {\n    return Array.from(document.querySelectorAll(`[data-panel-resize-handle-id]`));\n}\nfunction getResizeHandlesForGroup(groupId) {\n    return Array.from(document.querySelectorAll(`[data-panel-resize-handle-id][data-panel-group-id=\"${groupId}\"]`));\n}\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray) {\n    const handle = getResizeHandle(handleId);\n    const handles = getResizeHandlesForGroup(groupId);\n    const index = handle ? handles.indexOf(handle) : -1;\n    const idBefore = panelsArray[index]?.current?.id ?? null;\n    const idAfter = panelsArray[index + 1]?.current?.id ?? null;\n    return [\n        idBefore,\n        idAfter\n    ];\n}\nfunction panelsMapToSortedArray(panels) {\n    return Array.from(panels.values()).sort((panelA, panelB)=>{\n        const orderA = panelA.current.order;\n        const orderB = panelB.current.order;\n        if (orderA == null && orderB == null) {\n            return 0;\n        } else if (orderA == null) {\n            return -1;\n        } else if (orderB == null) {\n            return 1;\n        } else {\n            return orderA - orderB;\n        }\n    });\n}\nfunction safeResizePanel(units, groupSizePixels, panel, prevSize, nextSize, event = null) {\n    let { collapsedSize, collapsible, maxSize, minSize } = panel.current;\n    if (units === \"pixels\") {\n        collapsedSize = collapsedSize / groupSizePixels * 100;\n        if (maxSize != null) {\n            maxSize = maxSize / groupSizePixels * 100;\n        }\n        minSize = minSize / groupSizePixels * 100;\n    }\n    if (collapsible) {\n        if (prevSize > collapsedSize) {\n            // Mimic VS COde behavior; collapse a panel if it's smaller than half of its min-size\n            if (nextSize <= minSize / 2 + collapsedSize) {\n                return collapsedSize;\n            }\n        } else {\n            const isKeyboardEvent = event?.type?.startsWith(\"key\");\n            if (!isKeyboardEvent) {\n                // Keyboard events should expand a collapsed panel to the min size,\n                // but mouse events should wait until the panel has reached its min size\n                // to avoid a visual flickering when dragging between collapsed and min size.\n                if (nextSize < minSize) {\n                    return collapsedSize;\n                }\n            }\n        }\n    }\n    return Math.min(maxSize != null ? maxSize : 100, Math.max(minSize, nextSize));\n}\nfunction validatePanelProps(units, panelData) {\n    const { collapsible, defaultSize, maxSize, minSize } = panelData.current;\n    // Basic props validation\n    if (minSize < 0 || units === \"percentages\" && minSize > 100) {\n        {\n            console.error(`Invalid Panel minSize provided, ${minSize}`);\n        }\n        panelData.current.minSize = 0;\n    }\n    if (maxSize != null) {\n        if (maxSize < 0 || units === \"percentages\" && maxSize > 100) {\n            {\n                console.error(`Invalid Panel maxSize provided, ${maxSize}`);\n            }\n            panelData.current.maxSize = null;\n        }\n    }\n    if (defaultSize !== null) {\n        if (defaultSize < 0 || units === \"percentages\" && defaultSize > 100) {\n            {\n                console.error(`Invalid Panel defaultSize provided, ${defaultSize}`);\n            }\n            panelData.current.defaultSize = null;\n        } else if (defaultSize < minSize && !collapsible) {\n            {\n                console.error(`Panel minSize (${minSize}) cannot be greater than defaultSize (${defaultSize})`);\n            }\n            panelData.current.defaultSize = minSize;\n        } else if (maxSize != null && defaultSize > maxSize) {\n            {\n                console.error(`Panel maxSize (${maxSize}) cannot be less than defaultSize (${defaultSize})`);\n            }\n            panelData.current.defaultSize = maxSize;\n        }\n    }\n}\nfunction validatePanelGroupLayout({ groupId, panels, nextSizes, prevSizes, units }) {\n    // Clone because this method modifies\n    nextSizes = [\n        ...nextSizes\n    ];\n    const panelsArray = panelsMapToSortedArray(panels);\n    const groupSizePixels = units === \"pixels\" ? getAvailableGroupSizePixels(groupId) : NaN;\n    let remainingSize = 0;\n    // First, check all of the proposed sizes against the min/max constraints\n    for(let index = 0; index < panelsArray.length; index++){\n        const panel = panelsArray[index];\n        const prevSize = prevSizes[index];\n        const nextSize = nextSizes[index];\n        const safeNextSize = safeResizePanel(units, groupSizePixels, panel, prevSize, nextSize);\n        if (nextSize != safeNextSize) {\n            remainingSize += nextSize - safeNextSize;\n            nextSizes[index] = safeNextSize;\n            {\n                console.error(`Invalid size (${nextSize}) specified for Panel \"${panel.current.id}\" given the panel's min/max size constraints`);\n            }\n        }\n    }\n    // If there is additional, left over space, assign it to any panel(s) that permits it\n    // (It's not worth taking multiple additional passes to evenly distribute)\n    if (remainingSize.toFixed(3) !== \"0.000\") {\n        for(let index = 0; index < panelsArray.length; index++){\n            const panel = panelsArray[index];\n            let { maxSize, minSize } = panel.current;\n            if (units === \"pixels\") {\n                minSize = minSize / groupSizePixels * 100;\n                if (maxSize != null) {\n                    maxSize = maxSize / groupSizePixels * 100;\n                }\n            }\n            const size = Math.min(maxSize != null ? maxSize : 100, Math.max(minSize, nextSizes[index] + remainingSize));\n            if (size !== nextSizes[index]) {\n                remainingSize -= size - nextSizes[index];\n                nextSizes[index] = size;\n                // Fuzzy comparison to account for imprecise floating point math\n                if (Math.abs(remainingSize).toFixed(3) === \"0.000\") {\n                    break;\n                }\n            }\n        }\n    }\n    // If we still have remainder, the requested layout wasn't valid and we should warn about it\n    if (remainingSize.toFixed(3) !== \"0.000\") {\n        {\n            console.error(`\"Invalid panel group configuration; default panel sizes should total 100% but was ${100 - remainingSize}%`);\n        }\n    }\n    return nextSizes;\n}\nfunction assert(expectedCondition, message = \"Assertion failed!\") {\n    if (!expectedCondition) {\n        console.error(message);\n        throw Error(message);\n    }\n}\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\nfunction useWindowSplitterPanelGroupBehavior({ committedValuesRef, groupId, panels, setSizes, sizes, panelSizeBeforeCollapse }) {\n    useEffect(()=>{\n        const { direction, panels } = committedValuesRef.current;\n        const groupElement = getPanelGroup(groupId);\n        assert(groupElement != null, `No group found for id \"${groupId}\"`);\n        const { height, width } = groupElement.getBoundingClientRect();\n        const handles = getResizeHandlesForGroup(groupId);\n        const cleanupFunctions = handles.map((handle)=>{\n            const handleId = handle.getAttribute(\"data-panel-resize-handle-id\");\n            const panelsArray = panelsMapToSortedArray(panels);\n            const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelsArray);\n            if (idBefore == null || idAfter == null) {\n                return ()=>{};\n            }\n            let currentMinSize = 0;\n            let currentMaxSize = 100;\n            let totalMinSize = 0;\n            let totalMaxSize = 0;\n            // A panel's effective min/max sizes also need to account for other panel's sizes.\n            panelsArray.forEach((panelData)=>{\n                const { id, maxSize, minSize } = panelData.current;\n                if (id === idBefore) {\n                    currentMinSize = minSize;\n                    currentMaxSize = maxSize != null ? maxSize : 100;\n                } else {\n                    totalMinSize += minSize;\n                    totalMaxSize += maxSize != null ? maxSize : 100;\n                }\n            });\n            const ariaValueMax = Math.min(currentMaxSize, 100 - totalMinSize);\n            const ariaValueMin = Math.max(currentMinSize, (panelsArray.length - 1) * 100 - totalMaxSize);\n            const flexGrow = getFlexGrow(panels, idBefore, sizes);\n            handle.setAttribute(\"aria-valuemax\", \"\" + Math.round(ariaValueMax));\n            handle.setAttribute(\"aria-valuemin\", \"\" + Math.round(ariaValueMin));\n            handle.setAttribute(\"aria-valuenow\", \"\" + Math.round(parseInt(flexGrow)));\n            const onKeyDown = (event)=>{\n                if (event.defaultPrevented) {\n                    return;\n                }\n                switch(event.key){\n                    case \"Enter\":\n                        {\n                            event.preventDefault();\n                            const index = panelsArray.findIndex((panel)=>panel.current.id === idBefore);\n                            if (index >= 0) {\n                                const panelData = panelsArray[index];\n                                const size = sizes[index];\n                                if (size != null) {\n                                    let delta = 0;\n                                    if (size.toPrecision(PRECISION) <= panelData.current.minSize.toPrecision(PRECISION)) {\n                                        delta = direction === \"horizontal\" ? width : height;\n                                    } else {\n                                        delta = -(direction === \"horizontal\" ? width : height);\n                                    }\n                                    const nextSizes = adjustByDelta(event, committedValuesRef.current, idBefore, idAfter, delta, sizes, panelSizeBeforeCollapse.current, null);\n                                    if (sizes !== nextSizes) {\n                                        setSizes(nextSizes);\n                                    }\n                                }\n                            }\n                            break;\n                        }\n                }\n            };\n            handle.addEventListener(\"keydown\", onKeyDown);\n            const panelBefore = getPanel(idBefore);\n            if (panelBefore != null) {\n                handle.setAttribute(\"aria-controls\", panelBefore.id);\n            }\n            return ()=>{\n                handle.removeAttribute(\"aria-valuemax\");\n                handle.removeAttribute(\"aria-valuemin\");\n                handle.removeAttribute(\"aria-valuenow\");\n                handle.removeEventListener(\"keydown\", onKeyDown);\n                if (panelBefore != null) {\n                    handle.removeAttribute(\"aria-controls\");\n                }\n            };\n        });\n        return ()=>{\n            cleanupFunctions.forEach((cleanupFunction)=>cleanupFunction());\n        };\n    }, [\n        committedValuesRef,\n        groupId,\n        panels,\n        panelSizeBeforeCollapse,\n        setSizes,\n        sizes\n    ]);\n}\nfunction useWindowSplitterResizeHandlerBehavior({ disabled, handleId, resizeHandler }) {\n    useEffect(()=>{\n        if (disabled || resizeHandler == null) {\n            return;\n        }\n        const handleElement = getResizeHandle(handleId);\n        if (handleElement == null) {\n            return;\n        }\n        const onKeyDown = (event)=>{\n            if (event.defaultPrevented) {\n                return;\n            }\n            switch(event.key){\n                case \"ArrowDown\":\n                case \"ArrowLeft\":\n                case \"ArrowRight\":\n                case \"ArrowUp\":\n                case \"End\":\n                case \"Home\":\n                    {\n                        event.preventDefault();\n                        resizeHandler(event);\n                        break;\n                    }\n                case \"F6\":\n                    {\n                        event.preventDefault();\n                        const handles = getResizeHandles();\n                        const index = getResizeHandleIndex(handleId);\n                        assert(index !== null);\n                        const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n                        const nextHandle = handles[nextIndex];\n                        nextHandle.focus();\n                        break;\n                    }\n            }\n        };\n        handleElement.addEventListener(\"keydown\", onKeyDown);\n        return ()=>{\n            handleElement.removeEventListener(\"keydown\", onKeyDown);\n        };\n    }, [\n        disabled,\n        handleId,\n        resizeHandler\n    ]);\n}\nfunction areEqual(arrayA, arrayB) {\n    if (arrayA.length !== arrayB.length) {\n        return false;\n    }\n    for(let index = 0; index < arrayA.length; index++){\n        if (arrayA[index] !== arrayB[index]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction getDragOffset(event, handleId, direction, initialOffset = 0, initialHandleElementRect = null) {\n    const isHorizontal = direction === \"horizontal\";\n    let pointerOffset = 0;\n    if (isMouseEvent(event)) {\n        pointerOffset = isHorizontal ? event.clientX : event.clientY;\n    } else if (isTouchEvent(event)) {\n        const firstTouch = event.touches[0];\n        pointerOffset = isHorizontal ? firstTouch.screenX : firstTouch.screenY;\n    } else {\n        return 0;\n    }\n    const handleElement = getResizeHandle(handleId);\n    const rect = initialHandleElementRect || handleElement.getBoundingClientRect();\n    const elementOffset = isHorizontal ? rect.left : rect.top;\n    return pointerOffset - elementOffset - initialOffset;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction getMovement(event, groupId, handleId, panelsArray, direction, prevSizes, initialDragState) {\n    const { dragOffset = 0, dragHandleRect, sizes: initialSizes } = initialDragState || {};\n    // If we're resizing by mouse or touch, use the initial sizes as a base.\n    // This has the benefit of causing force-collapsed panels to spring back open if drag is reversed.\n    const baseSizes = initialSizes || prevSizes;\n    if (isKeyDown(event)) {\n        const isHorizontal = direction === \"horizontal\";\n        const groupElement = getPanelGroup(groupId);\n        const rect = groupElement.getBoundingClientRect();\n        const groupSizeInPixels = isHorizontal ? rect.width : rect.height;\n        const denominator = event.shiftKey ? 10 : 100;\n        const delta = groupSizeInPixels / denominator;\n        let movement = 0;\n        switch(event.key){\n            case \"ArrowDown\":\n                movement = isHorizontal ? 0 : delta;\n                break;\n            case \"ArrowLeft\":\n                movement = isHorizontal ? -delta : 0;\n                break;\n            case \"ArrowRight\":\n                movement = isHorizontal ? delta : 0;\n                break;\n            case \"ArrowUp\":\n                movement = isHorizontal ? 0 : -delta;\n                break;\n            case \"End\":\n                movement = groupSizeInPixels;\n                break;\n            case \"Home\":\n                movement = -groupSizeInPixels;\n                break;\n        }\n        // If the Panel being resized is collapsible,\n        // we need to special case resizing around the minSize boundary.\n        // If contracting, Panels should shrink to their minSize and then snap to fully collapsed.\n        // If expanding from collapsed, they should snap back to their minSize.\n        const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelsArray);\n        const targetPanelId = movement < 0 ? idBefore : idAfter;\n        const targetPanelIndex = panelsArray.findIndex((panel)=>panel.current.id === targetPanelId);\n        const targetPanel = panelsArray[targetPanelIndex];\n        if (targetPanel.current.collapsible) {\n            const baseSize = baseSizes[targetPanelIndex];\n            if (baseSize === 0 || baseSize.toPrecision(PRECISION) === targetPanel.current.minSize.toPrecision(PRECISION)) {\n                movement = movement < 0 ? -targetPanel.current.minSize * groupSizeInPixels : targetPanel.current.minSize * groupSizeInPixels;\n            }\n        }\n        return movement;\n    } else {\n        return getDragOffset(event, handleId, direction, dragOffset, dragHandleRect);\n    }\n}\nfunction isKeyDown(event) {\n    return event.type === \"keydown\";\n}\nfunction isMouseEvent(event) {\n    return event.type.startsWith(\"mouse\");\n}\nfunction isTouchEvent(event) {\n    return event.type.startsWith(\"touch\");\n}\nlet currentState = null;\nlet element = null;\nfunction getCursorStyle(state) {\n    switch(state){\n        case \"horizontal\":\n            return \"ew-resize\";\n        case \"horizontal-max\":\n            return \"w-resize\";\n        case \"horizontal-min\":\n            return \"e-resize\";\n        case \"vertical\":\n            return \"ns-resize\";\n        case \"vertical-max\":\n            return \"n-resize\";\n        case \"vertical-min\":\n            return \"s-resize\";\n    }\n}\nfunction resetGlobalCursorStyle() {\n    if (element !== null) {\n        document.head.removeChild(element);\n        currentState = null;\n        element = null;\n    }\n}\nfunction setGlobalCursorStyle(state) {\n    if (currentState === state) {\n        return;\n    }\n    currentState = state;\n    const style = getCursorStyle(state);\n    if (element === null) {\n        element = document.createElement(\"style\");\n        document.head.appendChild(element);\n    }\n    element.innerHTML = `*{cursor: ${style}!important;}`;\n}\nfunction debounce(callback, durationMs = 10) {\n    let timeoutId = null;\n    let callable = (...args)=>{\n        if (timeoutId !== null) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(()=>{\n            callback(...args);\n        }, durationMs);\n    };\n    return callable;\n}\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using an attribute like minSize instead should work well enough.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getSerializationKey(panels) {\n    return panels.map((panel)=>{\n        const { minSize, order } = panel.current;\n        return order ? `${order}:${minSize}` : `${minSize}`;\n    }).sort((a, b)=>a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n    try {\n        const serialized = storage.getItem(`PanelGroup:sizes:${autoSaveId}`);\n        if (serialized) {\n            const parsed = JSON.parse(serialized);\n            if (typeof parsed === \"object\" && parsed != null) {\n                return parsed;\n            }\n        }\n    } catch (error) {}\n    return null;\n}\nfunction savePanelGroupLayout(autoSaveId, panels, sizes, storage) {\n    const key = getSerializationKey(panels);\n    const state = loadSerializedPanelGroupState(autoSaveId, storage) || {};\n    state[key] = sizes;\n    try {\n        storage.setItem(`PanelGroup:sizes:${autoSaveId}`, JSON.stringify(state));\n    } catch (error) {\n        console.error(error);\n    }\n}\nconst debounceMap = {};\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n    try {\n        if (typeof localStorage !== \"undefined\") {\n            // Bypass this check for future calls\n            storageObject.getItem = (name)=>{\n                return localStorage.getItem(name);\n            };\n            storageObject.setItem = (name, value)=>{\n                localStorage.setItem(name, value);\n            };\n        } else {\n            throw new Error(\"localStorage not supported in this environment\");\n        }\n    } catch (error) {\n        console.error(error);\n        storageObject.getItem = ()=>null;\n        storageObject.setItem = ()=>{};\n    }\n}\nconst defaultStorage = {\n    getItem: (name)=>{\n        initializeDefaultStorage(defaultStorage);\n        return defaultStorage.getItem(name);\n    },\n    setItem: (name, value)=>{\n        initializeDefaultStorage(defaultStorage);\n        defaultStorage.setItem(name, value);\n    }\n};\n// Initial drag state serves a few purposes:\n// * dragOffset:\n//   Resize is calculated by the distance between the current pointer event and the resize handle being \"dragged\"\n//   This value accounts for the initial offset when the touch/click starts, so the handle doesn't appear to \"jump\"\n// * dragHandleRect, sizes:\n//   When resizing is done via mouse/touch event– some initial state is stored\n//   so that any panels that contract will also expand if drag direction is reversed.\nfunction PanelGroupWithForwardedRef({ autoSaveId, children = null, className: classNameFromProps = \"\", direction, disablePointerEventsDuringResize = false, forwardedRef, id: idFromProps = null, onLayout, storage = defaultStorage, style: styleFromProps = {}, tagName: Type = \"div\", units = \"percentages\" }) {\n    const groupId = useUniqueId(idFromProps);\n    const [activeHandleId, setActiveHandleId] = useState(null);\n    const [panels, setPanels] = useState(new Map());\n    // When resizing is done via mouse/touch event–\n    // We store the initial Panel sizes in this ref, and apply move deltas to them instead of to the current sizes.\n    // This has the benefit of causing force-collapsed panels to spring back open if drag is reversed.\n    const initialDragStateRef = useRef(null);\n    const devWarningsRef = useRef({\n        didLogDefaultSizeWarning: false,\n        didLogIdAndOrderWarning: false,\n        didLogInvalidLayoutWarning: false,\n        prevPanelIds: []\n    });\n    // Use a ref to guard against users passing inline props\n    const callbacksRef = useRef({\n        onLayout\n    });\n    useEffect(()=>{\n        callbacksRef.current.onLayout = onLayout;\n    });\n    const panelIdToLastNotifiedSizeMapRef = useRef({});\n    // 0-1 values representing the relative size of each panel.\n    const [sizes, setSizes] = useState([]);\n    // Used to support imperative collapse/expand API.\n    const panelSizeBeforeCollapse = useRef(new Map());\n    const prevDeltaRef = useRef(0);\n    // Store committed values to avoid unnecessarily re-running memoization/effects functions.\n    const committedValuesRef = useRef({\n        direction,\n        id: groupId,\n        panels,\n        sizes,\n        units\n    });\n    useImperativeHandle(forwardedRef, ()=>({\n            getId: ()=>groupId,\n            getLayout: (unitsFromParams)=>{\n                const { sizes, units: unitsFromProps } = committedValuesRef.current;\n                const units = unitsFromParams ?? unitsFromProps;\n                if (units === \"pixels\") {\n                    const groupSizePixels = getAvailableGroupSizePixels(groupId);\n                    return sizes.map((size)=>size / 100 * groupSizePixels);\n                } else {\n                    return sizes;\n                }\n            },\n            setLayout: (sizes, unitsFromParams)=>{\n                const { id: groupId, panels, sizes: prevSizes, units } = committedValuesRef.current;\n                if ((unitsFromParams || units) === \"pixels\") {\n                    const groupSizePixels = getAvailableGroupSizePixels(groupId);\n                    sizes = sizes.map((size)=>size / groupSizePixels * 100);\n                }\n                const panelIdToLastNotifiedSizeMap = panelIdToLastNotifiedSizeMapRef.current;\n                const panelsArray = panelsMapToSortedArray(panels);\n                const nextSizes = validatePanelGroupLayout({\n                    groupId,\n                    panels,\n                    nextSizes: sizes,\n                    prevSizes,\n                    units\n                });\n                if (!areEqual(prevSizes, nextSizes)) {\n                    setSizes(nextSizes);\n                    callPanelCallbacks(panelsArray, nextSizes, panelIdToLastNotifiedSizeMap);\n                }\n            }\n        }), [\n        groupId\n    ]);\n    useWindowSplitterPanelGroupBehavior({\n        committedValuesRef,\n        groupId,\n        panels,\n        setSizes,\n        sizes,\n        panelSizeBeforeCollapse\n    });\n    // Notify external code when sizes have changed.\n    useEffect(()=>{\n        const { onLayout } = callbacksRef.current;\n        const { panels, sizes } = committedValuesRef.current;\n        // Don't commit layout until all panels have registered and re-rendered with their actual sizes.\n        if (sizes.length > 0) {\n            if (onLayout) {\n                onLayout(sizes);\n            }\n            const panelIdToLastNotifiedSizeMap = panelIdToLastNotifiedSizeMapRef.current;\n            // When possible, we notify before the next render so that rendering work can be batched together.\n            // Some cases are difficult to detect though,\n            // for example– panels that are conditionally rendered can affect the size of neighboring panels.\n            // In this case, the best we can do is notify on commit.\n            // The callPanelCallbacks() uses its own memoization to avoid notifying panels twice in these cases.\n            const panelsArray = panelsMapToSortedArray(panels);\n            callPanelCallbacks(panelsArray, sizes, panelIdToLastNotifiedSizeMap);\n        }\n    }, [\n        sizes\n    ]);\n    useEffect(()=>{\n        // If this panel has been configured to persist sizing information, save sizes to local storage.\n        if (autoSaveId) {\n            if (sizes.length === 0 || sizes.length !== panels.size) {\n                return;\n            }\n            const panelsArray = panelsMapToSortedArray(panels);\n            // Limit the frequency of localStorage updates.\n            if (!debounceMap[autoSaveId]) {\n                debounceMap[autoSaveId] = debounce(savePanelGroupLayout, 100);\n            }\n            debounceMap[autoSaveId](autoSaveId, panelsArray, sizes, storage);\n        }\n        {\n            const { didLogIdAndOrderWarning, prevPanelIds } = devWarningsRef.current;\n            if (!didLogIdAndOrderWarning) {\n                const { panels } = committedValuesRef.current;\n                const panelIds = Array.from(panels.keys());\n                devWarningsRef.current.prevPanelIds = panelIds;\n                const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n                if (panelsHaveChanged) {\n                    if (Array.from(panels.values()).find((panel)=>panel.current.idWasAutoGenerated || panel.current.order == null)) {\n                        devWarningsRef.current.didLogIdAndOrderWarning = true;\n                        console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n                    }\n                }\n            }\n        }\n    }, [\n        autoSaveId,\n        panels,\n        sizes,\n        storage\n    ]);\n    const getPanelSize = useCallback((id, unitsFromParams)=>{\n        const { panels, units: unitsFromProps } = committedValuesRef.current;\n        const panelsArray = panelsMapToSortedArray(panels);\n        const index = panelsArray.findIndex((panel)=>panel.current.id === id);\n        const size = sizes[index];\n        const units = unitsFromParams ?? unitsFromProps;\n        if (units === \"pixels\") {\n            const groupSizePixels = getAvailableGroupSizePixels(groupId);\n            return size / 100 * groupSizePixels;\n        } else {\n            return size;\n        }\n    }, [\n        groupId,\n        sizes\n    ]);\n    const getPanelStyle = useCallback((id, defaultSize)=>{\n        const { panels } = committedValuesRef.current;\n        // Before mounting, Panels will not yet have registered themselves.\n        // This includes server rendering.\n        // At this point the best we can do is render everything with the same size.\n        if (panels.size === 0) {\n            {\n                if (!devWarningsRef.current.didLogDefaultSizeWarning) {\n                    if (defaultSize == null) {\n                        devWarningsRef.current.didLogDefaultSizeWarning = true;\n                        console.warn(`WARNING: Panel defaultSize prop recommended to avoid layout shift after server rendering`);\n                    }\n                }\n            }\n            return {\n                flexBasis: 0,\n                flexGrow: defaultSize != null ? defaultSize : undefined,\n                flexShrink: 1,\n                // Without this, Panel sizes may be unintentionally overridden by their content.\n                overflow: \"hidden\"\n            };\n        }\n        const flexGrow = getFlexGrow(panels, id, sizes);\n        return {\n            flexBasis: 0,\n            flexGrow,\n            flexShrink: 1,\n            // Without this, Panel sizes may be unintentionally overridden by their content.\n            overflow: \"hidden\",\n            // Disable pointer events inside of a panel during resize.\n            // This avoid edge cases like nested iframes.\n            pointerEvents: disablePointerEventsDuringResize && activeHandleId !== null ? \"none\" : undefined\n        };\n    }, [\n        activeHandleId,\n        disablePointerEventsDuringResize,\n        sizes\n    ]);\n    const registerPanel = useCallback((id, panelRef)=>{\n        const { units } = committedValuesRef.current;\n        validatePanelProps(units, panelRef);\n        setPanels((prevPanels)=>{\n            if (prevPanels.has(id)) {\n                return prevPanels;\n            }\n            const nextPanels = new Map(prevPanels);\n            nextPanels.set(id, panelRef);\n            return nextPanels;\n        });\n    }, []);\n    const registerResizeHandle = useCallback((handleId)=>{\n        const resizeHandler = (event)=>{\n            event.preventDefault();\n            const { direction, panels, sizes: prevSizes } = committedValuesRef.current;\n            const panelsArray = panelsMapToSortedArray(panels);\n            const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelsArray);\n            if (idBefore == null || idAfter == null) {\n                return;\n            }\n            let movement = getMovement(event, groupId, handleId, panelsArray, direction, prevSizes, initialDragStateRef.current);\n            if (movement === 0) {\n                return;\n            }\n            const groupElement = getPanelGroup(groupId);\n            const rect = groupElement.getBoundingClientRect();\n            const isHorizontal = direction === \"horizontal\";\n            // Support RTL layouts\n            if (document.dir === \"rtl\" && isHorizontal) {\n                movement = -movement;\n            }\n            const size = isHorizontal ? rect.width : rect.height;\n            const delta = movement / size * 100;\n            // If a validateLayout method has been provided\n            // it's important to use it before updating the mouse cursor\n            const nextSizes = adjustByDelta(event, committedValuesRef.current, idBefore, idAfter, delta, prevSizes, panelSizeBeforeCollapse.current, initialDragStateRef.current);\n            const sizesChanged = !areEqual(prevSizes, nextSizes);\n            // Don't update cursor for resizes triggered by keyboard interactions.\n            if (isMouseEvent(event) || isTouchEvent(event)) {\n                // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n                // In this case, Panel sizes might not change–\n                // but updating cursor in this scenario would cause a flicker.\n                if (prevDeltaRef.current != delta) {\n                    if (!sizesChanged) {\n                        // If the pointer has moved too far to resize the panel any further,\n                        // update the cursor style for a visual clue.\n                        // This mimics VS Code behavior.\n                        if (isHorizontal) {\n                            setGlobalCursorStyle(movement < 0 ? \"horizontal-min\" : \"horizontal-max\");\n                        } else {\n                            setGlobalCursorStyle(movement < 0 ? \"vertical-min\" : \"vertical-max\");\n                        }\n                    } else {\n                        // Reset the cursor style to the the normal resize cursor.\n                        setGlobalCursorStyle(isHorizontal ? \"horizontal\" : \"vertical\");\n                    }\n                }\n            }\n            if (sizesChanged) {\n                const panelIdToLastNotifiedSizeMap = panelIdToLastNotifiedSizeMapRef.current;\n                // It's okay to bypass in this case because we already validated above\n                setSizes(nextSizes);\n                // If resize change handlers have been declared, this is the time to call them.\n                // Trigger user callbacks after updating state, so that user code can override the sizes.\n                callPanelCallbacks(panelsArray, nextSizes, panelIdToLastNotifiedSizeMap);\n            }\n            prevDeltaRef.current = delta;\n        };\n        return resizeHandler;\n    }, [\n        groupId\n    ]);\n    const unregisterPanel = useCallback((id)=>{\n        setPanels((prevPanels)=>{\n            if (!prevPanels.has(id)) {\n                return prevPanels;\n            }\n            const nextPanels = new Map(prevPanels);\n            nextPanels.delete(id);\n            return nextPanels;\n        });\n    }, []);\n    const collapsePanel = useCallback((id)=>{\n        const { panels, sizes: prevSizes } = committedValuesRef.current;\n        const panel = panels.get(id);\n        if (panel == null) {\n            return;\n        }\n        const { collapsedSize, collapsible } = panel.current;\n        if (!collapsible) {\n            return;\n        }\n        const panelsArray = panelsMapToSortedArray(panels);\n        const index = panelsArray.indexOf(panel);\n        if (index < 0) {\n            return;\n        }\n        const currentSize = prevSizes[index];\n        if (currentSize === collapsedSize) {\n            // Panel is already collapsed.\n            return;\n        }\n        panelSizeBeforeCollapse.current.set(id, currentSize);\n        const [idBefore, idAfter] = getBeforeAndAfterIds(id, panelsArray);\n        if (idBefore == null || idAfter == null) {\n            return;\n        }\n        const isLastPanel = index === panelsArray.length - 1;\n        const delta = isLastPanel ? currentSize : collapsedSize - currentSize;\n        const nextSizes = adjustByDelta(null, committedValuesRef.current, idBefore, idAfter, delta, prevSizes, panelSizeBeforeCollapse.current, null);\n        if (prevSizes !== nextSizes) {\n            const panelIdToLastNotifiedSizeMap = panelIdToLastNotifiedSizeMapRef.current;\n            setSizes(nextSizes);\n            // If resize change handlers have been declared, this is the time to call them.\n            // Trigger user callbacks after updating state, so that user code can override the sizes.\n            callPanelCallbacks(panelsArray, nextSizes, panelIdToLastNotifiedSizeMap);\n        }\n    }, []);\n    const expandPanel = useCallback((id)=>{\n        const { panels, sizes: prevSizes } = committedValuesRef.current;\n        const panel = panels.get(id);\n        if (panel == null) {\n            return;\n        }\n        const { collapsedSize, minSize } = panel.current;\n        const sizeBeforeCollapse = panelSizeBeforeCollapse.current.get(id) || minSize;\n        if (!sizeBeforeCollapse) {\n            return;\n        }\n        const panelsArray = panelsMapToSortedArray(panels);\n        const index = panelsArray.indexOf(panel);\n        if (index < 0) {\n            return;\n        }\n        const currentSize = prevSizes[index];\n        if (currentSize !== collapsedSize) {\n            // Panel is already expanded.\n            return;\n        }\n        const [idBefore, idAfter] = getBeforeAndAfterIds(id, panelsArray);\n        if (idBefore == null || idAfter == null) {\n            return;\n        }\n        const isLastPanel = index === panelsArray.length - 1;\n        const delta = isLastPanel ? collapsedSize - sizeBeforeCollapse : sizeBeforeCollapse;\n        const nextSizes = adjustByDelta(null, committedValuesRef.current, idBefore, idAfter, delta, prevSizes, panelSizeBeforeCollapse.current, null);\n        if (prevSizes !== nextSizes) {\n            const panelIdToLastNotifiedSizeMap = panelIdToLastNotifiedSizeMapRef.current;\n            setSizes(nextSizes);\n            // If resize change handlers have been declared, this is the time to call them.\n            // Trigger user callbacks after updating state, so that user code can override the sizes.\n            callPanelCallbacks(panelsArray, nextSizes, panelIdToLastNotifiedSizeMap);\n        }\n    }, []);\n    const resizePanel = useCallback((id, nextSize, unitsFromParams)=>{\n        const { id: groupId, panels, sizes: prevSizes, units } = committedValuesRef.current;\n        if ((unitsFromParams || units) === \"pixels\") {\n            const groupSizePixels = getAvailableGroupSizePixels(groupId);\n            nextSize = nextSize / groupSizePixels * 100;\n        }\n        const panel = panels.get(id);\n        if (panel == null) {\n            return;\n        }\n        let { collapsedSize, collapsible, maxSize, minSize } = panel.current;\n        if (units === \"pixels\") {\n            const groupSizePixels = getAvailableGroupSizePixels(groupId);\n            minSize = minSize / groupSizePixels * 100;\n            if (maxSize != null) {\n                maxSize = maxSize / groupSizePixels * 100;\n            }\n        }\n        const panelsArray = panelsMapToSortedArray(panels);\n        const index = panelsArray.indexOf(panel);\n        if (index < 0) {\n            return;\n        }\n        const currentSize = prevSizes[index];\n        if (currentSize === nextSize) {\n            return;\n        }\n        if (collapsible && nextSize === collapsedSize) ;\n        else {\n            const unsafeNextSize = nextSize;\n            nextSize = Math.min(maxSize != null ? maxSize : 100, Math.max(minSize, nextSize));\n            {\n                if (unsafeNextSize !== nextSize) {\n                    console.error(`Invalid size (${unsafeNextSize}) specified for Panel \"${panel.current.id}\" given the panel's min/max size constraints`);\n                }\n            }\n        }\n        const [idBefore, idAfter] = getBeforeAndAfterIds(id, panelsArray);\n        if (idBefore == null || idAfter == null) {\n            return;\n        }\n        const isLastPanel = index === panelsArray.length - 1;\n        const delta = isLastPanel ? currentSize - nextSize : nextSize - currentSize;\n        const nextSizes = adjustByDelta(null, committedValuesRef.current, idBefore, idAfter, delta, prevSizes, panelSizeBeforeCollapse.current, null);\n        if (prevSizes !== nextSizes) {\n            const panelIdToLastNotifiedSizeMap = panelIdToLastNotifiedSizeMapRef.current;\n            setSizes(nextSizes);\n            // If resize change handlers have been declared, this is the time to call them.\n            // Trigger user callbacks after updating state, so that user code can override the sizes.\n            callPanelCallbacks(panelsArray, nextSizes, panelIdToLastNotifiedSizeMap);\n        }\n    }, []);\n    const context = useMemo(()=>({\n            activeHandleId,\n            collapsePanel,\n            direction,\n            expandPanel,\n            getPanelSize,\n            getPanelStyle,\n            groupId,\n            registerPanel,\n            registerResizeHandle,\n            resizePanel,\n            startDragging: (id, event)=>{\n                setActiveHandleId(id);\n                if (isMouseEvent(event) || isTouchEvent(event)) {\n                    const handleElement = getResizeHandle(id);\n                    initialDragStateRef.current = {\n                        dragHandleRect: handleElement.getBoundingClientRect(),\n                        dragOffset: getDragOffset(event, id, direction),\n                        sizes: committedValuesRef.current.sizes\n                    };\n                }\n            },\n            stopDragging: ()=>{\n                resetGlobalCursorStyle();\n                setActiveHandleId(null);\n                initialDragStateRef.current = null;\n            },\n            units,\n            unregisterPanel\n        }), [\n        activeHandleId,\n        collapsePanel,\n        direction,\n        expandPanel,\n        getPanelSize,\n        getPanelStyle,\n        groupId,\n        registerPanel,\n        registerResizeHandle,\n        resizePanel,\n        units,\n        unregisterPanel\n    ]);\n    const style = {\n        display: \"flex\",\n        flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n        height: \"100%\",\n        overflow: \"hidden\",\n        width: \"100%\"\n    };\n    return createElement(PanelGroupContext.Provider, {\n        children: createElement(Type, {\n            children,\n            className: classNameFromProps,\n            \"data-panel-group\": \"\",\n            \"data-panel-group-direction\": direction,\n            \"data-panel-group-id\": groupId,\n            \"data-panel-group-units\": units,\n            style: {\n                ...style,\n                ...styleFromProps\n            }\n        }),\n        value: context\n    });\n}\nconst PanelGroup = forwardRef((props, ref)=>createElement(PanelGroupWithForwardedRef, {\n        ...props,\n        forwardedRef: ref\n    }));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction PanelResizeHandle({ children = null, className: classNameFromProps = \"\", disabled = false, id: idFromProps = null, onDragging, style: styleFromProps = {}, tagName: Type = \"div\" }) {\n    const divElementRef = useRef(null);\n    // Use a ref to guard against users passing inline props\n    const callbacksRef = useRef({\n        onDragging\n    });\n    useEffect(()=>{\n        callbacksRef.current.onDragging = onDragging;\n    });\n    const panelGroupContext = useContext(PanelGroupContext);\n    if (panelGroupContext === null) {\n        throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n    }\n    const { activeHandleId, direction, groupId, registerResizeHandle, startDragging, stopDragging } = panelGroupContext;\n    const resizeHandleId = useUniqueId(idFromProps);\n    const isDragging = activeHandleId === resizeHandleId;\n    const [isFocused, setIsFocused] = useState(false);\n    const [resizeHandler, setResizeHandler] = useState(null);\n    const stopDraggingAndBlur = useCallback(()=>{\n        // Clicking on the drag handle shouldn't leave it focused;\n        // That would cause the PanelGroup to think it was still active.\n        const div = divElementRef.current;\n        div.blur();\n        stopDragging();\n        const { onDragging } = callbacksRef.current;\n        if (onDragging) {\n            onDragging(false);\n        }\n    }, [\n        stopDragging\n    ]);\n    useEffect(()=>{\n        if (disabled) {\n            setResizeHandler(null);\n        } else {\n            const resizeHandler = registerResizeHandle(resizeHandleId);\n            setResizeHandler(()=>resizeHandler);\n        }\n    }, [\n        disabled,\n        resizeHandleId,\n        registerResizeHandle\n    ]);\n    useEffect(()=>{\n        if (disabled || resizeHandler == null || !isDragging) {\n            return;\n        }\n        const onMove = (event)=>{\n            resizeHandler(event);\n        };\n        const onMouseLeave = (event)=>{\n            resizeHandler(event);\n        };\n        const divElement = divElementRef.current;\n        const targetDocument = divElement.ownerDocument;\n        targetDocument.body.addEventListener(\"contextmenu\", stopDraggingAndBlur);\n        targetDocument.body.addEventListener(\"mousemove\", onMove);\n        targetDocument.body.addEventListener(\"touchmove\", onMove);\n        targetDocument.body.addEventListener(\"mouseleave\", onMouseLeave);\n        window.addEventListener(\"mouseup\", stopDraggingAndBlur);\n        window.addEventListener(\"touchend\", stopDraggingAndBlur);\n        return ()=>{\n            targetDocument.body.removeEventListener(\"contextmenu\", stopDraggingAndBlur);\n            targetDocument.body.removeEventListener(\"mousemove\", onMove);\n            targetDocument.body.removeEventListener(\"touchmove\", onMove);\n            targetDocument.body.removeEventListener(\"mouseleave\", onMouseLeave);\n            window.removeEventListener(\"mouseup\", stopDraggingAndBlur);\n            window.removeEventListener(\"touchend\", stopDraggingAndBlur);\n        };\n    }, [\n        direction,\n        disabled,\n        isDragging,\n        resizeHandler,\n        stopDraggingAndBlur\n    ]);\n    useWindowSplitterResizeHandlerBehavior({\n        disabled,\n        handleId: resizeHandleId,\n        resizeHandler\n    });\n    const style = {\n        cursor: getCursorStyle(direction),\n        touchAction: \"none\",\n        userSelect: \"none\"\n    };\n    return createElement(Type, {\n        children,\n        className: classNameFromProps,\n        \"data-resize-handle-active\": isDragging ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n        \"data-panel-group-direction\": direction,\n        \"data-panel-group-id\": groupId,\n        \"data-panel-resize-handle-enabled\": !disabled,\n        \"data-panel-resize-handle-id\": resizeHandleId,\n        onBlur: ()=>setIsFocused(false),\n        onFocus: ()=>setIsFocused(true),\n        onMouseDown: (event)=>{\n            startDragging(resizeHandleId, event.nativeEvent);\n            const { onDragging } = callbacksRef.current;\n            if (onDragging) {\n                onDragging(true);\n            }\n        },\n        onMouseUp: stopDraggingAndBlur,\n        onTouchCancel: stopDraggingAndBlur,\n        onTouchEnd: stopDraggingAndBlur,\n        onTouchStart: (event)=>{\n            startDragging(resizeHandleId, event.nativeEvent);\n            const { onDragging } = callbacksRef.current;\n            if (onDragging) {\n                onDragging(true);\n            }\n        },\n        ref: divElementRef,\n        role: \"separator\",\n        style: {\n            ...style,\n            ...styleFromProps\n        },\n        tabIndex: 0\n    });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js\n");

/***/ })

};
;