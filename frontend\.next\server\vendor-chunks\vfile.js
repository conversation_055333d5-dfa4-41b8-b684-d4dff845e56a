"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile";
exports.ids = ["vendor-chunks/vfile"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vfile/lib/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFile: () => (/* binding */ VFile)\n/* harmony export */ });\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _minpath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! #minpath */ \"node:path\");\n/* harmony import */ var _minproc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! #minproc */ \"node:process\");\n/* harmony import */ var _minurl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! #minurl */ \"(ssr)/./node_modules/vfile/lib/minurl.shared.js\");\n/* harmony import */ var _minurl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! #minurl */ \"node:url\");\n/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */ /**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */ \n\n\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */ const order = /** @type {const} */ [\n    \"history\",\n    \"path\",\n    \"basename\",\n    \"stem\",\n    \"extname\",\n    \"dirname\"\n];\nclass VFile {\n    /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */ constructor(value){\n        /** @type {Options | VFile} */ let options;\n        if (!value) {\n            options = {};\n        } else if ((0,_minurl__WEBPACK_IMPORTED_MODULE_0__.isUrl)(value)) {\n            options = {\n                path: value\n            };\n        } else if (typeof value === \"string\" || isUint8Array(value)) {\n            options = {\n                value\n            };\n        } else {\n            options = value;\n        }\n        /* eslint-disable no-unused-expressions */ /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */ // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n        // the empty string will be overridden in the next block.\n        this.cwd = \"cwd\" in options ? \"\" : _minproc__WEBPACK_IMPORTED_MODULE_1__.cwd();\n        /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */ this.data = {};\n        /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */ this.history = [];\n        /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */ this.messages = [];\n        /**\n     * Raw value.\n     *\n     * @type {Value}\n     */ this.value;\n        // The below are non-standard, they are “well-known”.\n        // As in, used in several tools.\n        /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */ this.map;\n        /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */ this.result;\n        /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */ this.stored;\n        /* eslint-enable no-unused-expressions */ // Set path related properties in the correct order.\n        let index = -1;\n        while(++index < order.length){\n            const field = order[index];\n            // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n            // `vfile`s too.\n            if (field in options && options[field] !== undefined && options[field] !== null) {\n                // @ts-expect-error: TS doesn’t understand basic reality.\n                this[field] = field === \"history\" ? [\n                    ...options[field]\n                ] : options[field];\n            }\n        }\n        /** @type {string} */ let field;\n        // Set non-path related properties.\n        for(field in options){\n            // @ts-expect-error: fine to set other things.\n            if (!order.includes(field)) {\n                // @ts-expect-error: fine to set other things.\n                this[field] = options[field];\n            }\n        }\n    }\n    /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */ get basename() {\n        return typeof this.path === \"string\" ? _minpath__WEBPACK_IMPORTED_MODULE_2__.basename(this.path) : undefined;\n    }\n    /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */ set basename(basename) {\n        assertNonEmpty(basename, \"basename\");\n        assertPart(basename, \"basename\");\n        this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname || \"\", basename);\n    }\n    /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */ get dirname() {\n        return typeof this.path === \"string\" ? _minpath__WEBPACK_IMPORTED_MODULE_2__.dirname(this.path) : undefined;\n    }\n    /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */ set dirname(dirname) {\n        assertPath(this.basename, \"dirname\");\n        this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(dirname || \"\", this.basename);\n    }\n    /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */ get extname() {\n        return typeof this.path === \"string\" ? _minpath__WEBPACK_IMPORTED_MODULE_2__.extname(this.path) : undefined;\n    }\n    /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */ set extname(extname) {\n        assertPart(extname, \"extname\");\n        assertPath(this.dirname, \"extname\");\n        if (extname) {\n            if (extname.codePointAt(0) !== 46 /* `.` */ ) {\n                throw new Error(\"`extname` must start with `.`\");\n            }\n            if (extname.includes(\".\", 1)) {\n                throw new Error(\"`extname` cannot contain multiple dots\");\n            }\n        }\n        this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname, this.stem + (extname || \"\"));\n    }\n    /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */ get path() {\n        return this.history[this.history.length - 1];\n    }\n    /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */ set path(path) {\n        if ((0,_minurl__WEBPACK_IMPORTED_MODULE_0__.isUrl)(path)) {\n            path = (0,_minurl__WEBPACK_IMPORTED_MODULE_3__.fileURLToPath)(path);\n        }\n        assertNonEmpty(path, \"path\");\n        if (this.path !== path) {\n            this.history.push(path);\n        }\n    }\n    /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */ get stem() {\n        return typeof this.path === \"string\" ? _minpath__WEBPACK_IMPORTED_MODULE_2__.basename(this.path, this.extname) : undefined;\n    }\n    /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */ set stem(stem) {\n        assertNonEmpty(stem, \"stem\");\n        assertPart(stem, \"stem\");\n        this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname || \"\", stem + (this.extname || \"\"));\n    }\n    // Normal prototypal methods.\n    /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */ fail(causeOrReason, optionsOrParentOrPlace, origin) {\n        // @ts-expect-error: the overloads are fine.\n        const message = this.message(causeOrReason, optionsOrParentOrPlace, origin);\n        message.fatal = true;\n        throw message;\n    }\n    /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */ info(causeOrReason, optionsOrParentOrPlace, origin) {\n        // @ts-expect-error: the overloads are fine.\n        const message = this.message(causeOrReason, optionsOrParentOrPlace, origin);\n        message.fatal = undefined;\n        return message;\n    }\n    /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */ message(causeOrReason, optionsOrParentOrPlace, origin) {\n        const message = new vfile_message__WEBPACK_IMPORTED_MODULE_4__.VFileMessage(// @ts-expect-error: the overloads are fine.\n        causeOrReason, optionsOrParentOrPlace, origin);\n        if (this.path) {\n            message.name = this.path + \":\" + message.name;\n            message.file = this.path;\n        }\n        message.fatal = false;\n        this.messages.push(message);\n        return message;\n    }\n    /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */ toString(encoding) {\n        if (this.value === undefined) {\n            return \"\";\n        }\n        if (typeof this.value === \"string\") {\n            return this.value;\n        }\n        const decoder = new TextDecoder(encoding || undefined);\n        return decoder.decode(this.value);\n    }\n}\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */ function assertPart(part, name) {\n    if (part && part.includes(_minpath__WEBPACK_IMPORTED_MODULE_2__.sep)) {\n        throw new Error(\"`\" + name + \"` cannot be a path: did not expect `\" + _minpath__WEBPACK_IMPORTED_MODULE_2__.sep + \"`\");\n    }\n}\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */ function assertNonEmpty(part, name) {\n    if (!part) {\n        throw new Error(\"`\" + name + \"` cannot be empty\");\n    }\n}\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */ function assertPath(path, name) {\n    if (!path) {\n        throw new Error(\"Setting `\" + name + \"` requires `path` to be set too\");\n    }\n}\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */ function isUint8Array(value) {\n    return Boolean(value && typeof value === \"object\" && \"byteLength\" in value && \"byteOffset\" in value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/minurl.shared.js":
/*!*************************************************!*\
  !*** ./node_modules/vfile/lib/minurl.shared.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: () => (/* binding */ isUrl)\n/* harmony export */ });\n/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */ // From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nfunction isUrl(fileUrlOrPath) {\n    return Boolean(fileUrlOrPath !== null && typeof fileUrlOrPath === \"object\" && \"href\" in fileUrlOrPath && fileUrlOrPath.href && \"protocol\" in fileUrlOrPath && fileUrlOrPath.protocol && // @ts-expect-error: indexing is fine.\n    fileUrlOrPath.auth === undefined);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/minurl.shared.js\n");

/***/ })

};
;