'use client'

import { useRef, useEffect, useState } from 'react'
import { Tree, NodeApi } from 'react-arborist'
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  FileText,
  Code2,
  Image,
  Settings,
  File as FileIcon
} from 'lucide-react'

interface TreeNode {
  id: string
  name: string
  type: 'file' | 'folder'
  children?: TreeNode[]
  file?: any
  path: string
}

interface CustomFileTreeProps {
  data: TreeNode[]
  onFileSelect?: (file: any) => void
  selectedPath?: string
}

function Node({ node, style, dragHandle }: { node: NodeApi<TreeNode>, style: any, dragHandle: any }) {
  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()

    switch (ext) {
      case 'js':
      case 'jsx':
        return <Code2 className="w-4 h-4 text-[#f7df1e]" />
      case 'ts':
      case 'tsx':
        return <Code2 className="w-4 h-4 text-[#3178c6]" />
      case 'html':
        return <Code2 className="w-4 h-4 text-[#e34c26]" />
      case 'css':
        return <Code2 className="w-4 h-4 text-[#1572b6]" />
      case 'json':
        return <Settings className="w-4 h-4 text-[#cbcb41]" />
      case 'md':
        return <FileText className="w-4 h-4 text-[#083fa1]" />
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <Image className="w-4 h-4 text-[#4caf50]" />
      default:
        return <FileIcon className="w-4 h-4 text-[#858585]" />
    }
  }

  return (
    <div
      style={style}
      ref={dragHandle}
      className={`flex items-center py-1 px-2 cursor-pointer hover:bg-[#161b22] transition-colors text-sm ${
        node.isSelected ? 'bg-[#21262d]' : ''
      }`}
      onClick={() => node.isInternal ? node.toggle() : node.select()}
    >
      {node.isInternal ? (
        <>
          <div className="w-4 h-4 flex items-center justify-center mr-1">
            {node.isOpen ? (
              <ChevronDown className="w-3 h-3 text-[#7d8590]" />
            ) : (
              <ChevronRight className="w-3 h-3 text-[#7d8590]" />
            )}
          </div>
          <div className="mr-2">
            {node.isOpen ? (
              <FolderOpen className="w-4 h-4 text-[#7d8590]" />
            ) : (
              <Folder className="w-4 h-4 text-[#7d8590]" />
            )}
          </div>
        </>
      ) : (
        <>
          <div className="w-4 mr-1" />
          <div className="mr-2">
            {getFileIcon(node.data.name)}
          </div>
        </>
      )}
      <span className="text-[#f0f6fc] truncate text-sm">
        {node.data.name}
      </span>
    </div>
  )
}

export function CustomFileTree({ data, onFileSelect, selectedPath }: CustomFileTreeProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [dimensions, setDimensions] = useState({ width: 300, height: 400 })

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect()
        setDimensions({
          width: Math.max(width, 200),
          height: Math.max(height, 200)
        })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)

    return () => window.removeEventListener('resize', updateDimensions)
  }, [])

  return (
    <div ref={containerRef} className="h-full bg-[#0d1117]">
      <Tree
        data={data}
        openByDefault={false}
        width={dimensions.width}
        height={dimensions.height}
        indent={16}
        rowHeight={28}
        onSelect={(nodes) => {
          const node = nodes[0]
          if (node && node.data.file && onFileSelect) {
            onFileSelect(node.data.file)
          }
        }}
      >
        {Node}
      </Tree>
    </div>
  )
}
