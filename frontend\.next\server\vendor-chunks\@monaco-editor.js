"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@monaco-editor";
exports.ids = ["vendor-chunks/@monaco-editor"];
exports.modules = {

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayLikeToArray: () => (/* binding */ _arrayLikeToArray),\n/* harmony export */   arrayWithHoles: () => (/* binding */ _arrayWithHoles),\n/* harmony export */   defineProperty: () => (/* binding */ _defineProperty),\n/* harmony export */   iterableToArrayLimit: () => (/* binding */ _iterableToArrayLimit),\n/* harmony export */   nonIterableRest: () => (/* binding */ _nonIterableRest),\n/* harmony export */   objectSpread2: () => (/* binding */ _objectSpread2),\n/* harmony export */   objectWithoutProperties: () => (/* binding */ _objectWithoutProperties),\n/* harmony export */   objectWithoutPropertiesLoose: () => (/* binding */ _objectWithoutPropertiesLoose),\n/* harmony export */   slicedToArray: () => (/* binding */ _slicedToArray),\n/* harmony export */   unsupportedIterableToArray: () => (/* binding */ _unsupportedIterableToArray)\n/* harmony export */ });\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        });\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread2(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        if (i % 2) {\n            ownKeys(Object(source), true).forEach(function(key) {\n                _defineProperty(target, key, source[key]);\n            });\n        } else if (Object.getOwnPropertyDescriptors) {\n            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n        } else {\n            ownKeys(Object(source)).forEach(function(key) {\n                Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n            });\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    if (typeof Symbol === \"undefined\" || !(Symbol.iterator in Object(arr))) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n    try {\n        for(var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/config/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar config = {\n    paths: {\n        vs: \"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs\"\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9jb25maWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLElBQUlBLFNBQVM7SUFDWEMsT0FBTztRQUNMQyxJQUFJO0lBQ047QUFDRjtBQUVBLGlFQUFlRixNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvY29uZmlnL2luZGV4LmpzPzM3ZmQiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGNvbmZpZyA9IHtcbiAgcGF0aHM6IHtcbiAgICB2czogJ2h0dHBzOi8vY2RuLmpzZGVsaXZyLm5ldC9ucG0vbW9uYWNvLWVkaXRvckAwLjUyLjIvbWluL3ZzJ1xuICB9XG59O1xuXG5leHBvcnQgZGVmYXVsdCBjb25maWc7XG4iXSwibmFtZXMiOlsiY29uZmlnIiwicGF0aHMiLCJ2cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _loader_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _loader_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loader/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvaW5kZXguanM/ZmZiNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbG9hZGVyIGZyb20gJy4vbG9hZGVyL2luZGV4LmpzJztcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL2xvYWRlci9pbmRleC5qcyc7XG4iXSwibmFtZXMiOlsibG9hZGVyIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/loader/index.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var state_local__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! state-local */ \"(ssr)/./node_modules/state-local/lib/es/state-local.js\");\n/* harmony import */ var _config_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/config/index.js\");\n/* harmony import */ var _validators_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validators/index.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\");\n/* harmony import */ var _utils_compose_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/compose.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\");\n/* harmony import */ var _utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/deepMerge.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\");\n/* harmony import */ var _utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/makeCancelable.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\");\n\n\n\n\n\n\n\n/** the local state of the module */ var _state$create = state_local__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    config: _config_index_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    isInitialized: false,\n    resolve: null,\n    reject: null,\n    monaco: null\n}), _state$create2 = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.slicedToArray)(_state$create, 2), getState = _state$create2[0], setState = _state$create2[1];\n/**\n * set the loader configuration\n * @param {Object} config - the configuration object\n */ function config(globalConfig) {\n    var _validators$config = _validators_index_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"].config(globalConfig), monaco = _validators$config.monaco, config = (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectWithoutProperties)(_validators$config, [\n        \"monaco\"\n    ]);\n    setState(function(state) {\n        return {\n            config: (0,_utils_deepMerge_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(state.config, config),\n            monaco: monaco\n        };\n    });\n}\n/**\n * handles the initialization of the monaco-editor\n * @return {Promise} - returns an instance of monaco (with a cancelable promise)\n */ function init() {\n    var state = getState(function(_ref) {\n        var monaco = _ref.monaco, isInitialized = _ref.isInitialized, resolve = _ref.resolve;\n        return {\n            monaco: monaco,\n            isInitialized: isInitialized,\n            resolve: resolve\n        };\n    });\n    if (!state.isInitialized) {\n        setState({\n            isInitialized: true\n        });\n        if (state.monaco) {\n            state.resolve(state.monaco);\n            return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n        }\n        if (window.monaco && window.monaco.editor) {\n            storeMonacoInstance(window.monaco);\n            state.resolve(window.monaco);\n            return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n        }\n        (0,_utils_compose_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(injectScripts, getMonacoLoaderScript)(configureLoader);\n    }\n    return (0,_utils_makeCancelable_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(wrapperPromise);\n}\n/**\n * injects provided scripts into the document.body\n * @param {Object} script - an HTML script element\n * @return {Object} - the injected HTML script element\n */ function injectScripts(script) {\n    return document.body.appendChild(script);\n}\n/**\n * creates an HTML script element with/without provided src\n * @param {string} [src] - the source path of the script\n * @return {Object} - the created HTML script element\n */ function createScript(src) {\n    var script = document.createElement(\"script\");\n    return src && (script.src = src), script;\n}\n/**\n * creates an HTML script element with the monaco loader src\n * @return {Object} - the created HTML script element\n */ function getMonacoLoaderScript(configureLoader) {\n    var state = getState(function(_ref2) {\n        var config = _ref2.config, reject = _ref2.reject;\n        return {\n            config: config,\n            reject: reject\n        };\n    });\n    var loaderScript = createScript(\"\".concat(state.config.paths.vs, \"/loader.js\"));\n    loaderScript.onload = function() {\n        return configureLoader();\n    };\n    loaderScript.onerror = state.reject;\n    return loaderScript;\n}\n/**\n * configures the monaco loader\n */ function configureLoader() {\n    var state = getState(function(_ref3) {\n        var config = _ref3.config, resolve = _ref3.resolve, reject = _ref3.reject;\n        return {\n            config: config,\n            resolve: resolve,\n            reject: reject\n        };\n    });\n    var require = window.require;\n    require.config(state.config);\n    require([\n        \"vs/editor/editor.main\"\n    ], function(monaco) {\n        storeMonacoInstance(monaco);\n        state.resolve(monaco);\n    }, function(error) {\n        state.reject(error);\n    });\n}\n/**\n * store monaco instance in local state\n */ function storeMonacoInstance(monaco) {\n    if (!getState().monaco) {\n        setState({\n            monaco: monaco\n        });\n    }\n}\n/**\n * internal helper function\n * extracts stored monaco instance\n * @return {Object|null} - the monaco instance\n */ function __getMonacoInstance() {\n    return getState(function(_ref4) {\n        var monaco = _ref4.monaco;\n        return monaco;\n    });\n}\nvar wrapperPromise = new Promise(function(resolve, reject) {\n    return setState({\n        resolve: resolve,\n        reject: reject\n    });\n});\nvar loader = {\n    config: config,\n    init: init,\n    __getMonacoInstance: __getMonacoInstance\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/loader/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js":
/*!********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/compose.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar compose = function compose() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(x) {\n        return fns.reduceRight(function(y, f) {\n            return f(y);\n        }, x);\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (compose);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxJQUFJQSxVQUFVLFNBQVNBO0lBQ3JCLElBQUssSUFBSUMsT0FBT0MsVUFBVUMsTUFBTSxFQUFFQyxNQUFNLElBQUlDLE1BQU1KLE9BQU9LLE9BQU8sR0FBR0EsT0FBT0wsTUFBTUssT0FBUTtRQUN0RkYsR0FBRyxDQUFDRSxLQUFLLEdBQUdKLFNBQVMsQ0FBQ0ksS0FBSztJQUM3QjtJQUVBLE9BQU8sU0FBVUMsQ0FBQztRQUNoQixPQUFPSCxJQUFJSSxXQUFXLENBQUMsU0FBVUMsQ0FBQyxFQUFFQyxDQUFDO1lBQ25DLE9BQU9BLEVBQUVEO1FBQ1gsR0FBR0Y7SUFDTDtBQUNGO0FBRUEsaUVBQWVQLE9BQU9BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jb21wb3NlLmpzP2E2MWMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGNvbXBvc2UgPSBmdW5jdGlvbiBjb21wb3NlKCkge1xuICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgZm5zID0gbmV3IEFycmF5KF9sZW4pLCBfa2V5ID0gMDsgX2tleSA8IF9sZW47IF9rZXkrKykge1xuICAgIGZuc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgfVxuXG4gIHJldHVybiBmdW5jdGlvbiAoeCkge1xuICAgIHJldHVybiBmbnMucmVkdWNlUmlnaHQoZnVuY3Rpb24gKHksIGYpIHtcbiAgICAgIHJldHVybiBmKHkpO1xuICAgIH0sIHgpO1xuICB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgY29tcG9zZTtcbiJdLCJuYW1lcyI6WyJjb21wb3NlIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImZucyIsIkFycmF5IiwiX2tleSIsIngiLCJyZWR1Y2VSaWdodCIsInkiLCJmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/compose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js":
/*!******************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/curry.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction curry(fn) {\n    return function curried() {\n        var _this = this;\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return args.length >= fn.length ? fn.apply(this, args) : function() {\n            for(var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){\n                nextArgs[_key2] = arguments[_key2];\n            }\n            return curried.apply(_this, [].concat(args, nextArgs));\n        };\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (curry);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsTUFBTUMsRUFBRTtJQUNmLE9BQU8sU0FBU0M7UUFDZCxJQUFJQyxRQUFRLElBQUk7UUFFaEIsSUFBSyxJQUFJQyxPQUFPQyxVQUFVQyxNQUFNLEVBQUVDLE9BQU8sSUFBSUMsTUFBTUosT0FBT0ssT0FBTyxHQUFHQSxPQUFPTCxNQUFNSyxPQUFRO1lBQ3ZGRixJQUFJLENBQUNFLEtBQUssR0FBR0osU0FBUyxDQUFDSSxLQUFLO1FBQzlCO1FBRUEsT0FBT0YsS0FBS0QsTUFBTSxJQUFJTCxHQUFHSyxNQUFNLEdBQUdMLEdBQUdTLEtBQUssQ0FBQyxJQUFJLEVBQUVILFFBQVE7WUFDdkQsSUFBSyxJQUFJSSxRQUFRTixVQUFVQyxNQUFNLEVBQUVNLFdBQVcsSUFBSUosTUFBTUcsUUFBUUUsUUFBUSxHQUFHQSxRQUFRRixPQUFPRSxRQUFTO2dCQUNqR0QsUUFBUSxDQUFDQyxNQUFNLEdBQUdSLFNBQVMsQ0FBQ1EsTUFBTTtZQUNwQztZQUVBLE9BQU9YLFFBQVFRLEtBQUssQ0FBQ1AsT0FBTyxFQUFFLENBQUNXLE1BQU0sQ0FBQ1AsTUFBTUs7UUFDOUM7SUFDRjtBQUNGO0FBRUEsaUVBQWVaLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9jdXJyeS5qcz8xMjVmIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGN1cnJ5KGZuKSB7XG4gIHJldHVybiBmdW5jdGlvbiBjdXJyaWVkKCkge1xuICAgIHZhciBfdGhpcyA9IHRoaXM7XG5cbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgfVxuXG4gICAgcmV0dXJuIGFyZ3MubGVuZ3RoID49IGZuLmxlbmd0aCA/IGZuLmFwcGx5KHRoaXMsIGFyZ3MpIDogZnVuY3Rpb24gKCkge1xuICAgICAgZm9yICh2YXIgX2xlbjIgPSBhcmd1bWVudHMubGVuZ3RoLCBuZXh0QXJncyA9IG5ldyBBcnJheShfbGVuMiksIF9rZXkyID0gMDsgX2tleTIgPCBfbGVuMjsgX2tleTIrKykge1xuICAgICAgICBuZXh0QXJnc1tfa2V5Ml0gPSBhcmd1bWVudHNbX2tleTJdO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gY3VycmllZC5hcHBseShfdGhpcywgW10uY29uY2F0KGFyZ3MsIG5leHRBcmdzKSk7XG4gICAgfTtcbiAgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgY3Vycnk7XG4iXSwibmFtZXMiOlsiY3VycnkiLCJmbiIsImN1cnJpZWQiLCJfdGhpcyIsIl9sZW4iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJhcmdzIiwiQXJyYXkiLCJfa2V5IiwiYXBwbHkiLCJfbGVuMiIsIm5leHRBcmdzIiwiX2tleTIiLCJjb25jYXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/_virtual/_rollupPluginBabelHelpers.js\");\n\nfunction merge(target, source) {\n    Object.keys(source).forEach(function(key) {\n        if (source[key] instanceof Object) {\n            if (target[key]) {\n                Object.assign(source[key], merge(target[key], source[key]));\n            }\n        }\n    });\n    return (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)((0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_0__.objectSpread2)({}, target), source);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (merge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkY7QUFFM0YsU0FBU0UsTUFBTUMsTUFBTSxFQUFFQyxNQUFNO0lBQzNCQyxPQUFPQyxJQUFJLENBQUNGLFFBQVFHLE9BQU8sQ0FBQyxTQUFVQyxHQUFHO1FBQ3ZDLElBQUlKLE1BQU0sQ0FBQ0ksSUFBSSxZQUFZSCxRQUFRO1lBQ2pDLElBQUlGLE1BQU0sQ0FBQ0ssSUFBSSxFQUFFO2dCQUNmSCxPQUFPSSxNQUFNLENBQUNMLE1BQU0sQ0FBQ0ksSUFBSSxFQUFFTixNQUFNQyxNQUFNLENBQUNLLElBQUksRUFBRUosTUFBTSxDQUFDSSxJQUFJO1lBQzNEO1FBQ0Y7SUFDRjtJQUNBLE9BQU9QLG1GQUFjQSxDQUFDQSxtRkFBY0EsQ0FBQyxDQUFDLEdBQUdFLFNBQVNDO0FBQ3BEO0FBRUEsaUVBQWVGLEtBQUtBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2RvcmEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9kZWVwTWVyZ2UuanM/YjZjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBvYmplY3RTcHJlYWQyIGFzIF9vYmplY3RTcHJlYWQyIH0gZnJvbSAnLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyc7XG5cbmZ1bmN0aW9uIG1lcmdlKHRhcmdldCwgc291cmNlKSB7XG4gIE9iamVjdC5rZXlzKHNvdXJjZSkuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7XG4gICAgaWYgKHNvdXJjZVtrZXldIGluc3RhbmNlb2YgT2JqZWN0KSB7XG4gICAgICBpZiAodGFyZ2V0W2tleV0pIHtcbiAgICAgICAgT2JqZWN0LmFzc2lnbihzb3VyY2Vba2V5XSwgbWVyZ2UodGFyZ2V0W2tleV0sIHNvdXJjZVtrZXldKSk7XG4gICAgICB9XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIF9vYmplY3RTcHJlYWQyKF9vYmplY3RTcHJlYWQyKHt9LCB0YXJnZXQpLCBzb3VyY2UpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBtZXJnZTtcbiJdLCJuYW1lcyI6WyJvYmplY3RTcHJlYWQyIiwiX29iamVjdFNwcmVhZDIiLCJtZXJnZSIsInRhcmdldCIsInNvdXJjZSIsIk9iamVjdCIsImtleXMiLCJmb3JFYWNoIiwia2V5IiwiYXNzaWduIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction isObject(value) {\n    return ({}).toString.call(value).includes(\"Object\");\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1vbmFjby1lZGl0b3IvbG9hZGVyL2xpYi9lcy91dGlscy9pc09iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsU0FBU0EsU0FBU0MsS0FBSztJQUNyQixPQUFPLEVBQUMsR0FBRUMsUUFBUSxDQUFDQyxJQUFJLENBQUNGLE9BQU9HLFFBQVEsQ0FBQztBQUMxQztBQUVBLGlFQUFlSixRQUFRQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29kb3JhLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0Btb25hY28tZWRpdG9yL2xvYWRlci9saWIvZXMvdXRpbHMvaXNPYmplY3QuanM/NGE2ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc09iamVjdCh2YWx1ZSkge1xuICByZXR1cm4ge30udG9TdHJpbmcuY2FsbCh2YWx1ZSkuaW5jbHVkZXMoJ09iamVjdCcpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBpc09iamVjdDtcbiJdLCJuYW1lcyI6WyJpc09iamVjdCIsInZhbHVlIiwidG9TdHJpbmciLCJjYWxsIiwiaW5jbHVkZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CANCELATION_MESSAGE: () => (/* binding */ CANCELATION_MESSAGE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// The source (has been changed) is https://github.com/facebook/react/issues/5465#issuecomment-157888325\nvar CANCELATION_MESSAGE = {\n    type: \"cancelation\",\n    msg: \"operation is manually canceled\"\n};\nfunction makeCancelable(promise) {\n    var hasCanceled_ = false;\n    var wrappedPromise = new Promise(function(resolve, reject) {\n        promise.then(function(val) {\n            return hasCanceled_ ? reject(CANCELATION_MESSAGE) : resolve(val);\n        });\n        promise[\"catch\"](reject);\n    });\n    return wrappedPromise.cancel = function() {\n        return hasCanceled_ = true;\n    }, wrappedPromise;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (makeCancelable);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/makeCancelable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@monaco-editor/loader/lib/es/validators/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   errorHandler: () => (/* binding */ errorHandler),\n/* harmony export */   errorMessages: () => (/* binding */ errorMessages)\n/* harmony export */ });\n/* harmony import */ var _utils_curry_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/curry.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/curry.js\");\n/* harmony import */ var _utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/isObject.js */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/utils/isObject.js\");\n\n\n/**\n * validates the configuration object and informs about deprecation\n * @param {Object} config - the configuration object \n * @return {Object} config - the validated configuration object\n */ function validateConfig(config) {\n    if (!config) errorHandler(\"configIsRequired\");\n    if (!(0,_utils_isObject_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(config)) errorHandler(\"configType\");\n    if (config.urls) {\n        informAboutDeprecation();\n        return {\n            paths: {\n                vs: config.urls.monacoBase\n            }\n        };\n    }\n    return config;\n}\n/**\n * logs deprecation message\n */ function informAboutDeprecation() {\n    console.warn(errorMessages.deprecation);\n}\nfunction throwError(errorMessages, type) {\n    throw new Error(errorMessages[type] || errorMessages[\"default\"]);\n}\nvar errorMessages = {\n    configIsRequired: \"the configuration object is required\",\n    configType: \"the configuration object should be an object\",\n    \"default\": \"an unknown error accured in `@monaco-editor/loader` package\",\n    deprecation: \"Deprecation warning!\\n    You are using deprecated way of configuration.\\n\\n    Instead of using\\n      monaco.config({ urls: { monacoBase: '...' } })\\n    use\\n      monaco.config({ paths: { vs: '...' } })\\n\\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\\n  \"\n};\nvar errorHandler = (0,_utils_curry_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(throwError)(errorMessages);\nvar validators = {\n    config: validateConfig\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validators);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/loader/lib/es/validators/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@monaco-editor/react/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiffEditor: () => (/* binding */ we),\n/* harmony export */   Editor: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (/* binding */ Ft),\n/* harmony export */   loader: () => (/* reexport safe */ _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useMonaco: () => (/* binding */ Le)\n/* harmony export */ });\n/* harmony import */ var _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @monaco-editor/loader */ \"(ssr)/./node_modules/@monaco-editor/loader/lib/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\nvar le = {\n    wrapper: {\n        display: \"flex\",\n        position: \"relative\",\n        textAlign: \"initial\"\n    },\n    fullWidth: {\n        width: \"100%\"\n    },\n    hide: {\n        display: \"none\"\n    }\n}, v = le;\n\nvar ae = {\n    container: {\n        display: \"flex\",\n        height: \"100%\",\n        width: \"100%\",\n        justifyContent: \"center\",\n        alignItems: \"center\"\n    }\n}, Y = ae;\nfunction Me({ children: e }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        style: Y.container\n    }, e);\n}\nvar Z = Me;\nvar $ = Z;\nfunction Ee({ width: e, height: r, isEditorReady: n, loading: t, _ref: a, className: m, wrapperProps: E }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"section\", {\n        style: {\n            ...v.wrapper,\n            width: e,\n            height: r\n        },\n        ...E\n    }, !n && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement($, null, t), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: a,\n        style: {\n            ...v.fullWidth,\n            ...!n && v.hide\n        },\n        className: m\n    }));\n}\nvar ee = Ee;\nvar H = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ee);\n\nfunction Ce(e) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(e, []);\n}\nvar k = Ce;\n\nfunction he(e, r, n = !0) {\n    let t = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(t.current || !n ? ()=>{\n        t.current = !1;\n    } : e, r);\n}\nvar l = he;\nfunction D() {}\nfunction h(e, r, n, t) {\n    return De(e, t) || be(e, r, n, t);\n}\nfunction De(e, r) {\n    return e.editor.getModel(te(e, r));\n}\nfunction be(e, r, n, t) {\n    return e.editor.createModel(r, n, t ? te(e, t) : void 0);\n}\nfunction te(e, r) {\n    return e.Uri.parse(r);\n}\nfunction Oe({ original: e, modified: r, language: n, originalLanguage: t, modifiedLanguage: a, originalModelPath: m, modifiedModelPath: E, keepCurrentOriginalModel: g = !1, keepCurrentModifiedModel: N = !1, theme: x = \"light\", loading: P = \"Loading...\", options: y = {}, height: V = \"100%\", width: z = \"100%\", className: F, wrapperProps: j = {}, beforeMount: A = D, onMount: q = D }) {\n    let [M, O] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [T, s] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0), u = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), c = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), w = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q), o = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(A), b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);\n    k(()=>{\n        let i = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();\n        return i.then((f)=>(c.current = f) && s(!1)).catch((f)=>f?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", f)), ()=>u.current ? I() : i.cancel();\n    }), l(()=>{\n        if (u.current && c.current) {\n            let i = u.current.getOriginalEditor(), f = h(c.current, e || \"\", t || n || \"text\", m || \"\");\n            f !== i.getModel() && i.setModel(f);\n        }\n    }, [\n        m\n    ], M), l(()=>{\n        if (u.current && c.current) {\n            let i = u.current.getModifiedEditor(), f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n            f !== i.getModel() && i.setModel(f);\n        }\n    }, [\n        E\n    ], M), l(()=>{\n        let i = u.current.getModifiedEditor();\n        i.getOption(c.current.editor.EditorOption.readOnly) ? i.setValue(r || \"\") : r !== i.getValue() && (i.executeEdits(\"\", [\n            {\n                range: i.getModel().getFullModelRange(),\n                text: r || \"\",\n                forceMoveMarkers: !0\n            }\n        ]), i.pushUndoStop());\n    }, [\n        r\n    ], M), l(()=>{\n        u.current?.getModel()?.original.setValue(e || \"\");\n    }, [\n        e\n    ], M), l(()=>{\n        let { original: i, modified: f } = u.current.getModel();\n        c.current.editor.setModelLanguage(i, t || n || \"text\"), c.current.editor.setModelLanguage(f, a || n || \"text\");\n    }, [\n        n,\n        t,\n        a\n    ], M), l(()=>{\n        c.current?.editor.setTheme(x);\n    }, [\n        x\n    ], M), l(()=>{\n        u.current?.updateOptions(y);\n    }, [\n        y\n    ], M);\n    let L = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!c.current) return;\n        o.current(c.current);\n        let i = h(c.current, e || \"\", t || n || \"text\", m || \"\"), f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n        u.current?.setModel({\n            original: i,\n            modified: f\n        });\n    }, [\n        n,\n        r,\n        a,\n        e,\n        t,\n        m,\n        E\n    ]), U = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        !b.current && w.current && (u.current = c.current.editor.createDiffEditor(w.current, {\n            automaticLayout: !0,\n            ...y\n        }), L(), c.current?.editor.setTheme(x), O(!0), b.current = !0);\n    }, [\n        y,\n        x,\n        L\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        M && d.current(u.current, c.current);\n    }, [\n        M\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        !T && !M && U();\n    }, [\n        T,\n        M,\n        U\n    ]);\n    function I() {\n        let i = u.current?.getModel();\n        g || i?.original?.dispose(), N || i?.modified?.dispose(), u.current?.dispose();\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(H, {\n        width: z,\n        height: V,\n        isEditorReady: M,\n        loading: P,\n        _ref: w,\n        className: F,\n        wrapperProps: j\n    });\n}\nvar ie = Oe;\nvar we = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(ie);\n\n\nfunction Pe() {\n    let [e, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__getMonacoInstance());\n    return k(()=>{\n        let n;\n        return e || (n = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(), n.then((t)=>{\n            r(t);\n        })), ()=>n?.cancel();\n    }), e;\n}\nvar Le = Pe;\n\n\n\n\nfunction He(e) {\n    let r = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        r.current = e;\n    }, [\n        e\n    ]), r.current;\n}\nvar se = He;\nvar _ = new Map;\nfunction Ve({ defaultValue: e, defaultLanguage: r, defaultPath: n, value: t, language: a, path: m, theme: E = \"light\", line: g, loading: N = \"Loading...\", options: x = {}, overrideServices: P = {}, saveViewState: y = !0, keepCurrentModel: V = !1, width: z = \"100%\", height: F = \"100%\", className: j, wrapperProps: A = {}, beforeMount: q = D, onMount: M = D, onChange: O, onValidate: T = D }) {\n    let [s, u] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [c, w] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!0), d = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), o = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), b = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), L = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(M), U = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(q), I = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(), i = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(t), f = se(m), Q = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1), B = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1);\n    k(()=>{\n        let p = _monaco_editor_loader__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init();\n        return p.then((R)=>(d.current = R) && w(!1)).catch((R)=>R?.type !== \"cancelation\" && console.error(\"Monaco initialization: error:\", R)), ()=>o.current ? pe() : p.cancel();\n    }), l(()=>{\n        let p = h(d.current, e || t || \"\", r || a || \"\", m || n || \"\");\n        p !== o.current?.getModel() && (y && _.set(f, o.current?.saveViewState()), o.current?.setModel(p), y && o.current?.restoreViewState(_.get(m)));\n    }, [\n        m\n    ], s), l(()=>{\n        o.current?.updateOptions(x);\n    }, [\n        x\n    ], s), l(()=>{\n        !o.current || t === void 0 || (o.current.getOption(d.current.editor.EditorOption.readOnly) ? o.current.setValue(t) : t !== o.current.getValue() && (B.current = !0, o.current.executeEdits(\"\", [\n            {\n                range: o.current.getModel().getFullModelRange(),\n                text: t,\n                forceMoveMarkers: !0\n            }\n        ]), o.current.pushUndoStop(), B.current = !1));\n    }, [\n        t\n    ], s), l(()=>{\n        let p = o.current?.getModel();\n        p && a && d.current?.editor.setModelLanguage(p, a);\n    }, [\n        a\n    ], s), l(()=>{\n        g !== void 0 && o.current?.revealLine(g);\n    }, [\n        g\n    ], s), l(()=>{\n        d.current?.editor.setTheme(E);\n    }, [\n        E\n    ], s);\n    let X = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!(!b.current || !d.current) && !Q.current) {\n            U.current(d.current);\n            let p = m || n, R = h(d.current, t || e || \"\", r || a || \"\", p || \"\");\n            o.current = d.current?.editor.create(b.current, {\n                model: R,\n                automaticLayout: !0,\n                ...x\n            }, P), y && o.current.restoreViewState(_.get(p)), d.current.editor.setTheme(E), g !== void 0 && o.current.revealLine(g), u(!0), Q.current = !0;\n        }\n    }, [\n        e,\n        r,\n        n,\n        t,\n        a,\n        m,\n        x,\n        P,\n        y,\n        E,\n        g\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        s && L.current(o.current, d.current);\n    }, [\n        s\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        !c && !s && X();\n    }, [\n        c,\n        s,\n        X\n    ]), i.current = t, (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        s && O && (I.current?.dispose(), I.current = o.current?.onDidChangeModelContent((p)=>{\n            B.current || O(o.current.getValue(), p);\n        }));\n    }, [\n        s,\n        O\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (s) {\n            let p = d.current.editor.onDidChangeMarkers((R)=>{\n                let G = o.current.getModel()?.uri;\n                if (G && R.find((J)=>J.path === G.path)) {\n                    let J = d.current.editor.getModelMarkers({\n                        resource: G\n                    });\n                    T?.(J);\n                }\n            });\n            return ()=>{\n                p?.dispose();\n            };\n        }\n        return ()=>{};\n    }, [\n        s,\n        T\n    ]);\n    function pe() {\n        I.current?.dispose(), V ? y && _.set(m, o.current.saveViewState()) : o.current.getModel()?.dispose(), o.current.dispose();\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(H, {\n        width: z,\n        height: F,\n        isEditorReady: s,\n        loading: N,\n        _ref: b,\n        className: j,\n        wrapperProps: A\n    });\n}\nvar fe = Ve;\nvar de = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(fe);\nvar Ft = de;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@monaco-editor/react/dist/index.mjs\n");

/***/ })

};
;