"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-highlight";
exports.ids = ["vendor-chunks/rehype-highlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-highlight/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/rehype-highlight/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeHighlight)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/common.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/**\n * @import {ElementContent, Element, Root} from 'hast'\n * @import {LanguageFn} from 'lowlight'\n * @import {VFile} from 'vfile'\n */ /**\n * @typedef Options\n *   Configuration (optional).\n * @property {Readonly<Record<string, ReadonlyArray<string> | string>> | null | undefined} [aliases={}]\n *   Register more aliases (optional);\n *   passed to `lowlight.registerAlias`.\n * @property {boolean | null | undefined} [detect=false]\n *   Highlight code without language classes by guessing its programming\n *   language (default: `false`).\n * @property {Readonly<Record<string, LanguageFn>> | null | undefined} [languages]\n *   Register languages (default: `common`);\n *   passed to `lowlight.register`.\n * @property {ReadonlyArray<string> | null | undefined} [plainText=[]]\n *   List of language names to not highlight (optional);\n *   note you can also add `no-highlight` classes.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   Names of languages to check when detecting (default: all registered\n *   languages).\n */ \n\n\n/** @type {Options} */ const emptyOptions = {};\n/**\n * Apply syntax highlighting.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */ function rehypeHighlight(options) {\n    const settings = options || emptyOptions;\n    const aliases = settings.aliases;\n    const detect = settings.detect || false;\n    const languages = settings.languages || lowlight__WEBPACK_IMPORTED_MODULE_0__.grammars;\n    const plainText = settings.plainText;\n    const prefix = settings.prefix;\n    const subset = settings.subset;\n    let name = \"hljs\";\n    const lowlight = (0,lowlight__WEBPACK_IMPORTED_MODULE_1__.createLowlight)(languages);\n    if (aliases) {\n        lowlight.registerAlias(aliases);\n    }\n    if (prefix) {\n        const pos = prefix.indexOf(\"-\");\n        name = pos === -1 ? prefix : prefix.slice(0, pos);\n    }\n    /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */ return function(tree, file) {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(tree, \"element\", function(node, _, parent) {\n            if (node.tagName !== \"code\" || !parent || parent.type !== \"element\" || parent.tagName !== \"pre\") {\n                return;\n            }\n            const lang = language(node);\n            if (lang === false || !lang && !detect || lang && plainText && plainText.includes(lang)) {\n                return;\n            }\n            if (!Array.isArray(node.properties.className)) {\n                node.properties.className = [];\n            }\n            if (!node.properties.className.includes(name)) {\n                node.properties.className.unshift(name);\n            }\n            const text = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node, {\n                whitespace: \"pre\"\n            });\n            /** @type {Root} */ let result;\n            try {\n                result = lang ? lowlight.highlight(lang, text, {\n                    prefix\n                }) : lowlight.highlightAuto(text, {\n                    prefix,\n                    subset\n                });\n            } catch (error) {\n                const cause = /** @type {Error} */ error;\n                if (lang && /Unknown language/.test(cause.message)) {\n                    file.message(\"Cannot highlight as `\" + lang + \"`, it’s not registered\", {\n                        ancestors: [\n                            parent,\n                            node\n                        ],\n                        cause,\n                        place: node.position,\n                        ruleId: \"missing-language\",\n                        source: \"rehype-highlight\"\n                    });\n                    /* c8 ignore next 5 -- throw arbitrary hljs errors */ return;\n                }\n                throw cause;\n            }\n            if (!lang && result.data && result.data.language) {\n                node.properties.className.push(\"language-\" + result.data.language);\n            }\n            if (result.children.length > 0) {\n                node.children = /** @type {Array<ElementContent>} */ result.children;\n            }\n        });\n    };\n}\n/**\n * Get the programming language of `node`.\n *\n * @param {Element} node\n *   Node.\n * @returns {false | string | undefined}\n *   Language or `undefined`, or `false` when an explikcit `no-highlight` class\n *   is used.\n */ function language(node) {\n    const list = node.properties.className;\n    let index = -1;\n    if (!Array.isArray(list)) {\n        return;\n    }\n    /** @type {string | undefined} */ let name;\n    while(++index < list.length){\n        const value = String(list[index]);\n        if (value === \"no-highlight\" || value === \"nohighlight\") {\n            return false;\n        }\n        if (!name && value.slice(0, 5) === \"lang-\") {\n            name = value.slice(5);\n        }\n        if (!name && value.slice(0, 9) === \"language-\") {\n            name = value.slice(9);\n        }\n    }\n    return name;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-highlight/lib/index.js\n");

/***/ })

};
;