'use client'

import { useState, useEffect } from 'react'
import { chatAPI } from '@/lib/api'
import { Loader2, Globe, AlertCircle, RefreshCw } from 'lucide-react'

export function WebsitePreview({ version }) {
  const [previewUrl, setPreviewUrl] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (version) {
      loadPreview()
    }
  }, [version])

  const loadPreview = async () => {
    if (!version) return

    setLoading(true)
    setError(null)
    
    try {
      const response = await chatAPI.getVersionPreview(version.id)
      setPreviewUrl(response.preview_url)
    } catch (error) {
      console.error('Error loading preview:', error)
      setError('Failed to load preview')
    } finally {
      setLoading(false)
    }
  }

  if (!version) {
    return (
      <div className="h-full flex items-center justify-center bg-[#0d1117]">
        <div className="text-center">
          <Globe className="w-12 h-12 mx-auto mb-2 text-[#7d8590] opacity-50" />
          <p className="text-[#7d8590]">No version selected</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-[#0d1117]">
      {/* Header */}
      <div className="h-12 bg-[#161b22] border-b border-[#21262d] flex items-center justify-between px-4">
        <div className="flex items-center space-x-2">
          <Globe className="w-4 h-4 text-[#7d8590]" />
          <span className="text-[#f0f6fc] text-sm font-medium">Website Preview</span>
        </div>
        <button
          onClick={loadPreview}
          disabled={loading}
          className="p-1 text-[#7d8590] hover:text-[#f0f6fc] disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 relative">
        {loading ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-[#7d8590] mb-2" />
              <p className="text-[#7d8590]">Loading preview...</p>
            </div>
          </div>
        ) : error ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 mx-auto mb-2 text-[#f85149]" />
              <h3 className="text-lg font-medium text-[#f0f6fc] mb-2">Preview Error</h3>
              <p className="text-[#7d8590] mb-4">{error}</p>
              <button
                onClick={loadPreview}
                className="bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : previewUrl ? (
          <iframe
            src={previewUrl}
            className="w-full h-full border-0"
            title="Website Preview"
            sandbox="allow-scripts allow-same-origin allow-forms"
          />
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Globe className="w-12 h-12 mx-auto mb-2 text-[#7d8590] opacity-50" />
              <h3 className="text-lg font-medium text-[#f0f6fc] mb-2">No Preview Available</h3>
              <p className="text-[#7d8590] mb-4">This version doesn't have a preview yet.</p>
              <button
                onClick={loadPreview}
                className="bg-[#0969da] hover:bg-[#0860ca] text-white px-4 py-2 rounded-lg"
              >
                Refresh
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
