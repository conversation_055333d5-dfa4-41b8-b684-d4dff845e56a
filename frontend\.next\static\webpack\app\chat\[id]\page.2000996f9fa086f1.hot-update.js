"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/custom-file-tree.tsx":
/*!*****************************************!*\
  !*** ./components/custom-file-tree.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomFileTree: function() { return /* binding */ CustomFileTree; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_arborist__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-arborist */ \"(app-pages-browser)/./node_modules/react-arborist/dist/module/components/tree.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* __next_internal_client_entry_do_not_use__ CustomFileTree auto */ \n\n\nfunction Node(param) {\n    let { node, style, dragHandle } = param;\n    const getFileIcon = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#f7df1e]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case \"ts\":\n            case \"tsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#3178c6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case \"html\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#e34c26]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            case \"css\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-4 h-4 text-[#1572b6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n            case \"json\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#cbcb41]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case \"md\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-[#083fa1]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n            case \"gif\":\n            case \"svg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-[#4caf50]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-[#858585]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        ref: dragHandle,\n        className: \"flex items-center py-1 px-2 cursor-pointer hover:bg-[#2a2d2e] transition-colors text-sm \".concat(node.isSelected ? \"bg-[#37373d]\" : \"\"),\n        onClick: ()=>node.isInternal ? node.toggle() : node.select(),\n        children: [\n            node.isInternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 flex items-center justify-center mr-1\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-3 h-3 text-[#cccccc]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-3 h-3 text-[#cccccc]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4 text-[#dcb67a]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-[#dcb67a]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: getFileIcon(node.data.name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-[#cccccc] truncate font-mono\",\n                children: node.data.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            node.data.type === \"file\" && node.data.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-auto text-xs text-[#858585]\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = Node;\nfunction CustomFileTree(param) {\n    let { data, onFileSelect, selectedPath } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-[#252526]\",\n        style: {\n            height: \"100%\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_arborist__WEBPACK_IMPORTED_MODULE_10__.Tree, {\n            data: data,\n            openByDefault: false,\n            width: 300,\n            height: 600,\n            indent: 16,\n            rowHeight: 24,\n            onSelect: (nodes)=>{\n                const node = nodes[0];\n                if (node && node.data.file && onFileSelect) {\n                    onFileSelect(node.data.file);\n                }\n            },\n            children: Node\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CustomFileTree;\nvar _c, _c1;\n$RefreshReg$(_c, \"Node\");\n$RefreshReg$(_c1, \"CustomFileTree\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/custom-file-tree.tsx\n"));

/***/ })

});