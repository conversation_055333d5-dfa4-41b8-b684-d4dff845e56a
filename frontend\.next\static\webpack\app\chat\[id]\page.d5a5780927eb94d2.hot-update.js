"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[id]/page",{

/***/ "(app-pages-browser)/./components/custom-file-tree.tsx":
/*!*****************************************!*\
  !*** ./components/custom-file-tree.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomFileTree: function() { return /* binding */ CustomFileTree; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_arborist__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-arborist */ \"(app-pages-browser)/./node_modules/react-arborist/dist/module/components/tree.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Code2,File,FileText,Folder,FolderOpen,Image,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* __next_internal_client_entry_do_not_use__ CustomFileTree auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Node(param) {\n    let { node, style, dragHandle } = param;\n    const getFileIcon = (fileName)=>{\n        var _fileName_split_pop;\n        const ext = (_fileName_split_pop = fileName.split(\".\").pop()) === null || _fileName_split_pop === void 0 ? void 0 : _fileName_split_pop.toLowerCase();\n        switch(ext){\n            case \"js\":\n            case \"jsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#f7df1e]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 16\n                }, this);\n            case \"ts\":\n            case \"tsx\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#3178c6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 16\n                }, this);\n            case \"html\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#e34c26]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 16\n                }, this);\n            case \"css\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-[#1572b6]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 16\n                }, this);\n            case \"json\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-[#cbcb41]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 16\n                }, this);\n            case \"md\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-[#083fa1]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, this);\n            case \"png\":\n            case \"jpg\":\n            case \"jpeg\":\n            case \"gif\":\n            case \"svg\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-[#4caf50]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-[#858585]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: style,\n        ref: dragHandle,\n        className: \"flex items-center py-1 px-2 cursor-pointer hover:bg-[#161b22] transition-colors text-sm \".concat(node.isSelected ? \"bg-[#21262d]\" : \"\"),\n        onClick: ()=>node.isInternal ? node.toggle() : node.select(),\n        children: [\n            node.isInternal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 flex items-center justify-center mr-1\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-3 h-3 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-3 h-3 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: node.isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Code2_File_FileText_Folder_FolderOpen_Image_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-4 h-4 text-[#7d8590]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-2\",\n                        children: getFileIcon(node.data.name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-[#f0f6fc] truncate text-sm\",\n                children: node.data.name\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            node.data.type === \"file\" && node.data.file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-auto text-xs text-[#7d8590]\",\n                children: \"+5 / -5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_c = Node;\nfunction CustomFileTree(param) {\n    let { data, onFileSelect, selectedPath } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [dimensions, setDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 300,\n        height: 400\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateDimensions = ()=>{\n            if (containerRef.current) {\n                const { width, height } = containerRef.current.getBoundingClientRect();\n                setDimensions({\n                    width: Math.max(width, 200),\n                    height: Math.max(height, 200)\n                });\n            }\n        };\n        updateDimensions();\n        window.addEventListener(\"resize\", updateDimensions);\n        return ()=>window.removeEventListener(\"resize\", updateDimensions);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"h-full bg-[#252526]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_arborist__WEBPACK_IMPORTED_MODULE_11__.Tree, {\n            data: data,\n            openByDefault: false,\n            width: dimensions.width,\n            height: dimensions.height,\n            indent: 16,\n            rowHeight: 24,\n            onSelect: (nodes)=>{\n                const node = nodes[0];\n                if (node && node.data.file && onFileSelect) {\n                    onFileSelect(node.data.file);\n                }\n            },\n            children: Node\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Codora\\\\frontend\\\\components\\\\custom-file-tree.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomFileTree, \"VOBevdEWdqvQFqks1QON/ZYTZDc=\");\n_c1 = CustomFileTree;\nvar _c, _c1;\n$RefreshReg$(_c, \"Node\");\n$RefreshReg$(_c1, \"CustomFileTree\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/custom-file-tree.tsx\n"));

/***/ })

});